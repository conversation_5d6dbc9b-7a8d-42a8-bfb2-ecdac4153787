﻿function addToCart(productId) {
    let selectedOptions = [];

    document.querySelectorAll(".option-item.selected").forEach(option => {
        selectedOptions.push(parseInt(option.id, 10));
    });

    const allAttributesSelected = document.querySelectorAll(".atterFiled").length === selectedOptions.length;
    if (!allAttributesSelected) {
        Swal.fire({
            title: 'Error',
            text: 'Please select all attributes.',
            icon: 'error',
            confirmButtonText: 'OK'
        });
        return;
    }

    console.log(JSON.stringify({
        productId: parseInt(productId, 10),
        quantity: 1,
        userId: 3,
        //cartItemId: 0,
        selectedProductAttributeOptionIds: selectedOptions
    }),);
    $.ajax({
        url: '/Cart/AddItemToCart',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            productId: parseInt(productId, 10),
            quantity: 1,
            userId: 3,
            cartItemId: 0,
            selectedProductAttributeOptionIds: selectedOptions
        }),
        success: function (response) {
            if (response.isSuccess) {
                updateCart(); // Call function to refresh cart UI

                Swal.fire({
                    title: 'Added to Cart',
                    text: 'Product successfully added',
                    icon: 'success',
                    timer: 1000,
                    showConfirmButton: false
                });
            } else {
                Swal.fire({
                    title: 'Failure',
                    text: response.message || 'Failed to add the product to the cart.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            }
        },
        error: function (xhr) {
            console.error('Error:', xhr.responseText || xhr.statusText);
            Swal.fire({
                title: 'Error',
                text: 'An error occurred while adding the product to the cart. Please try again.',
                icon: 'error',
                confirmButtonText: 'OK'
            });
        }
    });
}




function updateCart() {
    $.ajax({
        url: "/Cart/GetMyCart",
        type: "GET",
        contentType: "application/json",
        data: { userId: 3 },
        success: function (result) {
            if (result.data && result.data.cartItems) {
                const cartItems = result.data.cartItems;

                if (typeof populateCartTable1 === "function") {
                    populateCartTable1(cartItems);
                }

                if (typeof populateCartTable === "function") {
                    populateCartTable(result.data);
                    updateCartTotal(result.data);
                }

                const totalItems = cartItems.reduce((sum, item) => sum + item.quantity, 0);
                $('.cart-count').text(totalItems);

            } else {
                console.warn("No cart items found.");
                $('.cart-count').text(0);
            }
        },
        error: function (error) {
            console.error("Error loading cart:", error);
        }
    });
}


function toggleWishlist(productId, el, userId) {
    if (userId === 0) {
        Swal.fire({
            title: 'Warning',
            text: 'You must log in to add the product to your favorites.',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Go to Login',
            cancelButtonText: 'Close'
        }).then((result) => {
            if (result.isConfirmed) {
                window.location.href = '/Account/SignIn';
            }
        });
        return;
    }
    const icon = el.querySelector("i");
    const isSelected = icon.classList.contains("icon-selected");
    const url = isSelected
        ? '/Wishlist/RemoveItemFromWishList'
        : '/Wishlist/AddItemToWishList';

    $.ajax({
        url: url,
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            productId: productId,
            userId: userId
        }),

        success: function (response) {
            if (response.isSuccess || response.data) {
                if (icon) {
                    icon.classList.toggle("icon-selected");
                }

                updateWishlist();
                Swal.fire({
                    title: isSelected ? 'Removed from Wishlist' : 'Added to Wishlist',
                    text: isSelected
                        ? 'The product has been removed from your wishlist.'
                        : 'The product has been added to your wishlist.',
                    icon: 'success',
                    timer: 1000, // Auto close after 2 seconds
                    showConfirmButton: false
                });
            } else {
                console.error('Operation failed:', response.message);
            }
        },
        error: function (xhr) {
            console.error('Error:', xhr.responseText || xhr.statusText);
            Swal.fire({
                title: 'Warning',
                text: 'You must log in to add the product to your favorites.',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'Go to Login',
                cancelButtonText: 'Close'
            }).then((result) => {
                if (result.isConfirmed) {
                    window.location.href = '/Account/SignIn';
                }
            });
        }
    });
}


function updateWishlist() {


    $.ajax({
        url: `/Wishlist/GetMyWishList`,
        type: "GET",
        dataType: "json",
        success: function (response) {

            if (response && response.isSuccess && response.data) {
                populateWishlist(response.data);
            } else {
                console.error('Failed to load wishlist or no items found.');
            }
        },
        error: function (error) {

        }
    });
}

function removeFromWishlist(productId) {
    $.ajax({

        url: "/Wishlist/RemoveItemFromWishList",
        type: "POST",
        contentType: "application/json",
        data: JSON.stringify({
            productId: productId,

        }), success: function (response) {
            Swal.fire({
                title: 'Removed',
                text: 'Product removed from wishlist!',
                icon: 'success',
                timer: 1000,
                showConfirmButton: false
            });
            updateWishlist();

        },
        error: function (error) {
            console.error("Error removing item:", error);
            Swal.fire({
                title: 'Error',
                text: 'Could not remove item. Please try again.',
                icon: 'error',
                confirmButtonText: 'OK'
            });
        }
    });
}



function populateWishlist(wishlistItem) {
    const wishlistContainer = $('#wishlistContainer');
    wishlistContainer.empty();

    wishlistItem.forEach(item => {

        const rowHtml = `
            <tr>
                <td class="product-remove col-lg-2 ">
                    <a href="#" class="remove-wishlist" onclick="removeFromWishlist(${item.productId})">
                        <i class="fal fa-times"></i>
                    </a>
                </td>
                <td class="product-thumbnail col-lg-4 " style="text-align:center!important">
                    <a href="/Products/ProductDetails/${item.productId}">
                        <img   src="${baseUrl + item.itemImage || '~/images/product/electric/product-01.png'}" alt="${item.itemName}">
                    </a>
                </td>
                <td class="product-title col-lg-4 ">
                    <a href="/Products/ProductDetails/${item.productId}">${item.itemName}</a>
                </td>
                <td style="text-align:center" class="product-price col-lg-2 " data-title="Price">
                    <span class="currency-symbol">$</span>${item.itemPrice}
                </td>
             
              
            </tr>
        `;
        wishlistContainer.append(rowHtml);
    });
}


function removeFromCart(CartItemId, productId, quantity) {

    var requestData = {
        userId: userId,
        productId: productId,
        cartItemId: CartItemId,
        quantity: quantity
    };


    $.ajax({
        url: "/cart/RemoveItemFromCart",
        type: "POST",
        contentType: "application/json",
        data: JSON.stringify(requestData),
        success: function (data) {
            console.log("Item removed successfully:", data);
            updateCart();
        },
        error: function (error) {
            console.error("Error removing item:", error.responseText);
        }
    });
}

function printQRCode(element) {
    let qrImage = element.querySelector("img") || element.querySelector("canvas");

    if (!qrImage) {
        alert("QR Code not found!");
        return;
    }

    let qrCodeData;

    if (qrImage.tagName === "IMG") {
        qrCodeData = qrImage.src; // إذا كان QR كصورة
    } else if (qrImage.tagName === "CANVAS") {
        qrCodeData = qrImage.toDataURL("image/png"); // إذا كان QR كـ Canvas
    } else {
        alert("Unsupported QR Code format.");
        return;
    }

    let printWindow = window.open('', '', 'width=800,height=800');
    printWindow.document.write(`
        <html>
            <head>
                <title>QR Code</title>
                <style>
                    body {
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        height: 100vh;
                        margin: 0;
                    }
                    img {
                        width: 50vw;
                        height: 50vw;
                        max-width: 400px;
                        max-height: 400px;
                    }
                </style>
                <script>
                    window.onload = function() {
                        window.print();
                        setTimeout(() => window.close(), 500);
                    };
                </script>
            </head>
            <body>
                <img src="${qrCodeData}" alt="QR Code">
            </body>
        </html>
    `);
    printWindow.document.close();
}





