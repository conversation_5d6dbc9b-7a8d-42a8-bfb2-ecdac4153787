{"version": 3, "sources": ["default/_shortcode.scss", "default/_variables.scss", "default/_reset.scss", "default/_typography.scss", "default/_extend.scss", "default/_animations.scss", "default/_mixins.scss", "default/_common.scss", "default/_forms.scss", "elements/_about.scss", "elements/_back-to-top.scss", "elements/_breadcrumb.scss", "elements/_button.scss", "elements/_categories.scss", "elements/_contact.scss", "elements/_countdown.scss", "elements/_error.scss", "elements/_newsletter.scss", "elements/_pagination.scss", "elements/_poster.scss", "elements/_price-slider.scss", "elements/_privacy-policy.scss", "elements/_section.scss", "elements/_service.scss", "elements/_slick.scss", "elements/_slider.scss", "elements/_social.scss", "elements/_team.scss", "elements/_testimonial.scss", "elements/_video.scss", "elements/_splash.scss", "header/_header-top.scss", "header/_header.scss", "header/_nav.scss", "shop/_header-cart.scss", "shop/_shop.scss", "shop/_product-details.scss", "shop/_checkout.scss", "shop/_my-account.scss", "blog/_blog-list.scss", "blog/_blog-single.scss", "blog/_comment.scss", "blog/_sidebar.scss", "footer/_footer.scss", "default/_spacing.scss"], "names": [], "mappings": "CAOQ,6ICJR,MAEI,yBACA,yBACA,2BACA,0BACA,uBACA,sBACA,uBACA,uBACA,yBACA,0BAGA,yBACA,yBACA,yBAEA,yBACA,sBAEA,8BACA,6BAEA,sBACA,yBAEA,yBAEA,yBACA,wBACA,yBACA,sBAEA,0BACA,yBACA,yBACA,0BACA,2BACA,2BACA,uBACA,wBACA,yBAEA,cACA,mBACA,oBACA,oBACA,mBAGA,eACA,iBACA,gBACA,mBACA,cACA,oBACA,eAEA,eACA,iBACA,gBACA,cACA,eAEA,qDACA,gDACA,iDAEA,mBAEA,sCACA,wCACA,qCAEA,qBACA,qBACA,qBAEA,sBACA,uBACA,sBAEA,WACA,WACA,WACA,WACA,WACA,WCtFH,EACG,8BACA,2BACA,sBAIJ,0EAUI,cAGJ,mBAGI,qBAGJ,sBACI,aACA,SAGJ,SACI,aAIJ,EACI,2BACA,qBACA,aAIJ,yBAGC,qBACA,aACA,2BAGD,QACI,aAEJ,QACI,gBAGJ,YACI,yBAGJ,SAEI,iBAEJ,KACI,gCACA,WAEJ,kBAII,8BACA,qBACA,kBACA,iBACA,aACA,2BAEJ,QAEI,WAGJ,IACI,iDACA,8BACA,cACA,cACA,aACA,gBACA,qBACA,qBACA,wBACA,gCAIJ,MACI,kBAGJ,QAEI,cACA,cACA,kBACA,wBAEJ,IACI,WAEJ,IACI,eAGJ,GACI,aACA,mBAGJ,GACI,mBAEJ,GACI,iBACA,2BAGJ,WAGI,cACA,mBAGJ,cAEI,gBACA,sBAEJ,YAEI,SAIA,MACI,gBAIR,IACI,+BACA,SACA,sBACA,eACA,YAGJ,eACI,gBAEJ,OACI,SAEJ,KACI,SAEJ,SACI,qCACA,aACA,kBACA,2BAEJ,OACI,SACA,UACA,mBAGJ,6BAII,eACA,SACA,eACA,wBAGJ,aAEI,mBAGJ,oEAII,0BACA,uBACA,kBACA,eAGJ,iCAEI,eAGJ,uCAEI,UAGJ,mBACI,6BACA,0BACA,qBACA,qBACA,kBACA,YACA,YAGJ,8CACI,wBACA,gBAGJ,iDAEI,SACA,UAEJ,SACI,cACA,mBAEJ,cAGI,mBAGJ,GACI,gBACA,yBAEJ,+BAEI,qCACA,iBAEJ,IACI,cAEJ,IACI,8BACA,qBAEJ,GACI,wBACA,SACA,WACA,gBAGJ,qCAGI,0BAGJ,GACI,iBACA,mBAGJ,GACI,mBAGJ,QACI,iBAGJ,IACI,gCAGJ,YAGI,kBAKJ,aAEI,qBACA,kBACA,iBACA,aACA,YAGJ,oDAII,WACA,aAGJ,WACI,8BACA,kBACA,2BACA,iBAGJ,sBACI,eAGJ,iCAEI,8BACA,mBAGJ,+BAEI,gBCnVH,EACA,sBAGD,KACC,gBACA,gBACA,SACA,UACA,eAED,0CACC,sBACC,iBACA,oBAKF,KACC,8BACA,kCACA,mCACA,kCACA,gCACA,wBACA,6BACA,gBACA,gBAGD,uFAqBC,SACA,mBAGD,0CAYC,sBACA,kCACA,gBACA,2BAGD,OAEC,oBAGD,OAEC,oBAGD,OAEC,oBAGD,OAEC,oBAGD,OAEC,oBAGD,OAEC,oBAeA,kEACC,cAIF,gEACC,OAEC,eAED,OAEC,eAGD,OAEC,eAGD,OAEC,gBAKF,0CACC,OAEC,eAED,OAEC,eAGD,OAEC,eAGD,OAEC,gBAKF,qBAMC,0BAGD,cAIC,0BAGD,OAEC,4BAUA,oCACC,8BACA,kCAGD,oCACC,8BACA,kCAGD,oCACC,8BACA,kCAIF,EACC,8BACA,kCACA,6BACA,wBACA,gBACA,sBACC,gBACA,eAED,uBACC,eACA,iBAGD,sBACC,eAGD,4BACC,yBAGD,iBACC,kBAGD,KACC,8BACA,kCAGD,KACC,8BACA,kCAGD,KACC,8BACA,kCAGD,aACC,gBAIF,IACC,8BACA,kCAGD,IACC,8BACA,kCAGD,IACC,8BACA,kCAID,MACC,yBACA,iBACA,gBACA,WAGD,qCAGC,qBAGD,4GAIC,2BAGD,IACC,iDAMD,MAEC,kBAGD,GACI,kBACH,mBACA,kBAGE,uBACC,eACA,iBACA,wBACA,kBACA,kBAEA,0CAPD,uBAQE,mBAGD,+BACC,kBACA,WACA,UACA,WACA,mBACA,6BACA,OACA,SAGD,0BACC,eAKJ,MACC,8BACA,kCACA,eACA,kBACA,wBACA,kCACA,QACC,qBACA,2BAGA,cACC,2BAGF,cACC,wBAGF,MACC,gBAIF,GACC,mBACA,MACC,8BACA,kCACA,wBACA,eACA,kBAEA,QACC,2BAEA,qBAEA,cACC,2BAIH,MACC,kBC1XF,oBACI,4BAUJ,+pBACI,UACA,SACA,gBAEJ,WACI,UACA,SACA,gBAGJ,6mBACI,6BAGJ,2BACI,4BACA,sBACA,kCCnCJ,YACI,gBAEA,gBACI,eAGA,sBACI,qBAKZ,2BACI,GACI,UAEJ,KACI,WAIR,qBACI,KACI,mBAGJ,IACI,qBAGJ,GACI,oBAIR,qBACI,GACI,8BAGJ,IACI,gCAGJ,KACI,iCAIR,WACI,gCAGJ,iBACI,GACI,wCAGJ,IACI,0CAGJ,KACI,wCAUR,mCACI,GACI,6BAGJ,GACG,yBAIP,2BACI,GACI,6BAGJ,GACG,yBASP,iCACI,KACI,UACA,yCACA,iCAGJ,GACI,UACA,uBACA,gBAIR,yBACI,KACI,UACA,yCACA,iCAGJ,GACI,UACA,uBACA,gBAIR,eACI,qCACA,6BAOJ,gCACI,GACI,UACA,iCAIR,wBACI,GACI,UACA,gCACA,yBAQR,sBACI,GACI,UAGJ,IACI,wBACA,UAGJ,KACI,2BACA,WAIR,wBACI,GACI,wBAGJ,IACI,2BACA,UAEJ,IACI,0BACA,UAGJ,KACI,wBACA,WAIR,wBACI,GACI,wBAGJ,IACI,2BACA,UAEJ,IACI,0BACA,UAGJ,KACI,wBACA,WAIR,wBACI,GACI,wBAGJ,IACI,0BACA,UAEJ,IACI,2BACA,UAGJ,KACI,wBACA,WL7NR,iCAEI,YACA,cAGJ,gBACI,WAGJ,KACI,gBAGJ,gCACI,oBMdF,kBACC,gCADD,oBACC,kCADD,mBACC,iCADD,eACC,mBADD,gBACC,gBADD,eACC,6BADD,kBACC,gCNuCC,aACI,kDADJ,aACI,kDADJ,aACI,kDADJ,aACI,kDADJ,aACI,kDADJ,aACI,kDADJ,aACI,kDADJ,aACI,kDADJ,aACI,kDADJ,cACI,mDADJ,cACI,mDADJ,cACI,mDADJ,cACI,mDADJ,cACI,mDADJ,cACI,mDADJ,cACI,mDADJ,cACI,mDADJ,cACI,mDADJ,cACI,mDADJ,cACI,mDAOR,YACI,iBACA,WAGJ,aACI,aACA,mBAGJ,SACI,2BAQJ,mGAOI,kBACA,mBAGJ,KACI,mBACA,kBACA,kBACI,kBACA,mBAKR,QACI,gBACA,iBACA,qBACI,iBACA,kBAIR,QACI,iBACA,kBACA,qBACI,iBACA,kBAIR,SACI,kBACA,mBACA,sBACI,kBACA,mBAIR,SACI,kBACA,mBAEA,kEAJJ,SAKQ,kBACA,oBAEJ,iEARJ,SASQ,kBACA,oBAEJ,gEAZJ,SAaQ,kBACA,oBAEJ,0CAhBJ,SAiBQ,6BACA,+BAEJ,6CAEI,kBACA,mBAEA,kEALJ,6CAMQ,kBACA,oBAEJ,iEATJ,6CAUQ,kBACA,oBAEJ,gEAbJ,6CAcQ,6BACA,+BAEJ,0CAjBJ,6CAkBQ,6BACA,+BAKZ,SACI,kBACA,mBAEA,kEAJJ,SAKQ,kBACA,oBAEJ,iEARJ,SASQ,kBACA,oBAEJ,gEAZJ,SAaQ,kBACA,oBAEJ,0CAhBJ,SAiBQ,6BACA,+BAEJ,6CAEI,kBACA,mBAEA,kEALJ,6CAMQ,kBACA,oBAEJ,iEATJ,6CAUQ,kBACA,oBAEJ,gEAbJ,6CAcQ,6BACA,+BAEJ,0CAjBJ,6CAkBQ,6BACA,+BAKZ,SACI,kBACA,mBAEA,kEAJJ,SAKQ,kBACA,oBAEJ,iEARJ,SASQ,kBACA,oBAEJ,gEAZJ,SAaQ,kBACA,oBAEJ,0CAhBJ,SAiBQ,6BACA,+BAEJ,6CAEI,kBACA,mBAEA,kEALJ,6CAMQ,kBACA,oBAEJ,iEATJ,6CAUQ,kBACA,oBAEJ,gEAbJ,6CAcQ,6BACA,+BAEJ,0CAjBJ,6CAkBQ,6BACA,+BAKZ,SACI,kBACA,mBAEA,kEAJJ,SAKQ,kBACA,oBAEJ,iEARJ,SASQ,kBACA,oBAEJ,gEAZJ,SAaQ,kBACA,oBAEJ,0CAhBJ,SAiBQ,6BACA,+BAEJ,6CAEI,kBACA,mBAEA,kEALJ,6CAMQ,kBACA,oBAEJ,iEATJ,6CAUQ,kBACA,oBAEJ,gEAbJ,6CAcQ,6BACA,+BAEJ,0CAjBJ,6CAkBQ,6BACA,+BAKZ,SACI,kBACA,mBAEA,kEAJJ,SAKQ,kBACA,oBAEJ,iEARJ,SASQ,kBACA,oBAEJ,gEAZJ,SAaQ,kBACA,oBAEJ,0CAhBJ,SAiBQ,6BACA,+BAEJ,6CAEI,kBACA,mBAEA,kEALJ,6CAMQ,kBACA,oBAEJ,iEATJ,6CAUQ,kBACA,oBAEJ,gEAbJ,6CAcQ,6BACA,+BAEJ,0CAjBJ,6CAkBQ,6BACA,+BAKZ,SACI,kBACA,mBAEA,kEAJJ,SAKQ,kBACA,oBAEJ,kEARJ,SASQ,kBACA,oBAEJ,iEAZJ,SAaQ,kBACA,oBAEJ,gEAhBJ,SAiBQ,kBACA,oBAEJ,0CApBJ,SAqBQ,6BACA,+BAEJ,6CAEI,kBACA,mBAEA,kEALJ,6CAMQ,kBACA,oBAEJ,kEATJ,6CAUQ,kBACA,oBAEJ,iEAbJ,6CAcQ,kBACA,oBAEJ,gEAjBJ,6CAkBQ,6BACA,+BAEJ,0CArBJ,6CAsBQ,6BACA,+BAKZ,SACI,kBACA,mBAEA,kEAJJ,SAKQ,kBACA,oBAEJ,iEARJ,SASQ,kBACA,oBAEJ,gEAZJ,SAaQ,kBACA,oBAEJ,0CAhBJ,SAiBQ,6BACA,+BAEJ,6CAEI,kBACA,mBAEA,kEALJ,6CAMQ,kBACA,oBAEJ,iEATJ,6CAUQ,kBACA,oBAEJ,gEAbJ,6CAcQ,6BACA,+BAEJ,0CAjBJ,6CAkBQ,6BACA,+BAUZ,iDAEI,UACA,kEAGJ,qEAEI,UACA,kEAGJ,mDAEI,UACA,kEAGJ,2DAEI,UACA,kEAQJ,yDAGI,kBAGJ,oIAMI,kBACA,UAGJ,8EAGI,WACA,kBACA,OACA,MACA,YACA,WACA,UAGJ,sBACI,sCAGJ,4BACI,sBAGJ,4BACI,sBAIA,0FAGI,YAHJ,0FAGI,YAHJ,0FAGI,YAHJ,0FAGI,YAHJ,0FAGI,YAHJ,0FAGI,YAHJ,0FAGI,YAHJ,0FAGI,YAHJ,0FAGI,YAHJ,6FAGI,UASR,UACI,WACA,YACA,YACA,YACA,kBACA,wBACA,qBACA,gBACA,iBACA,cACA,eACA,gBAEA,qBACA,gBACA,gEAhBJ,UAiBQ,WACA,aAEJ,0CApBJ,UAqBQ,WACA,aAEJ,kBACI,WACA,YACA,OACA,SACA,2BACA,WACA,kBACA,WACA,4BACA,0BACA,kBAEJ,iBACI,gBACA,kBACA,WACA,MACA,SACA,OACA,QACA,WACA,YACA,WACA,4BACA,0BAEJ,0CAnDJ,UAoDQ,WACA,YACA,WACA,YACA,kBAGA,oBACI,kBACA,qBACA,eACA,0CAJJ,oBAKQ,gBAEJ,2BACI,QACA,SACA,mBACA,2BACA,4EACA,kBACA,WACA,SACA,QACA,2BAMJ,0BACI,2BAWhB,cACI,gBACA,kBACA,oBACI,gBACA,cAEJ,sBACI,gBACA,cO5mBR,eACI,8FAGJ,eACI,qFAIJ,eACI,wFAGJ,eACI,6FAGJ,eACI,6FAGJ,eACI,2FAGJ,eACI,sGAGJ,eACI,wFAGJ,YACI,sCAEJ,aACI,uCAGJ,eACI,yBAGJ,aACI,uCAEJ,YACI,sCAGJ,gBACI,yBAEJ,cACI,yBAGJ,eACI,2BAGJ,iBACI,6BAGJ,gBACI,4BAGJ,aACI,yBAGJ,aACI,yBAGJ,eACI,2BAGJ,eACI,aACA,eACA,mBAIA,0CADJ,qBAEQ,6BCjGR,6BAII,gCACA,qCACA,+BACA,2BACA,WACA,8GAEI,aACA,kCAIR,gDAII,0BAGJ,MACI,YACA,eAGJ,gBAEI,eACA,eACA,YACA,eACA,aACA,wBACA,qBACA,wBACA,gBACA,2CACA,kBACA,kFACA,mBACA,8BACA,kCACA,kCAGJ,oGAMI,8BACA,gBACA,YACA,iBACA,gBACA,wBACA,gBACA,eACA,aACA,qDACA,4BAEA,kLACI,wBAEA,UAEJ,wOAEI,wBAEJ,8OAEI,wBAEJ,6hBACI,kCAEA,0vBACI,2BAEA,UAEJ,m5BAEI,2BAEJ,o6BAEI,2BAGR,4gBACI,qBAEA,yuBACI,cAEA,UAEJ,k4BAEI,cAEJ,m5BAEI,cAEJ,knBACI,qBAGR,wIACI,kCAmBR,uCAEI,UACA,kBACA,mDACI,kBACA,eACA,iBACA,wBACA,gBACA,kBACA,eACA,mEACI,YACA,kBACA,QACA,OACA,WACA,YACA,sBACA,kDACA,kBACA,mBAEJ,iEACI,YACA,kBACA,QACA,SACA,WACA,WACA,+BACA,4CACA,0CACA,kBACA,yBACA,UACA,mBAKA,mFACI,sCACA,sDAEJ,iFACI,UAQR,gCACI,kBAEJ,+BACI,UACA,WACA,SACA,QACA,gBACA,kBAKZ,YACI,mBACA,kBACA,kBACI,kBACA,eACA,iBACA,gBACA,wBAEJ,kBACI,cACA,kBACA,YACA,8BAEA,eACA,sBACA,oCAEA,wBACI,kCACA,gBAGR,qBACI,iBACA,cACA,kBACA,YACA,aACA,8BAEA,sBACA,oCACA,gBACA,kBACA,iBACA,2BACI,kCAKZ,mBACI,WACA,eACA,kBACA,qBACA,gBACA,eACA,YACA,gCACA,yBACA,4BACA,8BACA,kCACA,sCAEA,yBACI,yBACA,2BAMJ,4BACI,WACA,2BAIJ,aACI,UAKJ,eACI,cC5RN,0CADD,kCAEE,mBACA,mBAED,0CALD,kCAME,oBAED,sCACC,mBACA,WAID,uCACC,mBACA,gBACA,2CAHD,uCAIE,gBAED,0CAND,uCAOE,gBAGF,8CACC,eACA,mBACA,cAED,kCACC,8BACA,mBAED,8CACC,kBACA,2CAFD,8CAGE,gBAGF,6CACC,mBACA,2CAFD,6CAGE,iBAIH,+BACC,iBACA,0CAFD,+BAGE,kBAGA,wDACC,eACA,kBACA,cAED,qDACC,eACA,2CAFD,qDAGE,gBAED,0CALD,qDAME,gBAGF,wDACC,qBACA,8DACC,kCAOL,iBACC,kBACA,UACA,uBACC,WACA,WACA,WACA,yBACA,kBACA,OACA,QACA,SACA,WAIF,gBACC,yCACA,kBACA,oCACA,kBACA,oCACA,6BACA,mBACA,0CARD,gBASE,cAED,uBACC,mBAGA,gCACC,mBACA,gBAED,2BACC,8BAGF,sBACC,kCAIF,eACC,iBACA,oBACA,0CAHD,eAIE,kBAED,sCACC,gBACA,6CACC,mBAKH,gBACC,mBACA,0CAFD,gBAGE,oBAED,2BACC,eACA,gBACA,4BACA,mBAED,uBACC,mBAKD,0BACC,mBACA,8BACC,kBACA,WAED,sCACC,gBACA,0CAFD,sCAGE,cCjKJ,aACI,eACA,aACA,WACA,cACA,WACA,YACA,iBACA,gCACA,WACA,kBACA,qBACA,4BACA,UACA,qBACA,uCACA,UACA,mBACA,0CAlBJ,aAmBQ,WACA,YACA,kBAEJ,mBACC,yBAED,kBACD,YACE,WACA,UACA,mBACG,0CALJ,kBAMQ,YACA,YAEJ,wBACA,yBACG,YACA,UACA,0CAJH,wBAKO,aCxCf,sBACI,kBACA,yBACA,oBAEI,oCACI,eACA,gBACA,gEAHJ,oCAIO,gBAEH,0CANJ,oCAOO,gBAEH,0CATJ,oCAUO,gBAGP,8CACI,iBACA,kBACA,UACA,0CAJJ,8CAKQ,cAEJ,qDACI,WACA,aACA,YACA,oCACA,kBACA,kBACA,UACA,WACA,WAKhB,iBACI,aACA,UACA,gBACA,gBACA,mBACA,oBACI,aACA,gBACA,8BACA,kCACA,gBACA,sBACI,WACA,cAGA,gDACI,2BAGR,8BACI,YACA,UACA,yBACA,aC7DR,2BACI,kBACA,8BACA,kCACA,gBACA,qBACA,kBACA,kBACA,+BACA,UACA,yCACI,WACA,YACA,WACA,kBACA,kBACA,MACA,SACA,OACA,QACA,WACA,4DAEJ,0CAvBJ,2BAwBQ,kBACA,gBAEJ,+BACI,gBACA,kBACA,2BACA,eAGA,qDACI,qBAIJ,qDACI,gBACA,eACA,2BACA,kBACA,QAGR,qDACI,oCACA,2BACA,+CACA,mEACI,oCAGR,yDACI,sCACA,wBACA,uEACI,sCAGR,yDACI,sCACA,yBACA,uEACI,sCAEJ,6DACI,yBAIA,iFACI,qBAIZ,6DACI,wCACA,yBACA,iEACI,yBAEJ,2EACI,wCAGR,mDACI,sCACA,+DACI,sCACA,kCACA,yBAGR,qDACI,oCACA,uBACA,0CAHJ,qDAIQ,uBAEJ,yDACI,eACA,wBACA,SACA,0CAJJ,yDAKQ,gBAGR,mEACI,sCACA,UACA,kBACA,qBACA,eAEJ,iEACI,kCACA,qEACI,yBAGJ,+EACI,mBACA,UACA,qBAIZ,mDACI,eACA,gBACA,oBACA,mBACA,sBACA,kBACA,uDACI,eC7IhB,kBACI,gBACA,kBACA,kBACA,kBACA,mBACA,6CACA,2BACA,UACA,gBACA,yBACI,WACA,YACA,WACA,oCACA,yBACA,kBACA,kBACA,MACA,SACA,OACA,QACA,WACA,4DAEJ,oBACI,kBACA,cAEJ,sBACI,kBACA,gBAEJ,6BACI,gBACA,eACA,iBAEJ,wBACI,gCACA,4CAEA,+BACI,qBAKZ,oBACI,yBACA,kBACA,kBACA,kBACA,eACA,8BACA,mBACA,sBACI,kBACA,aACA,uBAGJ,wBACI,kBACA,gBAEJ,+BACI,gBACA,eACA,iBAEJ,0BACI,gBACA,gBAIR,oBACI,yBACA,2BACI,aAEJ,sBACI,kBACA,0BACI,kBACA,WAKZ,oBACI,gBACA,aACA,2BACI,aAEJ,gCACI,UACA,oCACI,gBACA,cAGR,+BACI,gBACA,eACA,gBAEJ,0BACI,gBAKJ,oCACI,wBAKJ,4CACI,mBACA,mDACI,gBAEJ,0CALJ,4CAMQ,iBAGR,mDACI,UCnIR,aACC,WACA,kBACA,YAGA,0BACC,4BACA,yBAGA,0CADD,iCAEE,oBAED,mCACC,UACA,mBAKA,0CACC,2BAGF,qCACC,YAED,yCACC,WAID,0CACC,cACA,0CAFD,0CAGE,+BCpCJ,WACI,aACA,8BACI,YACA,WACA,oCACA,kBACA,kBACA,aACA,mBACA,uBACA,kBACA,gEAVJ,8BAWQ,YACA,YAEJ,0CAdJ,8BAeQ,YACA,WACA,mBAEJ,0CAnBJ,8BAoBQ,kBAEJ,yCACI,eACA,+CACI,aAIZ,6BACI,eACA,4BACA,yBACA,cACA,kBACA,gEANJ,6BAOQ,gBAEJ,0CATJ,6BAUQ,gBAGR,2BACI,cACA,eACA,4BAKJ,mCACI,sCACA,YACA,WACA,kBACA,kBACA,yCACI,YACA,8BACA,2BACA,4BACA,kBACA,YAGR,kCACI,gBACA,8BACA,2BAEJ,gCACI,aCzER,qBACI,kBACA,UACA,mCACA,iBACA,gBACA,gEANJ,qBAOQ,kBACA,gBAEJ,0CAVJ,qBAWQ,kBACA,gBAEJ,8BACI,oBACA,0CAFJ,8BAGQ,gBACA,oBAEJ,qCACI,mBAEJ,0CACI,iDACI,wBAGR,gCACI,mBAQZ,mBACC,eACA,MACA,SACA,OACA,QACA,YACA,WACA,gBACG,0CATJ,mBAUQ,mBAIR,qBACC,iBACA,aACA,mBACA,iEAJD,qBAKE,mBAED,0CAPD,qBAQE,cAIF,sBACI,kBACA,kCACI,mBAEJ,6BACI,mBAEJ,iCACI,mBACA,uBAGA,oDACI,4CACA,aACA,YACA,sCACA,yBACA,0CANJ,oDAOQ,YACA,YAGR,mDACI,eACA,yBACA,0CAHJ,mDAIQ,gBAIZ,uCACI,uBACA,6CACI,sCClGZ,2BACE,yBACA,kBACA,2CAHF,2BAII,wBAGF,0CAPF,2BAQI,wBAMA,0CADF,2BAEI,wBACA,gBAKN,iBACE,aACA,mBACA,eACA,0CAJF,iBAKI,eAEF,wBACE,WACA,6BACA,sCACA,yBACA,uBACA,+BACE,sCAEF,8BACE,wCAEF,0CAZF,wBAaI,mBAIN,kBACE,kBACA,0CAFF,kBAGI,gBAEF,wBACE,kBACA,YAGJ,gBACE,kBACA,eACA,SACA,UC3DF,iBACE,gBACA,WACA,iEAHF,iBAIM,iBAEJ,gEANF,iBAOM,iBAEJ,0CATF,iBAUM,iBAGA,gCACI,cACA,oDACI,aAEJ,mCACI,kBACA,aACA,gBACA,eACA,mBACA,YACA,UACA,sCACI,WACA,2CACI,iBACA,eACA,kBACA,2BACA,mBACA,cACA,eACA,mBACA,2CACA,kCACA,mDACI,gCACA,WACA,kCAGR,wCACI,iBACA,eACA,kBACA,2BACA,mBACA,cACA,eACA,mBACA,2CACA,kCACA,8CACI,gCACA,WACA,kCC3D1B,eACI,kBACA,gBACA,kBACA,iBACI,cACA,qBACI,eACA,WAGR,+BACI,kBACA,QACA,QACA,2BACA,UACA,oBACA,oBACA,0CARJ,+BASQ,cAEJ,4CACI,WACA,OACA,gBACA,kBACA,0CALJ,4CAMQ,mBAEJ,mDACI,gBACA,eAEJ,uDACI,2BACA,+BAKR,6BACI,cACA,mBACA,eACA,yBACA,0CALJ,6BAMO,gBAEH,0CARJ,6BASO,gBAEH,0CAXJ,6BAYQ,oBAGR,iCACI,eACA,iBACA,kBACA,2BACA,+BACA,mCACI,sBACA,iBAKR,yBACI,qBACA,0CAFJ,yBAGQ,uBAIJ,gDACI,yBAMhB,uBACI,kBACA,kBACA,gBACA,2CAJJ,uBAKQ,cAEJ,0CAPJ,uBAQQ,kBACA,yCACI,wBAGR,0CAbJ,uBAcQ,mBAEJ,0CAhBJ,uBAiBQ,mBAGJ,8CACI,gBACA,gBACA,qDACI,eACA,2CAFJ,qDAGQ,gBAEJ,0CALJ,qDAMQ,gBAKJ,0CADJ,iEAEQ,wBAMhB,4BACI,kBACA,kBACA,kBACA,2CAJJ,4BAKQ,cAEJ,0CAPJ,4BAQQ,iBAIR,eACI,cACA,kBACA,QACA,SACA,YACA,aACA,yBACA,iEARJ,eASQ,QACA,UAEJ,0CAZJ,eAaQ,cAGJ,4BACI,cACA,WACA,YACA,kBACA,SACA,OACA,kCACA,mBACA,yBACA,yBACA,UACA,sCAEA,qCACI,sBAEJ,qCACI,WACA,YACA,sBAEJ,qCACI,WACA,YACA,sBAEJ,qCACI,WACA,YACA,sBAEJ,qCACI,WACA,YACA,oBAOR,uBACI,kBAMR,sBACI,oBAEJ,iBACI,aACA,2HACA,yBACA,kBACA,4BACA,mBACA,yBAPJ,iBAQQ,cAEJ,0BACI,OACA,mBACA,yBAHJ,0BAIQ,iBAEJ,iCACI,eACA,gBACA,yBACA,wCACA,iBACA,kBACA,qBACA,yBACA,mBAEJ,iCACI,gBACA,2BACA,kBAEJ,4BACI,gBACA,2BAIJ,yBADJ,4BAEQ,cAGR,0BACI,uHCtPR,iDACI,0CACA,mBACA,kBACA,YACA,iBACA,QACA,mCACA,2BACA,WAGJ,gDACI,0CACA,gBACA,WAGJ,6BACI,0CACA,mBACA,WAGJ,wCACI,cAGJ,gEACI,oBAGJ,cACI,gDACA,mBACA,WACA,eACA,gBACA,cAGJ,4BAEI,WACA,kBACA,qBACA,2BACA,0BACA,yBACA,wBAGJ,aACI,gBACA,kBAGJ,oBACI,mBC1DH,uCACC,eACA,4BACA,wBACA,kBACA,oBACA,mBACA,qBACA,6CACC,WACA,WACA,WACA,sCACA,kBACA,SACA,OACA,QAGF,4BACC,mBACA,wBACA,4BAGD,uBACC,2BAED,wBACC,gBACA,mBACA,2BACC,kBACA,mCACC,yBClCJ,mBACI,eACA,0BACA,cACA,aACH,mBACA,mBACG,qBACE,YACA,WACA,iBACA,kBACA,eACA,kBACA,kBAGL,uCACC,2BACA,yCACA,sCACA,yBAGD,wCACC,2BACA,0CACA,sCACA,yBAGD,yCACC,6BACA,2CACC,wCACA,yBAKH,uBACG,mBACA,oBACA,0CAHH,uBAII,oBAGD,4CACC,kBACA,gBACA,+DACC,uBAKL,sBACC,gCACA,mBACA,oBACA,aACA,mBACA,8BACA,gBACA,6BACC,gBACA,mBACA,OAGA,kCACC,2BACA,0BACA,gBACA,eACA,wCACC,6BAKF,kDACC,oBAKH,oBACC,mBACA,0CAFD,oBAGE,oBAED,2CACC,gBACA,0CAFD,2CAGE,oBAED,0CALD,2CAME,oBACA,oBAED,kDACC,gBCvGH,aACI,oCACA,yBACA,kBACA,kBACA,mBACA,kBACA,kBACA,+BACA,2CATJ,aAUQ,mBAEJ,2CAZJ,aAaQ,mBAEJ,0CAfJ,aAgBQ,cAEJ,oBACI,WACA,YACA,UACA,yBACA,kBACA,kBACA,SACA,OACA,QACA,cACA,WACA,kBACA,UACA,+BAEJ,mBACI,mBACA,uBACI,gBAGR,oBACI,eACA,iBACA,gBACA,gBAEJ,mBACI,8BACA,0BACI,mBACA,UACA,aAGR,6BACI,aACA,YACA,UACA,gBACA,+BACA,oCACI,aAEJ,mCACI,kBACA,gBACA,eACA,eAEJ,sCACI,OACA,6CACI,kCAGR,mCACI,gBAGR,6BACI,yBACA,YACA,kBACA,uBACA,oCACI,aAEJ,mCACI,kBACA,UACA,mBACA,0CACI,WACA,WACA,YACA,oCACA,kBACA,kBACA,QACA,2BACA,OACA,QACA,cACA,WAEJ,qCACI,eACA,2BAIJ,6CACI,kBACA,gBAEJ,wCACI,gBAQhB,aACI,kBACA,oBACI,eACA,mBAEJ,eACI,eCjIJ,sCACI,aACA,0CAFJ,sCAGQ,oBAGR,+CACI,aACA,0CAFJ,+CAGQ,mBAMR,sCACI,eAEJ,sFAEI,eAKJ,sCACI,eAEJ,sFAEI,eAKJ,sCACI,eACA,0CAFJ,sCAGQ,gBAGR,+CACI,eACA,0CAFJ,+CAGQ,gBAKZ,yBACI,kBAEI,uDACI,gBACA,sBAIJ,yDACI,UACA,oEACI,UACA,WAIJ,+DACI,UACA,0EACI,UACA,WAUhB,+BACI,aACA,WACA,YACA,gCACA,wBACA,YACA,kBACA,UACA,sCACI,WACA,YACA,WACA,sCACA,kBACA,kBACA,MACA,SACA,OACA,QACA,WACA,4DAGA,4CACI,qBAGR,0CA5BJ,+BA6BQ,WACA,YACA,+BAII,kDACI,6BAMJ,kDACI,6BAMZ,6DACI,oCAEA,mEACI,sCAKR,qDACI,yBACA,2BACA,4DACI,aAEJ,2DACI,yBAGR,0DACI,cACA,0CAFJ,0DAGQ,eAOZ,+BACI,aACA,WACA,YACA,gCACA,cACA,YACA,kBACA,UACA,sCACI,WACA,YACA,WACA,sCACA,kBACA,kBACA,MACA,SACA,OACA,QACA,WACA,4DAEJ,qCACI,wBACA,4CACI,qBAGR,0CA7BJ,+BA8BQ,WACA,YACA,+BAII,kDACI,6BAMJ,kDACI,6BAQhB,8BACI,kBACA,UACA,WACA,0CAJJ,8BAKQ,WAEJ,yCACI,UACA,QAEJ,yCACI,UACA,WACA,0CAHJ,yCAIQ,YAUZ,iCACI,kBACA,UACA,QACA,2BAEA,4CACI,UACA,WAIJ,uCACI,UACA,0CAFJ,uCAGQ,WAEJ,kDACI,UACA,WACA,0CAHJ,kDAIQ,YAQhB,8BACI,kBACA,WACA,QACA,2BAEA,kEANJ,8BAOQ,QAEJ,2CATJ,8BAUQ,QAGJ,yCACI,UACA,YACA,kEAHJ,yCAIQ,SAEJ,2CANJ,yCAOQ,SAOZ,gCACI,kBACA,WACA,QACA,2BAEA,kEANJ,gCAOQ,QAEJ,2CATJ,gCAUQ,QAGJ,2CACI,UACA,YACA,kEAHJ,2CAIQ,SAEJ,2CANJ,2CAOQ,SAOZ,gCACI,kBACA,WACA,QACA,2BAEA,kEANJ,gCAOQ,QAEJ,2CATJ,gCAUQ,QAGJ,2CACI,UACA,YACA,kEAHJ,2CAIQ,SAEJ,2CANJ,2CAOQ,SAOZ,gCACI,kBACA,OACA,QACA,2BACA,+BACA,YACA,WACA,UAEA,sCACI,+BACA,gBAEJ,2CACI,UACA,QAQR,8BACI,kBACA,UACA,WACA,0CAJJ,8BAKQ,WAEJ,yCACI,UACA,QAEJ,yCACI,UACA,WACA,0CAHJ,yCAIQ,YASJ,uEACI,gBACA,sBACA,WACA,YACA,eAMhB,gCACI,aACA,gBAGJ,+BACI,YAIA,iCACI,kBACA,cACA,4CACI,UAEJ,4CACI,OAOR,6BACI,aACA,gCACI,aACA,WACA,WACA,uCACI,WACA,WACA,kBACA,yBACA,UACA,8CACI,aAGR,6CACI,WACA,oDACI,sCACA,WAMZ,2DACI,gBACA,aAGO,kFACI,sCAOf,+CACI,YAEI,yDACI,gCACA,gEACI,yBACA,UAIJ,sEACI,kCACA,6EACI,2BClexB,+BACI,eACA,0BACA,cACA,aACA,mBACA,mBACA,6BACA,iCACI,YACA,WACA,iBACA,kBACA,eACA,kBACA,kBACA,wCACA,yBAGR,4BACI,uBACA,mBACA,eACA,gBACA,2CALJ,4BAMQ,gBAEJ,gEARJ,4BASQ,gBAEJ,0CAXJ,4BAYQ,gBAGR,mCACI,aACA,mBACA,2CAHJ,mCAIQ,eAGR,kCACI,aACA,mBACA,OACA,iBACA,kBACA,QACA,2CAPJ,kCAQQ,cACA,iBAEJ,yCACI,cACA,4CACI,kBACA,SACA,+CAEI,qBACA,kBACA,mDACI,yBACA,kBAKhB,2CACI,OACA,cACA,iBACA,wDACI,cACA,eACA,cACA,kBAEJ,wDACI,eACA,6DACI,2BACA,gBAMZ,iCACI,kBACA,0CAFJ,iCAGQ,kBAEJ,4CACI,eAKR,0DACI,+DAEJ,uDACI,gEAEJ,0DACI,gEAEJ,6DACI,gEAEJ,oDACI,gEAKZ,yBACI,WACA,0CAFJ,yBAGQ,YAEJ,sCACI,aACA,qBAGJ,uCACI,kBACA,2CACI,qBAEJ,sDACI,aACA,YACA,oCACA,kBACA,aACA,sBACA,uBACA,kBACA,kBACA,SACA,WACA,kBACA,UACA,mBACA,qBACA,+BACA,kEAjBJ,sDAkBQ,UACA,YAGA,gEACI,wBAEJ,wEACI,eACA,gBACA,2BAMR,oEACI,mBACA,UACA,mBAKR,4EACI,YAIJ,sDACI,gBACA,aACA,0CAHJ,sDAIQ,kBACA,cAGA,gEACI,oCACA,WAGA,6EACI,wCACA,UAQxB,qBACI,yBACA,eACA,kBACA,UACA,gBACA,iBACA,2CAPJ,qBAQQ,kBAEJ,0CAVJ,qBAWQ,oBACA,kBAEJ,0CAdJ,qBAeQ,oBACA,iBAEJ,0CACI,aACA,0CAFJ,0CAGQ,mBAGA,0CADJ,oDAEO,wBAIH,2CADJ,iDAEQ,gBAEJ,gEAJJ,iDAKQ,eACA,oBAEJ,0CARJ,iDASQ,eACA,oBAKJ,2CADJ,uDAEQ,4BAEJ,0CAJJ,uDAKQ,wBAEJ,gEACI,aAGR,wDACI,aAIJ,0CADJ,8CAEQ,kBACA,qBAII,0CADJ,2EAEQ,YACA,WACA,MACA,OACA,gFACI,eACA,6FACI,gBAIZ,0CAbJ,2EAcQ,WAMZ,qCACI,kBACA,WAEA,6CACI,cACA,UACA,kEAHJ,6CAIQ,WAEJ,2CANJ,6CAOQ,WAEJ,0CATJ,6CAUQ,cAGR,6CACI,aACA,SAOZ,yCACI,kBACA,gBACA,mBACA,2CAJJ,yCAKQ,eAGR,sCACI,yBACA,kBACA,kBACA,0CAJJ,sCAKQ,mBAGJ,oDACI,aACA,mBAGA,mEACI,aACA,0CAFJ,mEAGQ,cAGA,6EACI,oCACA,UAGA,0FACI,wCACA,UAQxB,0CACI,OACA,UACA,kBACA,0CAJJ,0CAKQ,QAEJ,iDACI,eACA,mBACA,2CAHJ,iDAIQ,gBAEJ,0CANJ,iDAOQ,eACA,oBAEJ,0CAVJ,iDAWQ,eACA,oBAGR,oDACI,UACA,kBACA,sDACI,kBACA,2BAEJ,0DACI,WACA,WACA,WACA,sCACA,kBACA,SACA,OAEJ,0DACI,2BACA,4DACI,2BACA,kBAEJ,gEACI,sCAKhB,wCACI,kBACA,iBACA,UACA,OACA,iBACA,0CANJ,wCAOQ,kBAEJ,+CACI,WACA,aACA,YACA,oCACA,kBACA,kBACA,SACA,YACA,WACA,0CAVJ,+CAWQ,aACA,YACA,OACA,OAEJ,0CAhBJ,+CAiBQ,cAGR,4CACI,qBACA,iBAIR,yCACI,yBACA,kBACA,kBACA,kBACA,gBACA,0CANJ,yCAOQ,iBAEJ,wDACI,mBACA,kBACA,UACA,+DACI,WACA,aACA,YACA,oCACA,kBACA,kBACA,SACA,YACA,WACA,0CAVJ,+DAWQ,SACA,4BAIZ,gDACI,eACA,wBACA,kBACA,gBACA,uBACA,oBACA,qBACA,4BAEJ,gDACI,eACA,gBACA,2BAKZ,qBACI,yBACA,gBACA,8DACA,4BACA,sBACA,gEANJ,qBAOQ,gBAEJ,0CATJ,qBAUQ,qBAEJ,0CACI,mBACA,0CAFJ,0CAGQ,gBACA,oBAEJ,0CANJ,0CAOQ,oBAEJ,iDACI,mBACA,eACA,gBACA,kEAJJ,iDAKQ,gBAEJ,2CAPJ,iDAQQ,gBAEJ,gEAVJ,iDAWQ,gBAEJ,0CAbJ,iDAcQ,gBAKZ,8CACI,WACA,kBACA,UACA,4DACI,kBACA,0EACI,qBACA,eACA,kBACA,kBACA,2CALJ,0EAMQ,mBAEJ,0CARJ,0EASQ,eAKJ,sFACI,UACA,oGACI,qBACA,8CACA,0CAHJ,oGAIQ,oBAEJ,2GACI,WACA,WACA,YACA,gBACA,kBACA,OACA,QACA,mBACA,2BAQhB,iGACI,WAIJ,2EACI,kBACA,aACA,8EACI,YACA,WACA,aACA,qFACI,WACA,UACA,kBACA,+BACA,0CAGA,kGACI,mBACA,gDAU5B,qBACI,yBACA,iBACA,gBACA,iBACA,aACA,qBACA,0BAPJ,qBAQQ,iBAEJ,yBAVJ,qBAWQ,mBAEJ,0CACI,UACA,kBACA,eACA,iDACI,gBACA,mBACA,+DAEJ,oDACI,+DAEI,yBADJ,sDAEQ,mBAKhB,uCACI,kBACA,oBACA,mBACA,UACA,yBALJ,uCAMQ,oBAEJ,yBARJ,uCASQ,eACA,iBACA,eAGJ,mDACI,gEAEJ,oDACI,SACA,gBACA,UACA,uDACI,kBACA,SACA,WACA,+BACA,kBACA,UACA,+DACI,SACA,YACA,aAEI,wEACI,sBACA,uBACA,8BAIZ,+DACI,UACA,WACA,yBAHJ,+DAIQ,YAGA,wEACI,sBACA,uBACA,8BAMpB,uDACI,kBACA,UACA,UAGA,eACA,yBAPJ,uDAQQ,cAGA,8EACI,mBACA,UACA,mBAGR,kEACI,mCACA,eACA,wBACA,YACA,WACA,kBACA,iBACA,kBACA,eACA,+BACA,wEACI,yBAIR,wEACI,gBACA,WACA,kBACA,oCACA,8CACA,kBACA,YACA,WACA,kBACA,mBACA,kBACA,UACA,eACA,+EACI,gBACA,eACA,iFACI,+BAGR,+EACI,6BACA,eACA,gBAEJ,wFACI,kBACA,8FACI,eACA,cAEJ,uGACI,eACA,gBACA,wBACA,gBAGR,8EACI,WACA,QACA,SACA,qCACA,sCACA,yCACA,kBACA,aACA,UAKR,6DACI,mBACA,UAOA,sEACI,mBACA,UAGQ,uFACI,oBACA,sBAMJ,uFACI,oBACA,sBAY5B,sCACI,yBACA,kBACA,mBACA,kBACA,UACA,2CANJ,sCAOQ,cAEJ,gEATJ,sCAUQ,cAEJ,0CAZJ,sCAaQ,wBAEJ,0CAfJ,sCAgBQ,wBAEJ,6CACI,WACA,aACA,YACA,oCACA,kBACA,kBACA,SACA,WACA,WACA,gEAVJ,6CAWO,OACA,MACA,sBAEH,0CAfJ,6CAgBQ,cAGR,6DACI,aAEJ,oDACI,aACA,mBACA,aACA,0CAJJ,oDAKQ,eAIJ,mEACI,aAIZ,0CACI,OACA,iDACI,UACA,2CAFJ,iDAGQ,WAEJ,2CALJ,iDAMQ,YAEJ,0CARJ,iDASQ,oBAIZ,wCACI,iBACA,OACA,iBACA,4CACI,qBACA,iBACA,iBACA,mBACA,2CALJ,4CAMQ,iBACA,kBAEJ,0CATJ,4CAUQ,iBACA,kBAIR,0CAnBJ,wCAoBQ,kBACA,cACA,iBAKZ,qBACI,gBACA,sBACA,2BACA,4BACA,0CALJ,qBAMQ,gBAEJ,0CARJ,qBASQ,gBAEJ,0CAXJ,qBAYQ,2BAEJ,0CACI,UACA,kBACA,eACA,oDACI,+DAEJ,iDACI,UACA,mBACA,+DACA,0CAJJ,iDAKQ,WAGR,4CACI,eACA,gEACA,0CAHJ,4CAIQ,gBAGR,oDACI,gEAMR,yCACI,gBAEJ,sCAEI,kBACA,0CAHJ,sCAIQ,mBAKJ,8CACI,WAQJ,0DACI,+DAEJ,uDACI,gEAEJ,0DACI,gEAIJ,kDACI,gEAOZ,qBACI,KACI,UACA,yCACA,iCAEJ,GACI,UACA,uBACA,gBAIR,qBACI,KACI,UACA,iCAEJ,GACI,UACA,gBAIR,cACI,2BAGJ,cACI,2BC5/BJ,cAEI,aACA,eACA,aACA,gBACI,YACA,wBACA,aACA,uBACA,mBACA,eACA,eACA,kBACA,UACA,sBACI,WACA,YACA,WACA,wCACA,mBACA,kBACA,kBACA,WACA,6BAEJ,sBACI,yBACA,4BACI,mBC9BhB,gBACC,oBAED,qBACC,oCACG,gBACH,2CAHD,qBAIE,kBAIA,wDACC,iBACA,2CAFD,wDAGE,iBAQH,0CADD,kBAEE,mBAED,6BACC,mBACA,gBACA,qBACA,kBACA,iCACC,kBACA,6BAGF,yBACC,cACA,gBAED,4BACC,8BAIC,uCACC,qBC5CA,4CACI,oCAKJ,yEACI,gBAIJ,sCACI,oCACA,mBACA,aACA,mBACA,kBACA,yCACA,4CACI,WACA,QACA,SACA,yCACA,sCACA,kBACA,aACA,WAEJ,wCACI,eACA,iBAGR,8BACI,aACA,mBACA,yCACI,kBACA,6CACI,kBAGR,0CACI,OACA,uDACI,eAEJ,iDACI,gBAMhB,+BACI,+BACA,0CACI,qBACA,kBACA,uCACA,YACA,mBACA,6BACA,kBACA,UACA,WACA,iDACI,WACA,YACA,WACA,oCACA,kBACA,kBACA,MACA,OACA,QACA,SACA,WACA,mBACA,eAEJ,8CACI,kBAKJ,0DACI,mBAEJ,gDACI,eACA,cACA,0CAHJ,gDAIQ,gBAOJ,qEAII,uCAHA,4EACI,qBASpB,2BACI,sCACA,kBAEJ,uBACI,kBACA,kBACA,YACA,yBAJJ,uBAKQ,gBAEJ,kCACI,YACA,sCACI,0BACA,YACA,iBACA,yBAJJ,sCAKO,iBAIX,iCACI,kBACA,QACA,2BACA,OACA,QACA,kBACA,mCACI,aACA,YACA,kBACA,kBACA,wCACA,qBACA,eACA,yBACA,eACA,yCACI,sCAMhB,iCACI,kBACA,kBACA,mBACA,kBACA,YACA,0BANJ,iCAOQ,gBAEJ,yBATJ,iCAUQ,kBACA,kBAEJ,yBAbJ,iCAcQ,kBACA,oBAEJ,gDACI,kBACA,mBACA,UACA,uDACI,8DACA,YACA,WACA,kBACA,UACA,WACA,WACA,0BARJ,uDASQ,QAGR,uDACI,eACA,gBACA,yBAHJ,uDAIQ,gBAOZ,2BACI,eACA,gBACA,mBAEJ,sCACI,gBAEJ,sCACI,eAIR,wBACI,iBACA,oBACA,yBAHJ,wBAIQ,kBAEJ,0CACI,aACA,mBACA,yBACA,eACA,iDACI,WACA,cACA,eACA,gBACA,aACA,mBACA,wBACA,+BACA,eACA,mDACI,gBAEJ,iEACI,+BACA,mEACI,cACA,iBAGR,uDACI,2BAKZ,2CACI,gBC7PR,cACI,kBACA,oBACI,WACA,YACA,WACA,iFACA,kBACA,kBACA,MACA,SACA,OACA,QAEJ,kBACI,WACA,kBAEJ,gCACI,kBACA,kBACA,QACA,OACA,QACA,2BACA,UACA,4CACI,YACA,WACA,iBACA,oCACA,kBACA,qBACA,eACA,2BACA,kBACA,UACA,8CACI,kBACA,SACA,6BAEJ,kDACI,WACA,YACA,WACA,oCACA,kBACA,kBACA,MACA,OACA,QACA,SACA,WACA,6BAGA,oDACI,qBAEJ,wDACI,qBC7DhB,wCACI,gBAMR,mBACI,kBACA,wDACA,4BACA,sBACA,2BACA,kBACA,kBACA,UACA,gEATJ,mBAUQ,sBAKR,gBACI,aACA,uBACA,kBACA,UACA,qCACA,2CANJ,gBAOQ,sCAGJ,2CAVJ,gBAWQ,oCACA,qBAEJ,0CAdJ,gBAeQ,oCACA,qCACA,mBAEJ,0CAnBJ,gBAoBQ,oCACA,sCAEJ,iCACI,UAGA,0CADJ,uBAEQ,6BAEJ,0CAJJ,uBAKQ,qBAGA,0CADJ,8CAEQ,iBAGR,0CACI,mBACA,0CAFJ,0CAGM,wBAGN,8BACI,mBACA,eACA,gBACA,kEAJJ,8BAKQ,gBAEJ,2CAPJ,8BAQQ,gBAEJ,2CAVJ,8BAWQ,oBAEJ,0CACI,iCACI,cAGR,0CAlBJ,8BAmBQ,iBAKZ,6BACI,aACA,mBACA,2BACA,eACA,0CALJ,6BAMQ,wBAEJ,6CACI,eACA,0CAFJ,6CAGQ,eAIA,0DACI,mBACA,2BACA,eACA,iBACA,cAKJ,sDACI,eACA,cACA,2BACA,qBACA,kBACA,gBACA,6DACI,YACA,QAEJ,2CAXJ,sDAYQ,gBAGJ,gEAfJ,sDAgBQ,eACA,kBAGJ,0CApBJ,sDAqBQ,eACA,kBAOpB,8BACI,oCACA,kBACA,mBACA,oBACA,mBACA,0CACA,kBACA,aACA,0CATJ,8BAUQ,kBAEJ,oCACI,kBACA,gBACA,2BAEJ,yCACI,qBACA,gBACA,UACA,kBACA,4CACI,kBACA,qBACA,gBACA,sCACA,kBACA,YACA,WACA,iBACA,gDACI,WACA,YAMhB,kCACI,qBACA,0CAFJ,kCAGQ,gBACA,kBAMZ,cACI,yBACA,qBACA,qCACI,gBACA,4CACI,gBAGR,gEATJ,cAUQ,gBAEJ,0CAZJ,cAaQ,gBAKJ,2CACI,gBACA,kDACI,gBAKZ,gBACI,gBACA,kBACA,iCACA,kBACA,iEALJ,gBAMQ,iBAEJ,kEARJ,gBASQ,iBAEJ,0CAXJ,gBAYQ,iBAEJ,gEAdJ,gBAeQ,iBAEJ,uBACI,kBACA,gBACA,iDACA,kBACA,2BACI,eACA,WACA,kBACA,mBACA,+BAEJ,iCACI,kBACA,QACA,SACA,4CACA,kBACA,UAEJ,6BACI,WACA,YACA,WACA,gCACA,kBACA,kBACA,MACA,OACA,QACA,SACA,kBACA,UACA,+BAIR,uBACI,gBACA,gBACA,gBAEA,yBACI,qBACA,8BACI,wCACA,yBACA,qBACA,gBACA,kBACA,eACA,qBAGR,6BACI,2BAEJ,0CApBJ,uBAqBQ,gBACA,gBAMA,oCACI,mBACA,UAEJ,uCACI,QACA,UACA,mBAOF,2CACI,kBASlB,iBACI,oBAEJ,gBACI,6BACA,iBAGJ,YACI,kBACA,YACA,oBACA,yBACI,YACA,gBACA,gCACI,eACA,gBACA,mBASZ,iBACI,qBAEJ,YACI,oBACA,YACA,mBACI,YACA,mBACA,kBACA,+BACA,aACA,iDACA,0CAPJ,mBAQQ,mBAEJ,yBACI,eACA,kBACA,kBACA,YACA,WACA,iBACA,kBACA,UACA,sCACA,mBACA,cACA,kBACA,2BACI,eACA,6BAEJ,6BACI,gBAIR,4BACG,OACC,mCACI,mBACA,yBACA,cAEJ,uCACI,yBACA,mBACA,cAEJ,sCACI,8BACA,kCACA,yBACA,gBACA,qBACA,UACA,eACA,wCACI,iBACA,kBACA,QACA,eAGA,8CACI,iBAQhB,wCACI,mBACA,+DACA,uDACA,kCACA,sBACA,4BACA,8CACI,oCAMR,qCACI,mBACA,+DACA,uDACA,2CACI,oCAUhB,mBACI,yBAGJ,eACI,qBACA,kBAEA,sCACI,mBAEJ,kCACI,uBAEJ,oCACI,2BACA,eACA,0CACI,wBAQZ,gBACI,oCACA,eACA,kBACA,UACA,yDACI,aCxeR,qBACI,0DACA,2BACA,sBACA,4BACA,cACA,kBACA,uCACI,kBACA,aACA,mBACA,uBACA,kBACA,cACA,0CAPJ,uCAQQ,eAEJ,yCACI,cACA,eACA,2CACI,gBACA,yBACA,0BACA,+BACA,iDACI,6BAKhB,yCACI,aACA,mBACA,cACA,mBACA,0CALJ,yCAMO,uBACA,iBAEH,4DACI,aACA,kBACA,mEACI,WACA,YACA,UACA,yBACA,kBACA,QACA,WACA,2BAGA,8EACI,aAGR,gEACI,aACA,mBAEJ,0JACI,eACA,gBACA,yBAIZ,sCACI,kBACA,SACA,WACA,+BACA,yBACA,WACA,eACA,4CACI,2BAEJ,0CAXJ,sCAYQ,YAQZ,iBACI,cAGJ,qBACI,aACA,mBACA,0CAHJ,qBAIQ,wBAEJ,+BACI,eACA,kBACA,0CAHJ,+BAIQ,mBAEJ,0CACI,eAEJ,gDACI,eACA,wBACA,+BACA,aACA,mBACA,sDACI,YACA,gCACA,YACA,gBACA,eAGR,8CACI,gBACA,YACA,iCACA,yBACA,SACA,kBACA,UACA,6BACA,iDACI,gBAEJ,6DACI,eACA,kBACA,mEACI,wCACA,yBAGR,mDACI,mBACA,UACA,SAKR,kDACI,eACA,mEACI,2BACA,gBACA,yBACA,kBACA,kBACA,uBACD,yEACI,gBAEJ,yEACI,yCAQf,6BAEI,aACA,eACA,eACA,yBACA,0CANJ,6BAOQ,uBACA,cAEJ,gCACI,SACA,eACA,kBACA,kCACI,8BACA,wBACA,kCACA,qBACA,wCACI,2BAQhB,qCACI,yDAEI,yDACI,yBAOZ,iCACI,eACA,8CACI,OACA,kBACA,iBACA,kBACA,0CALJ,8CAMQ,eAEJ,0CARJ,8CASO,cAEH,oDACI,WACA,kBACA,SACA,UACA,+BACA,eACA,2BACA,UACA,0DACI,2BAIR,oDACI,WACA,+BACA,yBACA,kBACA,eACA,kBACA,2BACA,gBACA,0DACI,kCAMZ,0CADJ,qCAEQ,0BAMR,iCACI,gCACA,eACA,0CAHJ,iCAIQ,eACA,oBAEJ,+CACI,kBACA,0CAFJ,+CAGQ,iBAIJ,mDACI,yBAKJ,0CADJ,sDAEQ,uBACA,gCACA,oBACA,oBAOZ,iCACI,sCACA,cAII,gEACI,cACA,sEACI,yBAQJ,kDACI,cACA,wDACI,2BAMpB,qCACI,0DACA,oBAEI,yDACI,eACA,gBACA,yBAEI,6DACI,iBAGR,0CATJ,yDAUQ,gBAQhB,iCACI,sCACA,iBACA,yBAHJ,iCAIQ,eAEJ,yBANJ,iCAOO,mBAIH,mCACI,eACA,yBACA,qCACI,cACA,iBAMJ,yBADJ,6CAEQ,wBAGA,kDACI,yBACA,wDACI,6BC/WpB,gBACI,cAEI,0CADJ,oBAEO,iBAEH,0CAJJ,oBAKO,iBAMf,YACI,wBAIA,8BACI,aACA,mBACA,WACA,+CACI,OACA,cACA,0CAHJ,+CAIQ,UAIZ,2BACI,eACA,MACA,QACA,OACA,UACA,oCACA,uCACA,6BAGQ,0CACI,YACA,iBACA,iDACI,YAQxB,gBACI,yBACA,oBACA,+BACI,oCACA,eACA,mBACA,2CAJJ,+BAKQ,gBAEJ,gEAPJ,+BAQQ,mBAEJ,0CAVJ,+BAWQ,mBAIJ,2CADJ,6BAEQ,eAIJ,2CACI,+BACA,gBACA,iBACA,0DACI,uCAKR,0CADJ,8BAEO,mBAKX,gBACI,yBACA,+BACI,eACA,kBACA,UACA,2CACI,eACA,+BACA,gBACA,UACA,0DACI,uCAIZ,+BACI,oCACA,eACA,mBACA,gEAJJ,+BAKQ,mBAEJ,0CAPJ,+BAQQ,mBAGR,+BACI,aACA,mBAEI,qDACI,kBACA,eACA,gBACA,0CAJJ,qDAKQ,mBAUR,+BACI,iBACA,YACA,sCACI,YASZ,0CADJ,+BAEQ,gBAIR,2CAEQ,gDACI,eACA,iEAFJ,gDAGQ,UALhB,2CASI,0BACI,eACA,6BACI,eAOJ,kDACI,yBACA,YAMhB,gBACI,kBACA,UACA,+BACI,kBACA,4CACA,qDACA,yBAJJ,+BAKQ,eACA,aAGA,+DACI,iBACA,0BAFJ,+DAGQ,eAEJ,yBALJ,+DAMQ,UAOZ,kCACI,eACA,yBAFJ,kCAGQ,gBAEJ,qCACI,cACA,yBAFJ,qCAGQ,eAEJ,uCACI,eAIZ,4CACI,mBACA,0BAFJ,4CAGQ,mBAEJ,yBALJ,4CAMQ,mBAEJ,kDACI,yBACA,YACA,kBACA,eACA,mBACA,YACA,gBAEJ,kDACI,UACA,WACA,eACA,wBACA,QACA,wDACI,2BAMJ,6EACI,YACA,WACA,iBACA,eACA,YACA,cACA,UACA,WAKR,gEACI,gBAKZ,0BACI,2BACA,eACA,0BAHJ,0BAIQ,gBAEJ,6BACI,cACA,0BAFJ,6BAGQ,eAEJ,+BACI,eACA,gBACA,yBACA,uBACA,iCACI,kBACA,wBACA,eAEJ,sCACI,aAGA,uCACI,2BAKR,uDACI,uCACA,aACA,mBACA,eACA,iBACA,kBACA,yDACI,2BAGJ,6DACI,YACA,gCACA,YACA,sBACA,eACA,gBACA,+BAEJ,6DACI,2BACA,+DACI,2BAEJ,mEACI,2BAGR,yBA7BJ,uDA8BQ,YACA,cACA,yDACI,yBAIZ,qDACI,gBACA,yBAFJ,qDAGQ,0CACA,yBACA,gBACA,gBACA,kBACA,kBACA,yBACA,kBACA,UACA,iCACA,gCAEJ,yBAfJ,qDAgBQ,4BACA,YACA,oBAEJ,wDACI,SACA,0DACI,kBACA,eACA,0BACA,2BACA,gBACA,kBACA,cACA,+BACA,UACA,gBACA,gCACA,gEACI,6BACA,yBAEJ,iEACI,6BACA,uEACI,6BAGR,yBAtBJ,0DAuBQ,gBAIJ,qEACI,mBAIZ,0DACI,mBACA,UACA,0CASpB,0BADJ,eAEQ,cAKJ,0BADJ,gBAEQ,cAQJ,0CADJ,eAEQ,gBAEJ,kBACI,aACA,mBACA,eACA,UACA,0CALJ,kBAMQ,eAEJ,qBAEI,cACA,0CAHJ,qBAIQ,cAEJ,uBACI,eACA,gBACA,2BACA,kBACA,aACA,mBACA,uBACA,UACA,0CATJ,uBAUQ,gBAEJ,yBACI,qBACA,cAEJ,8BACI,WACA,YACA,WACA,wCACA,mBACA,kBACA,kBACA,WACA,6BACA,0CAVJ,8BAWQ,YACA,YAGR,6BACI,2BAEJ,6BACI,yBACA,oCACI,mBAGR,4BACI,yBACA,mCACI,mBAQZ,6DACI,kBACA,sCACA,oCACA,eACA,gBACA,yBACA,kBACA,YACA,WACA,iBACA,kBACA,UACA,YAIZ,2BACI,kBACA,gDACI,kBACA,SACA,QACA,gBACA,WACA,UACA,kBACA,gBACA,aACA,kBACA,iCAEA,gBACA,2BACA,gBACA,0CAhBJ,gDAiBQ,aAEJ,mDACI,gBACA,eAEJ,uDACI,eACA,gBAEJ,mDACI,SACA,qDACI,eACA,6BACA,eACA,cAGA,2DACI,2BAIZ,2DACI,kBACA,kBACA,gBACA,mBAEJ,0DACG,kBACA,WAEH,4DACI,eACA,sEACI,gBACA,gBACA,yBACA,wBACA,kBACA,cACA,gCACA,qBACA,4EACI,2BACA,kCAMZ,qDACI,UACA,mBACA,UACA,wBAIZ,4BACI,kBACA,kCACI,kBACA,UACA,WACA,UACA,QACA,2BACA,cACA,+BACA,eACA,2BACA,oCACI,qBACA,cAGR,kCACI,8BACA,2BACA,kBACA,kBACA,mBACA,gBACA,YACA,UACA,eASZ,qBACI,eACA,QACA,SACA,uDACA,UACA,kBACA,WACA,0CARJ,qBASQ,UACA,aAEJ,iCACI,YACA,WACA,eACA,yBACA,kBAEA,kBACA,YACA,MACA,UACA,uCACI,sCACA,yBAEJ,0CAfJ,iCAgBQ,YACA,WACA,eACA,WACA,UAEJ,0CAtBJ,iCAuBQ,WACA,UAGR,yCACI,oCACA,mBACA,kBACA,YACA,aACA,gBACA,cACA,0CARJ,yCASQ,WACA,wBAEJ,0CAZJ,yCAaQ,wBAGR,kCACI,+BACA,UACA,mBACA,uCACI,oBAEJ,gDACI,yBACA,6BACA,eACA,YACA,0BACA,oBACA,sDACI,yCAEJ,6DACI,2BACA,UAEJ,sEACI,2BAEJ,uEACI,2BAGR,4CACI,WACA,iBACA,eACA,+BACA,WACA,6BACA,UACA,kBACA,MACA,OACA,SACA,WACA,oBAEA,mDACI,aAEJ,8CACI,eACA,4BAGA,oDACI,2BAKhB,gCACI,UAEJ,2CACI,gCACA,oBACA,mBACA,aACA,mBACA,8BACA,kDACI,gBACA,eACA,gBACA,2BAEJ,qDACI,eACA,eACA,2BACA,kBACA,2DACI,WACA,WACA,QACA,sCACA,kBACA,YACA,QACA,UACA,eAEJ,2DACI,2BACA,iEACI,WACA,UACA,OAMZ,yDACI,aACA,mBACA,0CAHJ,yDAIQ,aACA,gBACA,uBACA,oEACI,gBACA,kBAEJ,yEACI,4BAGR,0CAfJ,yDAgBQ,aACA,oEACI,WAGA,yFACI,kBAEJ,iGACI,eAEJ,uFACI,kBAEJ,0FACI,cACA,kBACA,yGACI,cAGR,wFACI,iBAIZ,oEACI,gBAGR,2CACI,mBACA,UAGR,0BACI,mBACA,UACA,yCACA,wDAQR,mCACI,yBACA,0CAFJ,mCAGQ,gBAEJ,oDACI,eACA,iBACA,0CAHJ,oDAIQ,eAGR,0DACI,YACA,2CAFJ,0DAGQ,YAGR,sDACI,kBACA,8EACI,eACA,gCACA,gBACA,aACA,kBACA,kBACA,0CAPJ,8EAQQ,kBACA,mBAEJ,oFACI,kBACA,yBACA,eACA,0CAJJ,oFAKQ,mBAEJ,0CAPJ,oFAQQ,cAGR,oFACI,yBACA,SACA,eAGR,2EACI,kBACA,SACA,OACA,QACA,oCACA,yBACA,cACA,6BACA,UACA,2CAVJ,2EAWQ,eACA,MACA,YACA,SACA,WACA,YACA,oBACA,YACA,iBAEJ,0FACI,eACA,yBACA,kBACA,QACA,WACA,YACA,WACA,iBACA,sCACA,mBACA,aACA,gGACI,sCACA,yBAEJ,2CAhBJ,0FAiBQ,eAKJ,2CAFJ,0FAGQ,YACA,eAIJ,iFACI,eACA,SACA,kBAEI,4GACI,oBACA,mBACA,UACA,wBACA,gIACI,UACA,wBAEJ,8HACI,UACA,wBAKR,sGACI,mBAKhB,qFACI,aACA,mBACA,eACA,gBACA,WACA,eACA,gCACA,kBACA,2CATJ,qFAUQ,eACA,gBAEJ,gGACI,kBACA,kBACA,SACA,oGACI,WACA,YAIJ,wGACI,YACA,gCACA,gBACA,cACA,eACA,kBACA,QACA,2BACA,QACA,6BAGR,2FACI,2BACA,iGACI,2BACA,0CAIZ,gFACI,OAGR,2EACI,kBACA,MACA,UACA,YACA,UACA,2BACA,kBACA,UACA,oBACA,+BACA,gBACA,2CAZJ,2EAaQ,aAEJ,2CAfJ,2EAgBQ,iBACA,mBACA,UACA,wBACA,oBACA,WACA,aACA,oBAEJ,qGACI,oCACA,yBACA,gDACA,aACA,aACA,2CANJ,qGAOQ,cAEJ,2CATJ,qGAUQ,cACA,UACA,gBACA,gBAGR,oGACI,UACA,aACA,aACA,qCACA,gBACA,+BACA,2CAPJ,oGAQQ,qCACA,UACA,aAGR,+FACI,UACA,2BACA,+BACA,2CAJJ,+FAKQ,UACA,yBAEJ,6GACI,qBAEJ,6GACI,qBAEJ,6GACI,qBAEJ,gHACI,eACA,yBACA,mBACA,2CAJJ,gHAKQ,gBAGR,kGACI,mBAGI,uGACI,eACA,gBACA,wBACA,eACA,6GACI,2BAMpB,6FACI,4BACA,cACA,UACA,2BACA,+BACA,qBACA,2CAPJ,6FAQQ,eACA,UACA,wBACA,WAGJ,+GACI,eACA,yBACA,mBAEJ,2GACI,aACA,qCACA,SAEI,yIACI,mBAEJ,2HACI,gBACA,cACA,kBACA,+HACI,kBACA,2BAGA,qIACI,qBAMpB,uGACI,gBACA,cACA,kBAMhB,6CACI,2BAEI,2CADJ,gDAEQ,eAEJ,2DACI,eAEJ,kDACI,iBACA,YACA,yDACI,YAaZ,0CADJ,+BAEQ,cACA,eACA,MACA,SACA,aACA,YACA,oCACA,YACA,+BACA,uBACA,kBACA,UACA,yCACI,cACA,0BACA,gBACA,SACA,4CACI,yBACA,2BACA,UACA,+BACA,8CACI,wBACA,6CACA,uBACA,cACA,qBACA,sDACG,aAIH,qEACI,SACA,4EACI,YACA,QACA,wBAGR,iFACI,aACA,gBACA,oBACA,mBACA,UACA,eACA,gBACA,UACA,mBAEI,sFACI,iBACA,4FACI,aAEJ,4FACI,2BACA,gCAYhC,oCACI,QACA,mBACA,UAEI,iDACI,wBACA,UACA,+DACI,qBAEJ,+DACI,qBAEJ,+DACI,qBAEJ,+DACI,qBAEJ,+DACI,qBAEJ,+DACI,qBAEJ,+DACI,qBAEJ,+DACI,oBAQxB,kBACI,+BACA,kBACA,SACA,WACA,YACA,WACA,sCACA,mBACA,wBACA,eACA,0CAXJ,kBAYQ,cAEJ,wBACI,sCACA,yBAIR,kBACI,mBACA,sBACI,gBAEJ,0CALJ,kBAMQ,cAIR,oBACI,iBACA,0CAFJ,oBAGQ,cAEJ,0CALJ,oBAMQ,kBAEJ,8BACI,aACA,mBACA,uBACA,UACA,+BACA,kBACA,UACA,2BACA,eACA,0CAVJ,8BAWQ,gBAEJ,gCACI,qBACA,cAEJ,oCACI,WACA,YACA,WACA,kCACA,kBACA,kBACA,WACA,mBACA,6BACA,0CAVJ,oCAWQ,YACA,YAGR,oCACI,yBACA,0CACI,oBAOZ,yBACI,YCh2CJ,0CADJ,cAEQ,cAGR,UAEI,aACA,mBACA,eACA,uBACA,eACA,2CAPJ,UAQQ,gBAEJ,aACI,cACA,2CAFJ,aAGQ,eAEJ,eACI,2BACA,gBACA,eACA,gCACA,iBACA,YACA,cACA,kBACA,6BACA,uBACI,WACA,WACA,QACA,oCACA,kBACA,YACA,OACA,UACA,eAEJ,qBACI,yBACA,6BACI,UACA,WAGR,sBACI,yBACA,6BACI,WACA,UAKhB,kCACI,kBACA,oCACI,kBACA,kBACA,2CACI,YACA,gCACA,gBACA,cACA,eACA,kBACA,QACA,YAKA,0DACI,WACA,UAIZ,gDACI,kBACA,SACA,OACA,gBACA,WACA,UACA,kBACA,gBACA,kBACA,kBACA,iCACA,+BACA,gBACA,oBACA,mDACI,SACA,qDACI,kBACA,eACA,0BACA,2BACA,gBACA,iBACA,kBACA,cACA,+BACA,UACA,gBACA,2DACI,6BAGJ,4DACI,6BACA,kEACI,6BAOhB,sDACI,QACA,UACA,mBACA,UACA,mBCjIhB,eACI,eACA,aACA,MACA,SACA,YACA,iDACA,0CAPJ,eAQQ,WACA,aAEJ,kCACI,oCACA,kBACA,YACA,YACA,aACA,sBACA,cACA,0CARJ,kCASQ,WACA,cAEJ,0CAZJ,kCAaQ,mBAGJ,qDACI,UACA,mBAIJ,2DACI,mBACA,mBACA,eAIJ,2DACI,iCACA,mBACA,eAIJ,iEACI,gCAIR,4BACI,aACA,mBACA,8BACA,gCACA,oBACA,0CACI,eACA,cACA,gBACA,0CAJJ,0CAKQ,gBAGR,wCACI,YACA,WACA,eACA,yBACA,kBAEA,8CACI,sCACA,yBAIZ,0BACI,eACA,UAKJ,0BACI,aACA,mBACA,mBACA,gCACA,oBACA,0CANJ,0BAOQ,wBAEJ,qCACI,gBACA,mBAEJ,oCACI,kBACA,kBACA,0CAHJ,oCAIQ,mBAEJ,sCACI,cACA,yBACA,mBACA,0CAJJ,sCAKQ,YAEJ,0CACI,mBACA,aACA,YACA,iBAGR,+CACI,YACA,WACA,yBACA,oCACA,kBACA,eACA,yBACA,kBACA,UACA,WACA,+BACA,0CAZJ,+CAaQ,YACA,WACA,gBAEJ,qDACI,sCACA,yBAIZ,wCACI,OACA,kBACA,oBACA,0CAJJ,wCAKO,iBAGP,0CACI,mBACA,eACA,6BACA,4CACI,cAEJ,yDACI,gBACA,gBAGR,sCACI,eACA,yBACA,mBACA,gBACA,uBACA,oBACA,qBACA,4BACA,0CATJ,sCAUQ,eACA,oBAEJ,0CAbJ,sCAcQ,mBAGR,sCACI,eACA,kCACA,yBACA,0CAJJ,sCAKQ,eACA,iBAGR,yCACI,aACA,mBACA,kBACA,QACA,QACA,2BACA,yBACA,0CARJ,yCASQ,iBACA,wBACA,2BACA,gBAEJ,iDACI,kBACA,YACA,WACA,iBACA,eACA,yBACA,yBACA,kBACA,+BACA,uDACI,sCACA,yBAGR,yDACI,eACA,gBACA,cACA,YACA,WACA,YACA,kBACA,UAEJ,oJAEE,wBACA,SAEF,4DACE,0BAIV,4BACI,6BACA,2CACI,aACA,mBACA,8BACA,mBACA,yBACA,eACA,4DACI,gBAGR,uCACI,aACA,qCACA,gBACA,0CAJJ,uCAKQ,eAEJ,iDACI,kBAEI,8DACI,sBAGR,0CAPJ,iDAQQ,cACA,oBAMpB,oBACI,QAGJ,qBACI,YACA,WACA,gCACA,eACA,MACA,OACA,QACA,SACA,UACA,eC9RJ,iBACI,aACA,eACA,aACA,gCACI,wBACA,WACA,YACA,mBACA,mFACA,gCACA,gBACA,8BACA,oCACA,0CAVJ,gCAWO,WACA,2BAQX,cACI,kBACA,0CAFJ,cAGQ,mBAEJ,yBACI,kBACA,cACA,2BACI,cACA,yBACA,kBACA,gBACA,kBACA,+BACI,kBACA,WACA,eAEJ,sCACI,kBACA,MACA,OACA,QACA,SACA,kBACA,UACA,eAGR,sCACI,kBACA,SACA,UACA,UACA,sDACI,sCACA,cACA,qBACA,eACA,gBACA,WACA,kBACA,4CAEJ,kDACI,UACA,YAIZ,yBACI,mBAEJ,+BACI,gBACA,kBACA,mBACA,+CACI,mBACA,eACA,6BACA,iDACI,cAEJ,8DACI,gBACA,gBAGR,sCACI,eAEJ,0CACI,6BAEJ,sCACI,wBACA,eACA,gBACA,mBACA,6BACA,wCACI,eAGR,sDACI,YACA,6BACA,qBACA,0CAJJ,sDAKQ,wBAGA,iEACI,WACA,2BACA,gBACA,eACA,kCACA,2EACI,cACA,6BACA,cAMpB,oCACI,kBACA,SACA,OACA,QACA,UACA,kBACA,eAGJ,2BACI,aACA,mBACA,uBAEA,YACA,8BACI,WAGI,mFACI,WACA,YACA,iBACA,kBACA,oCACA,cACA,kBACA,eACA,kBACA,eACA,2BACA,yCACA,kBACA,UACA,iGACI,WACA,YACA,WACA,oCACA,kBACA,kBACA,MACA,SACA,OACA,QACA,WACA,4DAEJ,uFACI,+BAGA,6GACI,qBAEJ,mGACI,6BAMZ,8CACI,kBACA,YACA,iBACA,eACA,cACA,kBACA,gBACA,eACA,yBACA,wCACA,eACA,yCACA,kBACA,UACA,qDACI,WACA,YACA,WACA,wCACA,kBACA,kBACA,MACA,SACA,OACA,QACA,WACA,4DAEJ,oDACI,wCACA,yBACA,2DACI,qBAUZ,qCACI,qBAEJ,4CACI,mBACA,UAIZ,0CACI,YACA,UACA,mBACA,qBAGR,gCACI,kBAEI,wIAEI,uBACA,aAEJ,gEACI,mBAEJ,wEACI,mBAIR,6CACI,YACA,aACA,gBACA,kBACA,cACA,0CANJ,6CAOQ,YACA,cAEJ,0CAVJ,6CAWQ,YACA,cAGR,sDACI,iBACA,mBACA,UAGR,iCACI,kBAEI,yEACI,uBAEJ,iEACI,uBAIZ,iCACI,kBACA,kBAEI,8CACI,0BACA,kDACI,0BAIZ,kDACI,uBACA,SACA,oCACA,+DACI,iBAIZ,gCACI,yBACA,kBACA,mBACA,eACA,0CALJ,gCAMQ,iBAGA,6CACI,0BACA,iDACI,0BACA,yDAIZ,iDACI,SACA,uBACA,UACA,wEACI,kBACA,UACA,WACA,WACA,sCACA,sCACA,2BACA,qCACA,iBACA,kBAEI,mFACI,eACA,yBAIZ,wDACI,mBAEJ,uEACI,iBACA,UACA,mBACA,oFACI,2BAGQ,uGACI,+BACA,yBACA,2BACA,gBACA,8GACI,aAEJ,6GACI,sCACA,yBACA,kCAS5B,sCACI,8BACA,gCAEI,qDACI,qBAMZ,yCACI,WACA,WACA,WACA,yBACA,kBACA,kBACA,MACA,OACA,QACA,eAEJ,mDACI,SACA,oBACA,kBACA,6DACI,kBACA,UACA,WACA,+DACI,qBACA,kBACA,YACA,WACA,iBACA,sCACA,oCACA,2BACA,eACA,kBACA,eACA,+CACA,qEACI,sCACA,kCACA,yBACA,4CAIZ,mEACI,gBACA,gBAEJ,0DACI,eACA,2BAGA,iFACI,eAMR,+CACI,+BACA,iBAKA,uDACI,qBAOR,+CACI,kBAGR,+CACI,kBACA,UACA,0DACI,UACA,SAEJ,2DACI,UACA,WACA,SAEJ,+DACI,cACA,eACA,gBACA,WACA,kBACA,sCACA,iBACA,gBACA,yBACA,oEACI,oCACA,2BAEJ,wEACI,sCAIZ,+CACI,cACA,cAEI,kEACI,kBACA,aACA,mBACA,uBACA,kBACA,YACA,oEACI,eACA,kBAKhB,yDACI,mBAEJ,iDACI,iBAEI,yDACI,YACA,WACA,eACA,gEACI,YACA,WACA,eAIJ,gEACI,iBACA,uEACI,WACA,UAMpB,mDACI,mBACA,eACA,0DACI,cACA,4DACI,gBACA,uBACA,oBACA,qBACA,4BAGR,mEACI,eACA,kFACI,eAGR,0EACI,SACA,gBAIJ,8DACI,YAIZ,mCACI,aACA,mBACA,sCACA,kBACA,mBACA,0BANJ,mCAOQ,cACA,wBAEJ,8CACI,4BACA,0BAFJ,8CAGQ,kBAEJ,gDACI,kBACA,UACA,uDACI,WACA,aACA,YACA,kBACA,oCACA,kBACA,YACA,OACA,QACA,cACA,WAIZ,oDACI,OACA,SACA,mBACA,0BAJJ,oDAKQ,WAEJ,kEACI,eACA,gBACA,mBAEJ,2DACI,eAEJ,2EACI,aACA,mBACA,uFACI,eACA,gBACA,2BACA,iBAEJ,kFACI,eACA,gBACA,6BAQA,0DACI,mBAQxB,eAEI,iBACA,gBACA,aACA,eACA,0CANJ,eAOQ,wBAEJ,kBACI,eACA,eACA,uBACI,+BACA,WACA,YACA,aACA,mBACA,uBACA,kBACA,6BACA,8BACI,cACA,UACA,WACA,cACA,kBACA,YAKA,6CACI,mBAIJ,6CACI,qBAMJ,6CACI,mBAIJ,6CACI,qBAMJ,6CACI,mBAIJ,6CACI,qBAMJ,6CACI,mBAIJ,6CACI,qBAMJ,6CACI,mBAIJ,6CACI,qBAMJ,6CACI,mBAIJ,6CACI,qBAMJ,6CACI,mBAIJ,6CACI,qBAMJ,6CACI,mBAIJ,6CACI,qBAMJ,6CACI,mBAIJ,6CACI,qBAMJ,6CACI,mBAIJ,6CACI,qBAMJ,6CACI,mBAIJ,6CACI,qBAOpB,eACI,aACA,mBACA,eACA,YACA,eACA,kBACI,yBACA,gBACA,iBACA,mBACA,eACA,gBACA,aACA,mBACA,uBACA,yBACA,gBACA,iBACA,WACA,eACA,eACA,0CAhBJ,kBAiBQ,gBAEJ,yBACI,qBAEJ,wBACI,qBAKZ,mBACI,yBACA,aACA,mBACA,aACA,kBACA,mBACA,6BACA,0CARJ,mBASO,cAEH,0CAXJ,mBAYQ,cACA,mBAEJ,8BACI,kBACA,0CAFJ,8BAGQ,eACA,oBAEJ,gCACI,yBACA,kBACA,cACA,6BACA,gBAEJ,kCACI,kBACA,6BAGR,oCACI,OACA,kBACA,mBACA,0CAJJ,oCAKQ,SACA,WAEJ,mDACI,mBACA,eACA,wBACA,qDACK,6BAGT,oDACI,aACA,mBACA,kBACA,eACA,0CALJ,oDAMQ,wBAEJ,mEACI,gBACA,iBACA,qBACA,wEACI,gBACA,2BAGR,iEACI,cAGR,2DACI,eACA,gBACA,2BAEI,4EACI,cACA,6BACA,iBAIZ,kDACI,kBACA,QACA,QACA,2BACA,0CALJ,kDAMQ,iBACA,wBACA,iBAEJ,4DACI,kBACA,cACA,YACA,WACA,iBACA,yBACA,kBACA,2BACA,eACA,gBACA,6BACA,mBACA,0CAbJ,4DAcQ,aACA,sBAEJ,kEACI,gCACA,kCACA,yBAKhB,yBACI,gCACA,8BAEI,wCACI,qBAIZ,wCACI,aACA,0CAFJ,wCAGQ,cAEJ,mDACI,kBACA,gBACA,gBACA,kBACA,0CALJ,mDAMQ,oBAEJ,uDACI,eAGR,yDACI,UAEJ,sDACI,iBACA,wBACA,gBACA,gEACI,YACA,WACA,cACA,qBACA,kBAIJ,kDACG,sBAMf,eACI,cACA,eACA,iBAKA,wCACI,iBAEJ,iDACI,gBAMR,qBACI,wBACA,UACA,SACA,wBACI,aACA,mBACA,mBACA,gCACA,oBACA,mCACI,gBACA,mBACA,iBAEJ,0BACI,cAEJ,mCACI,YACA,kBACA,gBACA,gBACA,gBACA,kBACA,0CAPJ,mCAQQ,WACA,gBAEJ,qCACI,gBAEJ,uCACI,kBACA,eAGR,+BACI,mBACA,gBACA,eACA,0CAJJ,+BAKQ,gBAGR,iCACI,OAEJ,yDACI,eACA,iBACA,2BACA,gBACA,0CALJ,yDAMQ,gBAEJ,6DACI,kBACA,cAKA,6CACI,qBAOpB,eACI,gBAGJ,gBACI,eACA,WACA,gBACA,qBACA,0CALJ,gBAMO,aAIP,uBACI,kBACA,WACA,gBACA,UACA,kBACA,8BACA,wBACA,gBACA,+BACA,6BACI,WACA,WACA,WACA,sCACA,kBACA,SACA,OAEJ,yBACI,iBAEJ,0CAtBJ,uBAuBQ,iBAEJ,6BACI,2BAIR,mBACI,mBACA,kBACA,0CAHJ,mBAIQ,gBACA,eACA,MACA,SACA,YACA,YACA,oCACA,YACA,wBACA,gBACA,gCAGJ,gCACI,kBACA,oBAMQ,sDACI,YAEJ,qDACI,WAMhB,0BACI,eACA,gBACA,wBACA,2CACA,oBACA,mBACA,eACA,kBACA,kCACI,YACA,iCACA,kBACA,MACA,QACA,eACA,oBAEJ,iCACI,WACA,kBACA,YACA,OACA,QACA,WACA,gCACA,eAKJ,0CACI,cACA,6CACI,SACA,8BACA,4BACA,cACA,+CACI,kBACA,kBACA,wBACA,uDACI,WACA,YACA,WACA,iBACA,kBACA,mCACA,kBACA,kBACA,QACA,OACA,6BAMA,iIACI,YACA,gCACA,cACA,gBACA,yBACA,gCACA,kCASpB,qCACI,aACA,mBACA,eAEJ,qCACI,mBACA,YACA,WACA,iBACA,kBACA,kBACA,4CACI,yBAEJ,uCACI,qBACA,YACA,WACA,kBACA,sDACI,mBAEJ,sDACI,mBAEJ,sDACI,mBAEJ,sDACI,mBAEJ,sDACI,mBAEJ,sDACI,mBAEJ,sDACI,mBAEJ,sDACI,mBAEJ,sDACI,mBAEJ,sDACI,mBAEJ,sDACI,mBAOZ,oCACI,qBACA,oBACA,sCACI,yBACA,gBACA,iBACA,mBACA,eACA,gBACA,aACA,mBACA,uBACA,yBACA,gBACA,8BACA,wBAGA,6CACI,kCACA,sCACA,yBAMZ,2CACI,qBACA,kBACA,6CACI,yBACA,gBACA,iBACA,mBACA,eACA,gBACA,aACA,mBACA,uBACA,gBACA,8BACA,wBAGA,oDACI,+BAIZ,2GAEI,2BACA,8BAGR,yCACI,WACA,wBAGJ,qCACI,kBACA,SACA,UACA,YACA,WACA,sCACA,mBACA,wBACA,eAGJ,wBACI,OAIR,cACI,6CAEI,0CADJ,qBAEO,gCAIH,0CADJ,qBAEO,gCAOP,oDACI,oCACA,gBACA,2CAHJ,oDAIO,kBAEH,gEACI,iBACA,2CAFJ,gEAGQ,iBAOA,0CADJ,8EAEQ,YAQpB,0CAEQ,oCACI,gBACA,qBACA,YAEI,4EACI,2BAEJ,oEACI,4BASpB,yBACI,aACA,mBACA,8BACA,2CAJJ,yBAKM,sBACA,uBACA,oBAGE,2CADJ,gDAEO,mBACA,iBAEH,uDACI,gBAMZ,gBACI,aACA,mBACA,eACA,YACA,uBACI,WACA,kBACA,kBACA,eACA,gBACA,2BACA,+BACA,kBACA,UACA,eACA,WACA,6BACI,WACA,sCACA,kBACA,MACA,OACA,cACA,YACA,WACA,UACA,eACA,sCACA,kBACA,WAGA,mCACI,wCACA,UAGR,kCACI,yBACA,wCACI,wCACA,UACA,sCAOhB,oBACI,kBACA,mBACA,wCACI,kBACA,UACA,QACA,2BACA,QACA,UACA,+CACI,eACA,mBACA,gBAEJ,oDACI,mBACA,gEACI,eACA,gBACA,2BACA,cACA,kBAEJ,2DACI,eACA,gBACA,gBACA,6BAGR,kDACI,kBACA,SACA,QACA,yBAJJ,kDAKQ,UAEJ,6DACI,eACA,2BACA,sCACA,yBACA,kBACA,YACA,WACA,iBACA,kBACA,cACA,eACA,mEACI,2BACA,kCAKhB,iCACI,kBACA,UACA,6CACI,UACA,WACA,SAEJ,iDACI,cACA,eACA,gBACA,WACA,kBACA,sCACA,iBACA,gBACA,yBAGR,0CACI,kBACA,8CACI,kBACA,WACA,yBAHJ,8CAIQ,aACA,iBACA,sBAKR,+DACI,UACA,sEACI,mBAEJ,2EACI,mBACA,uFACI,gBAGR,yEACI,SACA,QACA,oFACI,oCACA,0FACI,sCACA,yBASxB,0BACI,aACA,oCACA,kBACA,yBACA,mBACA,iBACA,8CACI,OACA,qDACI,gBACA,uDACI,eAIJ,sEACI,eACA,gBACA,cAEJ,iEACI,gBACA,gBACA,6BAIZ,gDACI,kBACA,UACA,WACA,uDACI,WACA,aACA,YACA,sCACA,kBACA,kBACA,YACA,YACA,WChqDJ,qDACI,mBACA,kBACA,0CAHJ,qDAIQ,oBAEJ,kEACI,kBACA,SACA,8EACI,QAEJ,kFACI,sCACA,cACA,qBACA,eACA,gBACA,WACA,kBAOZ,yCACI,WACA,kBAMI,iDACI,mBASZ,+CACI,aACA,mBACA,mBACA,cACA,oBACA,gCACA,4DACI,iBACA,eACA,cAGA,8DACI,eACA,iBACA,wBACA,6BACA,oEACI,2BAKhB,8CACI,mBACA,wBAEJ,6CACI,gBACA,eACA,kCACA,cACA,mBACA,yBACA,0CAPJ,6CAQQ,gBAEJ,gEACI,aACA,mBACA,oBACA,0CAJJ,gEAKQ,oBAEJ,qEACI,qBACA,cACA,0CAHJ,qEAIQ,cAGR,2EACI,wBACA,6BAEJ,6EACI,sCACA,YACA,iBACA,iBACA,eACA,yBACA,mBACA,kCAIZ,6CAEI,mBACA,gDACI,2BACA,gBACA,eACA,iBACA,aACA,mBACA,SACA,kDACI,mBACA,eAIZ,4CACI,mBAEJ,kDACI,mBACA,aACA,mBACA,yDACI,gBACA,eACA,gBACA,gBACA,0CALJ,yDAMQ,eACA,gBAIR,iEACI,uBACA,oEACI,WAEI,gFACI,WACA,YAKhB,0EACI,aACA,mBAKY,4IACI,YACA,+BACA,cACA,mBAMpB,yEACI,uBACA,gFACI,eAKZ,iDACI,mBAGQ,mEACI,wCAGR,uDACI,2CACA,kBACA,0CAHJ,uDAIQ,cAEJ,mEACI,eAEJ,kEACI,gBACA,iBAKR,kEACI,eAGR,wDACI,gBACA,yBACA,0CAHJ,wDAIQ,gBAGR,+DACI,SACA,UACA,YACA,wBAEJ,2DACI,qBACA,WACA,8BACA,yBACA,4BACA,mCACA,mBACA,iBACA,gBACA,kBAGR,+CACI,kDACA,oBACA,mBACA,gBAEJ,iGAEI,aACA,mBACA,6GACI,eACA,cACA,kBAGA,mIACI,eAKR,mDACI,eACA,gBACA,2BAGR,oDACI,yBACA,0CAFJ,oDAGQ,2BACA,iBAEJ,gEACI,WACA,kBAMJ,wEACI,mBACA,2FACI,oBAGR,yEACI,mBAIJ,0CADJ,2EAEQ,oBAGA,4GACI,gBAQhB,0CADJ,wBAEQ,sBACA,iCACI,oBAGR,wCACI,OACA,qDACI,OACA,+DACI,WACA,kBACA,cAEI,4EACI,sBAKhB,yDACI,eACA,oBACA,iEAHJ,yDAIQ,iBAEJ,0CANJ,yDAOQ,iBAMhB,gBACI,gBACA,UACA,mBACI,cAEI,iEADJ,6BAEQ,mBAMhB,SACI,YACA,mBACA,eACI,WACA,WACA,YACA,YACA,iBACA,UACA,kBACA,+BACA,eACA,gBACA,cACA,cAEJ,iBACI,WACA,cACA,WACA,iBACA,eACA,kBACA,eACA,gBACA,WACA,YACA,mBACA,kBACA,eACA,+BACA,uBACI,kCAGR,oFAEE,wBACA,SAEF,4BACE,0BAKF,sBACI,gBACA,WACA,YACA,wBACA,mBACA,uBACA,kBACA,eACA,eACA,4BACI,gCACA,WAKZ,eACI,kBACA,YACA,WACA,UACA,0CALJ,eAMQ,YACA,YAKJ,0CADJ,qBAEQ,gBACA,iCACI,eACA,8CACI,aAIZ,sCACI,kBACA,gBACA,mBACA,mBACA,eACA,mBACA,0CACI,mBACA,+BACA,WACA,YACA,mBAIA,wGACI,kCAQR,4CACI,WACA,YACA,kBACA,qBACA,iEALJ,4CAMQ,WACA,aAUA,oFACI,4BAOpB,+BACI,kBACA,4CACI,kBACA,SACA,WACA,4DACI,sCACA,cACA,qBACA,eACA,gBACA,WACA,kBACA,4CAKR,mDACI,WACA,YAMJ,kCACI,oBACA,0CAFJ,kCAGQ,qBAGR,0BACI,oBACA,0CAFJ,0BAGQ,gCACA,qBAEJ,0CANJ,0BAOQ,sBACA,mBACA,iBAGJ,6BACI,cACA,0CAFJ,6BAGQ,eAEJ,+BACI,eACA,iBACA,gBACA,cACA,wBACA,kBACA,qCACI,WACA,WACA,QACA,sCACA,kBACA,YACA,QACA,UACA,eAEJ,2EAEI,2BACA,uFACI,WACA,OACA,UAGR,2CA3BJ,+BA4BQ,gBAEJ,0CA9BJ,+BA+BQ,gBAKhB,gCACI,kBACA,gBACA,kBACA,0CAJJ,gCAKQ,gBAEH,wCACG,mBACA,0CAFH,wCAGO,iBAEH,2CACG,aACA,0CAFH,2CAGO,cAEH,6CACG,eACA,sCACA,kBACA,kBACA,kBACA,kEANH,6CAOO,gBAEJ,iEATH,6CAUO,eACA,kBAEJ,0CAbH,6CAcO,eACA,kBAEJ,oDACI,gBACA,gCACA,kBACA,YACA,SACA,2BACA,kBACA,UACA,eACA,UACA,cAEJ,mDACI,WACA,YACA,WACA,oCACA,kBACA,aACA,OACA,QACA,kBACA,UACA,eACA,0CAZJ,mDAaQ,cAGR,oDACI,0BACA,sBACA,0CAHJ,oDAIQ,mBAEJ,2DACI,mBACA,UAEJ,0DACI,mBACA,UAMpB,6CACI,oCACA,aACA,kBACA,0CAJJ,6CAKQ,cAGR,yDACI,UACA,gBACA,sJACI,uBACA,gBAIZ,mCACI,oBACA,0CAFJ,mCAGQ,qBAEJ,8CACI,oCACA,kBACA,aACA,mBACA,sDACI,gCACA,kBACA,yDACI,SACA,kBACA,2DACI,eACA,gBACA,kBACA,iEACI,YAMZ,2EACI,mBAEJ,uEACI,qBACA,0EACI,eAMhB,8EACI,oCAMR,0CACI,oCACA,mBACA,aACA,mBACA,kBACA,kBACA,gDACI,WACA,YACA,iBACA,kBACA,eACA,kBACA,UACA,kBACA,2BACA,uDACI,WACA,YACA,WACA,yBACA,kBACA,kBACA,MACA,OACA,WAGJ,wDACI,6BAEJ,yDACI,2BAGR,mDACI,OACA,0DACI,kBACA,gBAEJ,qDACI,eASR,0CADJ,oCAEQ,gBAMR,oBACI,mBAIR,kBACI,UACA,gBACA,aACA,eACA,kBACA,mBACA,qBACI,kBACA,SACA,eACA,gBACA,wBACA,0CANJ,qBAOQ,gBAGR,wBACI,WACA,YACA,gBACA,aACA,mBACA,uBACA,kBACA,cACA,4BACI,eAKZ,oBACI,eACA,uBACI,aAIR,yBACI,gBACA,aACA,kBACA,mBACA,0CALJ,yBAMQ,qBAEJ,+BACI,gBAGQ,uDACI,gCAEJ,sFAEI,eACA,iBACA,gBACA,uBACA,gBACA,0CAPJ,sFAQQ,aACA,gBAGR,2CACI,0BACA,cACA,gBAOpB,uBACI,oBACA,2CAFJ,uBAGQ,oBAEJ,0CALJ,uBAMM,iBAKM,yDACI,gBACA,aACA,mBACA,8BACA,0CALJ,yDAMQ,eAEJ,6JAEI,kBACA,iKACI,eACA,qKACI,cACA,2MACI,cAKhB,2EACI,iBACA,0CAFJ,2EAGQ,cACA,kBACA,eAUhB,yFAEI,+BAEJ,2CACI,yBAEJ,8CACI,kBACA,iBACA,eACA,gBAMR,kCACI,cAIR,yBACI,mBACA,iEAFJ,yBAGQ,iBAEJ,0CALJ,yBAMQ,iBAGJ,0CACI,YAQJ,kCACI,iBAEJ,mCACI,YAEJ,kCACI,kBACA,yBACA,6CACI,WACA,sBACA,eACA,eACA,6BACA,kBACA,WACA,UACA,mDACI,WACA,YACA,WACA,gCACA,kBACA,kBACA,SACA,UACA,mBACA,WACA,6BAEJ,mDACI,yBACA,yDACI,mBAKhB,gCACI,aAIR,UACI,aAEJ,QACI,aASQ,oEACI,gBACA,2BACA,mBAEJ,sEACI,mBACA,iBACA,mBACA,mFACI,cAEJ,qFACI,eACA,gBACA,2BAEJ,qFACI,eACA,8BACA,iBACA,gBAGR,mEACI,gBACA,sEACI,gBAGR,yEACI,cACA,mBACA,gFACI,mBACA,eAIJ,wEACI,kBACA,sCACA,YACA,WACA,YACA,eACA,gBACA,6JACI,yBACA,sCAKR,yBADJ,qEAEQ,4BAGA,6EACI,iBACA,YACA,WACA,oFACI,YACA,WAKA,2FACI,WACA,YAKR,4FACI,qBAIJ,4FACI,qBAIJ,4FACI,qBAIJ,4FACI,qBAKhB,gFACI,mBACA,yFACI,gBACA,WACA,iGACI,WACA,YACA,iBACA,kBACA,eAEJ,+FACI,yBACA,kBACA,YACA,WACA,eACA,cAMR,8FACI,aACA,UACA,0BAHJ,8FAIQ,YAEJ,yBANJ,8FAOQ,eAEJ,iGACI,YACD,mGACC,kBACA,qGACI,eAQxB,iDACI,mBAEI,gEACI,kBAIZ,4CACI,cACA,6DACI,gBACA,kBACA,iEACI,WACA,kBACA,iBAIJ,0EACI,YACA,WACA,kBACA,oCACA,eACA,WACA,iFACI,kBAEJ,qFACI,YACA,UCzpCpB,uBACC,aACA,8BACA,8BACC,mBACA,gBACA,qBAED,mCACC,qBACA,eACA,2BACA,6BACA,yCACC,yBAMH,oBACC,kCACA,SACA,0CAHD,oBAIE,iBAGA,oEACC,iBACA,0CAFD,oEAGE,oBAIH,0BACC,sCACA,0CAFD,0BAGE,cAED,6BACC,eACA,0BACA,YACA,2BACA,kBACA,0CAND,6BAOE,eACA,mBAED,yCACC,0BAED,wCACC,0BAIH,0BACC,2BAEC,0CADD,6BAEE,mBACA,kBACA,cACA,6CACA,mBACA,wCACC,iBAGF,0CAXD,6BAYE,mBAGF,6BACC,gBACA,6CACA,sBACA,aACA,eACA,gBACA,wBACA,gBACA,0CATD,6BAUE,eACA,mBAED,0CAbD,6BAcE,eACA,cACA,iBACA,yBACA,kBACA,oCACC,8BACA,kBACA,OACA,eACA,0BAGF,4CACC,eACA,0CAFD,4CAGE,kBACA,MACA,QACA,mBACA,WAED,6DACC,cACA,kBACA,YACA,WACA,iBACA,sCACA,sCACA,kBACA,eACA,yBACA,6BACA,0CAZD,6DAaE,YACA,WACA,iBACA,gBAED,mEACC,kCAGF,mDACC,aAGF,+CACC,gBACA,YACA,0CAHD,+CAIE,eACA,kBACA,OACA,MACA,mBACA,aAED,0CAXD,+CAYE,YAED,iDACC,mBACA,cACA,qDACC,mBACA,YACG,WACH,iBAGF,sDACC,aAGF,2CACC,UACA,yBACA,0CAHD,2CAIE,WACA,gBACA,oBAED,6CACC,6BAED,kDACC,aAID,2DACC,qBACA,kBACA,eACA,iEACC,kCAGF,qDACC,aAQF,sGACC,eAED,oGACC,mBACA,0CAFD,oGAGE,oBAIH,6CACC,WACA,0CAFD,6CAGE,qBAED,mDACC,aACA,eACA,0CAHD,mDAIE,gBAIF,qDACC,eACA,iBACA,gBACA,gEAJD,qDAKE,gBAED,0CAPD,qDAQE,gBAID,gEADD,6DAEE,gBAED,0CAJD,6DAKE,gBAQJ,8CACC,aACA,qCACA,SACA,0CAJD,8CAKE,eAGF,uCACC,iBACA,6CACC,WACA,UACA,gCACA,gBACA,0CALD,6CAME,aAGF,0DACC,4BAED,iDACC,WACA,iBACA,qBACA,+BACA,uDACC,kCAIH,oCACC,iBACA,0CAFD,oCAGE,gBACA,iBAED,8CACC,iBACA,qBACA,oDACC,kCAMJ,oBACC,yBACA,kBACA,aACA,0CAJD,oBAKE,mBAED,2BACC,4BACA,0CAFD,2BAGE,gBAID,yCACC,2BACA,4CACC,wBACA,kCACA,8BACA,4BACA,cACA,yBACA,gBACA,0CARD,4CASE,gBAMF,gEACC,mBACA,2EACC,gBAED,sEACC,cACA,gBACA,6EACC,iBACA,qBAED,4EACC,sCACA,YAKJ,uDACC,eACA,0BACA,2BAID,+DACC,oCACA,kBACA,aACA,mBACA,0CALD,+DAME,UACA,gCAID,6DACC,eACA,2BACA,0BACA,eAED,6DACC,eACA,iEAFD,6DAGE,iBAGD,0CAND,6DAOE,iBAID,gFACC,yBAID,gJACC,iBAKA,2EACC,mBACA,iBAKF,2FACC,aACA,mBACA,8BACA,mBACA,kGACC,qBAGF,6EACC,gBAED,uFACC,kBACA,6FACC,wBACA,kBAKF,0EACC,eACA,gBACA,yBAKF,iFACC,2CACA,mBACA,oBACA,8FACC,mBACA,oGACC,eACA,cACA,0CAHD,oGAIE,gBAED,2GACC,+BAED,0GACC,YACA,gCACA,cACA,yBACA,gBACA,gBACA,uBACA,+BACA,YAGF,sIACC,sCAGF,mFACC,kBAKJ,kCACC,WACA,kBAEC,+CACC,sBAOH,8BACC,4BAIC,8CACC,2BAGF,yCACC,YACA,gCACA,eAED,4CACC,gCACA,kBACA,kCAED,0CACC,gCAED,+CACC,mBACA,qDACC,MACA,OACA,oBACA,cAEA,2DACC,iBACA,QACA,WACA,WAIH,qDACC,mBAEC,uEACA,iBACA,eACA,eACA,mBACA,WACA,cACA,0CAPA,uEAQC,gBAED,0CAVA,uEAWC,gBAED,8EACC,UACA,QACA,QAED,6EACC,UACA,UACA,SAGF,uEACC,eAID,kEACC,aACA,iBACA,gBAQJ,sBACC,mBACA,uCACC,mBAED,kCACC,sCACA,kBACA,kBACA,oCACC,iBAED,oCACC,gBACA,6BACA,sCACC,wBACA,gBACA,gBAED,0CACC,2BAIH,mCACC,aACA,gBAED,4CACC,oCACA,mBACA,aACA,8CACC,8BACA,mBAED,kDACC,oCACA,WACA,YACA,kBACA,mBACA,6BAGD,sDACC,iBACA,kBACA,gCACA,4DACC,kCAIH,2CACC,oCACA,mBACA,aACA,6CACC,mBAED,iDACC,gCAED,qDACC,WACA,kBC7mBH,kBACC,eACA,MACA,SACA,OACA,QACA,YACA,WACA,gBAGD,eACC,eACA,MACA,OACA,QACA,mBACA,UACA,0CAPD,eAQE,mBAED,0CAVD,eAWE,aACA,mBAED,0BACC,qBACA,0CAFD,0BAGE,oBAGF,kCACC,aACA,mBACA,yBACA,0CAJD,kCAKE,uBACA,uBAED,oCACC,gBACA,cACA,8BACA,4BAED,+CACC,iBACG,gEAFJ,+CAGK,kBAED,0CALJ,+CAMK,cACA,iBAIN,yBACC,WACA,YACA,iBACA,yBACA,kBACA,eACA,cACA,kBACA,6BAEA,+BACC,sCACA,kCACA,yBACA,iCACC,6BAMJ,oBACC,iBACA,aACA,2BACA,iEAJD,oBAKE,mBAED,0CAPD,oBAQE,cAIF,uBACC,WACA,2BACA,gBACA,aACA,yBACA,0CAND,uBAOE,uBACA,kBACA,kBAED,0CAXD,uBAYE,iBACA,4BAIF,kBACC,gBACA,WACA,aACA,+BACC,oBAED,yBACC,mBAED,oBACC,wBAED,8BACC,mBACA,yCACC,gBAGF,gCACC,YACA,gCACA,eACA,wBAED,8BACC,WAED,8BACC,eACA,2BACA,6BACA,oCACC,wBAMF,4CACC,mBACA,mDACC,mBAED,uDACC,mBACA,2DACC,kBAGF,0DACC,eACA,wBACA,4BAIF,kCACC,kBACA,2CAFD,kCAGE,gBAKH,sBACC,oCACA,uBACA,kBACA,gEAJD,sBAKE,wBAED,0CAPD,sBAQE,oBAED,gCACC,mBAED,gCACC,gBACA,wBACA,8BACA,kBACA,kBACA,0BACA,kBACA,6BACA,YACA,WACA,gEAXD,gCAYE,2BAED,kCACC,kBACA,SACA,UACA,eACA,gEALD,kCAME,WAGF,6EAEC,sCACA,2BAMF,uCACC,yBACA,eACA,mBACA,4CACC,0BAED,yCACC,2BACA,6BACA,+CACC,2BAIH,2BACC,8BAKD,6BACC,kCACA,mCACC,sCACA,sCACC,YACA,kBACA,sBACA,eACA,4BACA,0BACA,kDACC,0BAED,iDACC,0BACA,mBACA,iBAED,2CAfD,sCAgBE,gBAIH,mCACC,gBAEC,kFACC,kBACA,sBACA,4BACA,8BACA,2BACA,gBACA,6CACA,wGACC,iBAGF,yCACC,2BAED,yCACC,gBAGA,0GACC,gBAIH,6CACC,iBACA,mCACA,+BACA,wBACA,oDACC,aAED,mDACC,aAED,mDACC,sCACA,kCACA,yBAQJ,qCACC,8BACA,cAED,uCACC,2CACA,oBACA,mBAED,+BACC,4BACA,cACA,iEAHD,+BAIE,gBAGF,sCACC,8BACA,cACA,6BACA,4CACC,2BAKD,8BACC,8BACA,kCACA,cACA,gCAOD,kDACC,YACA,kBACA,gCACA,wBAGA,wDACC,0BACA,UCpWA,iCACI,6BACA,iBACA,6CACI,wBACA,gBACA,cASR,yBACI,mBACA,2BACI,cACA,4BACA,+BACI,WACA,4BAKR,8BACI,gBACA,gBAKJ,yBACI,mBAGA,gDACI,oBAKR,4BACI,mBACA,4CACA,4BACA,aACA,0CALJ,4BAMQ,mBAKR,kCACI,mBACA,4CACA,4BACA,uBACA,0CALJ,kCAMQ,wBAII,6DACI,gBACA,iBACA,eACA,kBACA,0CALJ,6DAMQ,gBAEJ,0CARJ,6DASQ,gBAWpB,sCACI,kBACA,kBACA,4CACI,WACA,YACA,WACA,oCACA,WACA,kBACA,kBACA,MACA,OACA,QACA,SACA,UAEJ,mDACI,kBACA,QACA,OACA,QACA,2BACA,UACA,6DACI,aACA,YACA,aACA,mBACA,uBACA,gCACA,kBACA,cACA,eACA,yBACA,6BACA,mEACI,sBAEJ,0CAfJ,6DAgBQ,YACA,WACA,gBAOpB,6BACI,aACA,mBACA,gCACA,oBACA,0CALJ,6BAMQ,wBAEJ,wCACI,gBACA,mBACA,iBAEJ,wCACI,YACA,kBACA,gBACA,gBACA,gBACA,iEANJ,wCAOQ,WACA,kBACA,gBAEJ,0CAXJ,wCAYQ,WACA,gBAEJ,0CACI,kBACA,gBACA,8CACI,WACA,kBACA,eACA,mBAIZ,sCACI,OACA,6CACI,eACA,mBACA,0CAHJ,6CAIQ,gBAEJ,+CACI,oBACA,qBACA,4BACA,gBACA,uBAGR,sDACI,gBAKA,kDACI,qBAWpB,gBACI,aACA,mBACA,oCACI,eACA,gBACA,kBACA,WACA,wCACI,mBACA,WACA,YACA,mBAIJ,iDACI,kBACA,eAQZ,gBACI,aACA,eACA,UACA,SACA,gBACA,eACA,mBACI,wBACA,eACA,eACA,kBACA,aACA,gBACA,0BACI,kBACA,WACA,mBACA,UACA,YACA,QACA,QACA,2BAGA,qCACI,aAShB,WACI,yBACA,kBACA,aACA,sBACI,mBACA,gBACA,kBACA,kBACA,0BACI,eAEJ,qCACI,kBACA,YACA,WACA,uCACI,sCACA,sCACA,2BACA,qCACA,iBACA,kBACA,yBACA,eAKR,2BACI,mBAEJ,8BACI,UACA,mBACA,2BACA,gCACI,iBACA,QACA,2BACA,6BAEJ,oCACI,WACA,WACA,QACA,sCACA,kBACA,SACA,QACA,6BAEJ,oCACI,2BACA,0CACI,WACA,OAEJ,sCACI,2BAOR,gCACI,qBC/UZ,gCACI,eACA,2CAFJ,gCAGQ,WAKA,kEACI,kBAMJ,sDACI,cACA,gBAMhB,qBACI,iBACA,0CAFJ,qBAGQ,kBAEJ,mCACI,gBACA,cAKJ,iCACI,mBACA,uCACI,WAGR,iCACI,kDACA,mBACA,oBAKR,4DAEI,wBAIA,0BACI,WACA,kBACA,UACA,kDACA,UACA,SACA,WACA,YAIR,kBACI,aACA,mBACA,eACA,0CAJJ,kBAKQ,gBAIR,iBACI,YACA,YACA,sBACA,yBACA,mBACA,kBAEI,qCACI,kBAKZ,2CAEI,mBACA,0CAHJ,2CAIQ,oBAKJ,2CACI,qBACA,mBACA,kBACA,2CAJJ,2CAKQ,oBAGR,0CACI,sBACA,mBACA,0CAHJ,0CAIQ,mBACA,oBAKZ,YACI,qBACA,UCvHJ,yBACI,aACA,8BACA,mBACA,eACA,0CALJ,yBAMQ,eAGA,0CADJ,6CAEQ,iBAUZ,iBACI,gBACA,wBACI,mBAEJ,gCACI,wBACA,mBAEJ,+CACI,mBAYA,0BAEI,kBACA,0CAHJ,0BAIQ,mBAIZ,uBACI,aACA,gBACA,uCACI,eACA,aACA,oDACI,mBACA,eACA,kBACA,wDACI,mBACA,WAIZ,kCACI,iBACA,kBAIY,kEACI,2BAEJ,iEACI,2BAMpB,qCACI,aACA,mBACA,kBACA,eAEJ,mCACI,2BACA,eACA,iBAII,wDACI,eACA,iBACA,aACA,2BACA,gBACA,iBACA,kBACA,gBACA,iBAIQ,sFACI,2BAEJ,qFACI,2BAIZ,8DACI,2BAEJ,gEACI,kBACA,WACA,QACA,2BACA,UACA,UACA,WACA,gCACA,mBAQxB,kBACI,kBACA,UACA,UACA,oBACA,UACA,gBACA,eC/IA,0CADJ,mBAEQ,iBAGA,mDACI,wBAKZ,oBACI,yBACA,kBACA,aACA,kCACI,gBACA,mBACA,wBAEJ,0CATJ,oBAUQ,cASR,UACI,aACA,eACA,YACA,YACI,2CACA,yCACA,wBACA,YACA,eACA,WACA,qBACA,iBACA,oBAEA,kCAEA,kBACI,gCACA,WACA,kCAUZ,8BAEI,kBACA,0CACI,YACA,yBACA,yBACA,eACA,2BACA,kBACA,eACA,4BACA,kCAEJ,4DACI,kBACA,UACA,QACA,2BACA,cACA,UACA,+BACA,WACA,gEACI,wBACA,gBAcA,oBACI,2BACA,qBAEA,cACA,0BACI,2BAIJ,gCACI,eAGR,8BACI,eAEJ,uBACI,eACA,qBACA,gBACA,eAUZ,mBACI,gBACA,gBAEI,8BACI,eACA,cACA,6BAEJ,wBACI,cACA,6BAGA,oCACI,2BAEJ,8BACI,yBAWhB,gCACI,kBACA,YACA,eCjKR,YACI,oBACA,kBACA,0CAHJ,YAIQ,qBAGA,iCACI,kBACA,MACA,aACA,WACA,yBACA,WACA,OACA,QACA,cACA,oBAUZ,oBACI,mBACA,kCACI,eACA,gBACA,wBACA,mBAGA,8BACI,eAEI,6CACI,aAIZ,8BACI,gBACA,iCACI,gBACA,mBACA,mCACI,wBACA,gBACA,eACA,eACA,kBACA,yCACI,WACA,WACA,QACA,oCACA,kBACA,YACA,QACA,UACA,eAEJ,yCACI,2BACA,+CACI,WACA,UACA,OASpB,8BACG,YACA,WAGP,uCACI,gBACA,0CACI,kBACA,kBACA,4CACI,4BACA,2BACA,kDACI,aAGR,4CACI,kBACA,kBACA,QACA,OAWhB,mBACI,eACA,+BAEI,aACA,aACA,kCACI,aACA,kBACA,wBACA,gBACA,eACA,yCACI,kBACA,WACA,iCACA,UACA,WACA,mBACA,WACA,QACA,2BACA,0CAVJ,yCAWQ,cAGR,oCACI,wBACA,gBACA,eACA,wBACA,eACA,kBACA,0CACI,WACA,WACA,QACA,oCACA,kBACA,YACA,QACA,UACA,eAEJ,0CACI,2BACA,gDACI,WACA,UACA,OAKhB,oDACI,mBACA,uDACI,kBACA,8DACI,aAKhB,iCACI,kBACA,wCACI,kBACA,WACA,yBACA,oBACA,WACA,aACA,OACA,QACA,cACA,MAKA,mDACI,wBACA,eACA,gBACA,qBACA,iBACA,wBACA,0CAPJ,mDAQQ,eAMb,2CADH,mCAEQ,mBAEL,0CAJH,mCAKQ,sBACA,oBAGJ,yCACI,iBACA,0CAFJ,yCAGQ,mBAGA,mDACI,aASpB,aACI,sCAEI,mCACI,cAIJ,6BACI,cACA,mCACI,yBAKR,+CACI,cAKQ,gDACI,cACA,sDACI,yBAShB,oDACI,yBAIJ,+CACI,cACA,qDACI,mBAEJ,iDACI,cACA,uDACI,yBAMZ,gEACI,cASZ,4BACI,iBAEI,gDACI,yBACA,WAKR,kDACI,gBAGA,+DACI,aACA,mBACA,gBACA,wEACI,kBAEI,iEADJ,4EAEQ,aAIZ,yEACI,OACA,2EACI,mBACA,cACA,sFACI,gBAWR,mEACI,aAKR,uEACI,aACA,2EACG,YAMX,2CADJ,iDAEQ,uBACA,aACA,iBAEJ,mDACI,eACA,wBACA,mBACA,yDACI,YACA,WAEJ,yDACI,yBAShB,4BACI,mBAEJ,oCACI,6CACA,iBACA,+CACI,oBAGR,+BACI,eAEJ,gCACI,aACA,mBACA,uCACI,yBACA,gBACA,mBACA,iBACA,eACA,gBACA,eAEJ,mCACI,2CACA,gBACA,kBACA,gBACA,YACA,sCACI,kBACA,qBACA,YACA,WACA,iBACA,kBACA,oCACA,WACA,0CACI,eAMZ,yBADJ,oCAEQ,iBAEJ,uCACI,gBACA,sCACA,0CACI,+BACA,4CACI,YACA,WACA,iBACA,sCACA,kBACA,cACA,kBACA,eACA,yBACA,kDACI,wCAMpB,gCACI,0CAGQ,iDACI,gBAQhB,gCACI,aACA,mBACA,8CACI,4CACA,mBACA,kBACA,eACA,gBACA,eACA,gBACA,iBAEJ,uCACI,OACA,0CACI,gBACA,6CACI,cACA,yDACI,aAEJ,wDACI,gBAEJ,+CACI,eACA,cACA,kCAMpB,6CACI,mBAEI,gEACI,yBACA,YACA,0BACA,eACA,YACA,cACA,sEACI,yBACA,gBACA,cAEJ,6EACI,cAEA,UAEJ,sFAEI,cAEJ,uFAEI,cAGR,iEACI,WACA,sCACA,eACA,gBACA,0BACA,yBACA,eACA,uEACI,wCAIZ,2DACI,yBACA,eACA,kBAEJ,+CACI,cACA,eACA,mBACA,oDACI,cAUhB,mBACI,eACA,QACA,SACA,uDACA,YACA,kBACA,UACA,eACA,qCACI,oCACA,kBACA,aACA,YACA,aACA,aACA,sBACA,cACA,gEACA,4BACA,iCACA,0CAZJ,qCAaQ,WACA,kBACA,iBACA,uBAEJ,0CAlBJ,qCAmBQ,kBACA,kBAEJ,kDACI,YACA,WACA,eACA,yBACA,sCACA,kBACA,kBACA,UACA,YACA,wDACI,wCAEJ,0CAbJ,kDAcQ,YACA,WACA,eACA,UACA,aAEJ,0CApBJ,kDAqBQ,UACA,aAGR,gDACI,kBACA,UACA,aACA,mBACA,uEACI,gBACA,0CAFJ,uEAGQ,iBAEJ,8EACI,eACA,gBACA,0CAHJ,8EAIQ,gBAIZ,2DACI,mBACA,8EACI,sCAKJ,4DACI,eACA,gBACA,kBACA,QAMpB,wBACI,mBACA,UACA,yCACA,wDC5oBJ,2BACI,gBAGJ,kBACI,eACA,0CAFJ,kBAGQ,gBAIR,wBACI,oBACA,0CAFJ,wBAGQ,qBAIR,mBACI,oBACA,0CAFJ,mBAGQ,qBAIR,wBACI,oBACA,gEAFJ,wBAGQ,qBAEJ,0CALJ,wBAMQ,qBAMJ,gEAFJ,iBAGQ,qBAEJ,0CALJ,iBAMQ,qBAGR,qBACI,iBACA,gEAFJ,qBAGQ,kBAEJ,0CALJ,qBAMQ,kBAMA,+CACI,qBACA,kBAEA,gEAJJ,+CAKQ,iBACA,qBAEJ,0CARJ,+CASQ,iBACA,qBAMhB,OACI,0BAEJ,OACI,2BAEJ,OACI,yBAEJ,OACI,4BAEJ,OACI,0BAEJ,OACI,yBAEJ,OACI,wBAEJ,OACI,2BAGJ,WACI,gBACA,gEAFJ,WAGQ,gBAEJ,0CALJ,WAMQ,gBAKJ,sBACA,sBACA,uBACA,0BACA,wBACA,yBACA,sBACA,yBACA,wBACA,uBATA,wBACA,wBACA,yBACA,4BACA,0BACA,2BACA,wBACA,2BACA,0BACA,yBATA,wBACA,wBACA,yBACA,4BACA,0BACA,2BACA,wBACA,2BACA,0BACA,yBATA,wBACA,wBACA,yBACA,4BACA,0BACA,2BACA,wBACA,2BACA,0BACA,yBATA,wBACA,wBACA,yBACA,4BACA,0BACA,2BACA,wBACA,2BACA,0BACA,yBATA,wBACA,wBACA,yBACA,4BACA,0BACA,2BACA,wBACA,2BACA,0BACA,yBATA,wBACA,wBACA,yBACA,4BACA,0BACA,2BACA,wBACA,2BACA,0BACA,yBATA,wBACA,wBACA,yBACA,4BACA,0BACA,2BACA,wBACA,2BACA,0BACA,yBATA,wBACA,wBACA,yBACA,4BACA,0BACA,2BACA,wBACA,2BACA,0BACA,yBATA,wBACA,wBACA,yBACA,4BACA,0BACA,2BACA,wBACA,2BACA,0BACA,yBATA,wBACA,wBACA,yBACA,4BACA,0BACA,2BACA,wBACA,2BACA,0BACA,yBATA,wBACA,wBACA,yBACA,4BACA,0BACA,2BACA,wBACA,2BACA,0BACA,yBATA,wBACA,wBACA,yBACA,4BACA,0BACA,2BACA,wBACA,2BACA,0BACA,yBATA,wBACA,wBACA,yBACA,4BACA,0BACA,2BACA,wBACA,2BACA,0BACA,yBATA,wBACA,wBACA,yBACA,4BACA,0BACA,2BACA,wBACA,2BACA,0BACA,yBATA,wBACA,wBACA,yBACA,4BACA,0BACA,2BACA,wBACA,2BACA,0BACA,yBATA,wBACA,wBACA,yBACA,4BACA,0BACA,2BACA,wBACA,2BACA,0BACA,yBATA,wBACA,wBACA,yBACA,4BACA,0BACA,2BACA,wBACA,2BACA,0BACA,yBATA,wBACA,wBACA,yBACA,4BACA,0BACA,2BACA,wBACA,2BACA,0BACA,yBATA,0BACA,0BACA,2BACA,8BACA,4BACA,6BACA,0BACA,6BACA,4BACA,2BATA,0BACA,0BACA,2BACA,8BACA,4BACA,6BACA,0BACA,6BACA,4BACA,2BATA,0BACA,0BACA,2BACA,8BACA,4BACA,6BACA,0BACA,6BACA,4BACA,2BATA,0BACA,0BACA,2BACA,8BACA,4BACA,6BACA,0BACA,6BACA,4BACA,2BATA,0BACA,0BACA,2BACA,8BACA,4BACA,6BACA,0BACA,6BACA,4BACA,2BATA,0BACA,0BACA,2BACA,8BACA,4BACA,6BACA,0BACA,6BACA,4BACA,2BATA,0BACA,0BACA,2BACA,8BACA,4BACA,6BACA,0BACA,6BACA,4BACA,2BATA,0BACA,0BACA,2BACA,8BACA,4BACA,6BACA,0BACA,6BACA,4BACA,2BATA,0BACA,0BACA,2BACA,8BACA,4BACA,6BACA,0BACA,6BACA,4BACA,2BATA,0BACA,0BACA,2BACA,8BACA,4BACA,6BACA,0BACA,6BACA,4BACA,2BATA,0BACA,0BACA,2BACA,8BACA,4BACA,6BACA,0BACA,6BACA,4BACA,2BATA,0BACA,0BACA,2BACA,8BACA,4BACA,6BACA,0BACA,6BACA,4BACA,2BATA,0BACA,0BACA,2BACA,8BACA,4BACA,6BACA,0BACA,6BACA,4BACA,2BATA,0BACA,0BACA,2BACA,8BACA,4BACA,6BACA,0BACA,6BACA,4BACA,2BATA,0BACA,0BACA,2BACA,8BACA,4BACA,6BACA,0BACA,6BACA,4BACA,2BATA,0BACA,0BACA,2BACA,8BACA,4BACA,6BACA,0BACA,6BACA,4BACA,2BATA,0BACA,0BACA,2BACA,8BACA,4BACA,6BACA,0BACA,6BACA,4BACA,2BATA,0BACA,0BACA,2BACA,8BACA,4BACA,6BACA,0BACA,6BACA,4BACA,2BATA,0BACA,0BACA,2BACA,8BACA,4BACA,6BACA,0BACA,6BACA,4BACA,2BATA,0BACA,0BACA,2BACA,8BACA,4BACA,6BACA,0BACA,6BACA,4BACA,2BATA,0BACA,0BACA,2BACA,8BACA,4BACA,6BACA,0BACA,6BACA,4BACA,2BAGJ,2CACI,WACI,eAIR,kEAEQ,WACI,cAGJ,WACI,cAGJ,UACI,gBAGJ,UACI,mBAGJ,UACI,iBAGJ,UACI,kBAGJ,UACI,eAGJ,UACI,kBA7BJ,YACI,eAGJ,YACI,eAGJ,WACI,iBAGJ,WACI,oBAGJ,WACI,kBAGJ,WACI,mBAGJ,WACI,gBAGJ,WACI,mBA7BJ,YACI,eAGJ,YACI,eAGJ,WACI,iBAGJ,WACI,oBAGJ,WACI,kBAGJ,WACI,mBAGJ,WACI,gBAGJ,WACI,mBA7BJ,YACI,eAGJ,YACI,eAGJ,WACI,iBAGJ,WACI,oBAGJ,WACI,kBAGJ,WACI,mBAGJ,WACI,gBAGJ,WACI,mBA7BJ,YACI,eAGJ,YACI,eAGJ,WACI,iBAGJ,WACI,oBAGJ,WACI,kBAGJ,WACI,mBAGJ,WACI,gBAGJ,WACI,mBA7BJ,YACI,eAGJ,YACI,eAGJ,WACI,iBAGJ,WACI,oBAGJ,WACI,kBAGJ,WACI,mBAGJ,WACI,gBAGJ,WACI,mBA7BJ,YACI,eAGJ,YACI,eAGJ,WACI,iBAGJ,WACI,oBAGJ,WACI,kBAGJ,WACI,mBAGJ,WACI,gBAGJ,WACI,mBA7BJ,YACI,eAGJ,YACI,eAGJ,WACI,iBAGJ,WACI,oBAGJ,WACI,kBAGJ,WACI,mBAGJ,WACI,gBAGJ,WACI,mBA7BJ,YACI,eAGJ,YACI,eAGJ,WACI,iBAGJ,WACI,oBAGJ,WACI,kBAGJ,WACI,mBAGJ,WACI,gBAGJ,WACI,mBA7BJ,YACI,eAGJ,YACI,eAGJ,WACI,iBAGJ,WACI,oBAGJ,WACI,kBAGJ,WACI,mBAGJ,WACI,gBAGJ,WACI,mBA7BJ,YACI,eAGJ,YACI,eAGJ,WACI,iBAGJ,WACI,oBAGJ,WACI,kBAGJ,WACI,mBAGJ,WACI,gBAGJ,WACI,mBA7BJ,YACI,eAGJ,YACI,eAGJ,WACI,iBAGJ,WACI,oBAGJ,WACI,kBAGJ,WACI,mBAGJ,WACI,gBAGJ,WACI,mBA7BJ,YACI,eAGJ,YACI,eAGJ,WACI,iBAGJ,WACI,oBAGJ,WACI,kBAGJ,WACI,mBAGJ,WACI,gBAGJ,WACI,mBA7BJ,YACI,eAGJ,YACI,eAGJ,WACI,iBAGJ,WACI,oBAGJ,WACI,kBAGJ,WACI,mBAGJ,WACI,gBAGJ,WACI,mBA7BJ,YACI,eAGJ,YACI,eAGJ,WACI,iBAGJ,WACI,oBAGJ,WACI,kBAGJ,WACI,mBAGJ,WACI,gBAGJ,WACI,mBA7BJ,YACI,eAGJ,YACI,eAGJ,WACI,iBAGJ,WACI,oBAGJ,WACI,kBAGJ,WACI,mBAGJ,WACI,gBAGJ,WACI,mBA7BJ,YACI,eAGJ,YACI,eAGJ,WACI,iBAGJ,WACI,oBAGJ,WACI,kBAGJ,WACI,mBAGJ,WACI,gBAGJ,WACI,mBA7BJ,YACI,eAGJ,YACI,eAGJ,WACI,iBAGJ,WACI,oBAGJ,WACI,kBAGJ,WACI,mBAGJ,WACI,gBAGJ,WACI,mBA7BJ,YACI,eAGJ,YACI,eAGJ,WACI,iBAGJ,WACI,oBAGJ,WACI,kBAGJ,WACI,mBAGJ,WACI,gBAGJ,WACI,mBA7BJ,aACI,gBAGJ,aACI,gBAGJ,YACI,kBAGJ,YACI,qBAGJ,YACI,mBAGJ,YACI,oBAGJ,YACI,iBAGJ,YACI,qBAMZ,iEAEQ,WACI,cAEJ,WACI,cAEJ,UACI,gBAEJ,UACI,mBAGJ,UACI,iBAGJ,UACI,kBAGJ,UACI,eAGJ,UACI,kBAEJ,UACI,gBA7BJ,YACI,eAEJ,YACI,eAEJ,WACI,iBAEJ,WACI,oBAGJ,WACI,kBAGJ,WACI,mBAGJ,WACI,gBAGJ,WACI,mBAEJ,WACI,iBA7BJ,YACI,eAEJ,YACI,eAEJ,WACI,iBAEJ,WACI,oBAGJ,WACI,kBAGJ,WACI,mBAGJ,WACI,gBAGJ,WACI,mBAEJ,WACI,iBA7BJ,YACI,eAEJ,YACI,eAEJ,WACI,iBAEJ,WACI,oBAGJ,WACI,kBAGJ,WACI,mBAGJ,WACI,gBAGJ,WACI,mBAEJ,WACI,iBA7BJ,YACI,eAEJ,YACI,eAEJ,WACI,iBAEJ,WACI,oBAGJ,WACI,kBAGJ,WACI,mBAGJ,WACI,gBAGJ,WACI,mBAEJ,WACI,iBA7BJ,YACI,eAEJ,YACI,eAEJ,WACI,iBAEJ,WACI,oBAGJ,WACI,kBAGJ,WACI,mBAGJ,WACI,gBAGJ,WACI,mBAEJ,WACI,iBA7BJ,YACI,eAEJ,YACI,eAEJ,WACI,iBAEJ,WACI,oBAGJ,WACI,kBAGJ,WACI,mBAGJ,WACI,gBAGJ,WACI,mBAEJ,WACI,iBA7BJ,YACI,eAEJ,YACI,eAEJ,WACI,iBAEJ,WACI,oBAGJ,WACI,kBAGJ,WACI,mBAGJ,WACI,gBAGJ,WACI,mBAEJ,WACI,iBA7BJ,YACI,eAEJ,YACI,eAEJ,WACI,iBAEJ,WACI,oBAGJ,WACI,kBAGJ,WACI,mBAGJ,WACI,gBAGJ,WACI,mBAEJ,WACI,iBA7BJ,YACI,eAEJ,YACI,eAEJ,WACI,iBAEJ,WACI,oBAGJ,WACI,kBAGJ,WACI,mBAGJ,WACI,gBAGJ,WACI,mBAEJ,WACI,iBA7BJ,YACI,eAEJ,YACI,eAEJ,WACI,iBAEJ,WACI,oBAGJ,WACI,kBAGJ,WACI,mBAGJ,WACI,gBAGJ,WACI,mBAEJ,WACI,iBA7BJ,YACI,eAEJ,YACI,eAEJ,WACI,iBAEJ,WACI,oBAGJ,WACI,kBAGJ,WACI,mBAGJ,WACI,gBAGJ,WACI,mBAEJ,WACI,iBA7BJ,YACI,eAEJ,YACI,eAEJ,WACI,iBAEJ,WACI,oBAGJ,WACI,kBAGJ,WACI,mBAGJ,WACI,gBAGJ,WACI,mBAEJ,WACI,iBA7BJ,YACI,eAEJ,YACI,eAEJ,WACI,iBAEJ,WACI,oBAGJ,WACI,kBAGJ,WACI,mBAGJ,WACI,gBAGJ,WACI,mBAEJ,WACI,iBA7BJ,YACI,eAEJ,YACI,eAEJ,WACI,iBAEJ,WACI,oBAGJ,WACI,kBAGJ,WACI,mBAGJ,WACI,gBAGJ,WACI,mBAEJ,WACI,iBA7BJ,YACI,eAEJ,YACI,eAEJ,WACI,iBAEJ,WACI,oBAGJ,WACI,kBAGJ,WACI,mBAGJ,WACI,gBAGJ,WACI,mBAEJ,WACI,iBA7BJ,YACI,eAEJ,YACI,eAEJ,WACI,iBAEJ,WACI,oBAGJ,WACI,kBAGJ,WACI,mBAGJ,WACI,gBAGJ,WACI,mBAEJ,WACI,iBA7BJ,YACI,eAEJ,YACI,eAEJ,WACI,iBAEJ,WACI,oBAGJ,WACI,kBAGJ,WACI,mBAGJ,WACI,gBAGJ,WACI,mBAEJ,WACI,iBA7BJ,YACI,eAEJ,YACI,eAEJ,WACI,iBAEJ,WACI,oBAGJ,WACI,kBAGJ,WACI,mBAGJ,WACI,gBAGJ,WACI,mBAEJ,WACI,iBA7BJ,aACI,gBAEJ,aACI,gBAEJ,YACI,kBAEJ,YACI,qBAGJ,YACI,mBAGJ,YACI,oBAGJ,YACI,iBAGJ,YACI,oBAEJ,YACI,mBAMZ,gEAEI,WACI,qBAEJ,UACI,0BAEJ,UACI,2BAEJ,UACI,yBAEJ,UACI,4BAEJ,UACI,0BAEJ,UACI,yBAEJ,UACI,wBAEJ,UACI,2BAEJ,aACI,2BAIA,WACI,cAGJ,WACI,cAGJ,UACI,gBAGJ,UACI,mBAGJ,UACI,iBAGJ,UACI,kBAGJ,UACI,eAGJ,UACI,kBA7BJ,YACI,eAGJ,YACI,eAGJ,WACI,iBAGJ,WACI,oBAGJ,WACI,kBAGJ,WACI,mBAGJ,WACI,gBAGJ,WACI,mBA7BJ,YACI,eAGJ,YACI,eAGJ,WACI,iBAGJ,WACI,oBAGJ,WACI,kBAGJ,WACI,mBAGJ,WACI,gBAGJ,WACI,mBA7BJ,YACI,eAGJ,YACI,eAGJ,WACI,iBAGJ,WACI,oBAGJ,WACI,kBAGJ,WACI,mBAGJ,WACI,gBAGJ,WACI,mBA7BJ,YACI,eAGJ,YACI,eAGJ,WACI,iBAGJ,WACI,oBAGJ,WACI,kBAGJ,WACI,mBAGJ,WACI,gBAGJ,WACI,mBA7BJ,YACI,eAGJ,YACI,eAGJ,WACI,iBAGJ,WACI,oBAGJ,WACI,kBAGJ,WACI,mBAGJ,WACI,gBAGJ,WACI,mBA7BJ,YACI,eAGJ,YACI,eAGJ,WACI,iBAGJ,WACI,oBAGJ,WACI,kBAGJ,WACI,mBAGJ,WACI,gBAGJ,WACI,mBA7BJ,YACI,eAGJ,YACI,eAGJ,WACI,iBAGJ,WACI,oBAGJ,WACI,kBAGJ,WACI,mBAGJ,WACI,gBAGJ,WACI,mBA7BJ,YACI,eAGJ,YACI,eAGJ,WACI,iBAGJ,WACI,oBAGJ,WACI,kBAGJ,WACI,mBAGJ,WACI,gBAGJ,WACI,mBA7BJ,YACI,eAGJ,YACI,eAGJ,WACI,iBAGJ,WACI,oBAGJ,WACI,kBAGJ,WACI,mBAGJ,WACI,gBAGJ,WACI,mBA7BJ,YACI,eAGJ,YACI,eAGJ,WACI,iBAGJ,WACI,oBAGJ,WACI,kBAGJ,WACI,mBAGJ,WACI,gBAGJ,WACI,mBA7BJ,YACI,eAGJ,YACI,eAGJ,WACI,iBAGJ,WACI,oBAGJ,WACI,kBAGJ,WACI,mBAGJ,WACI,gBAGJ,WACI,mBA7BJ,YACI,eAGJ,YACI,eAGJ,WACI,iBAGJ,WACI,oBAGJ,WACI,kBAGJ,WACI,mBAGJ,WACI,gBAGJ,WACI,mBA7BJ,YACI,eAGJ,YACI,eAGJ,WACI,iBAGJ,WACI,oBAGJ,WACI,kBAGJ,WACI,mBAGJ,WACI,gBAGJ,WACI,mBA7BJ,YACI,eAGJ,YACI,eAGJ,WACI,iBAGJ,WACI,oBAGJ,WACI,kBAGJ,WACI,mBAGJ,WACI,gBAGJ,WACI,mBA7BJ,YACI,eAGJ,YACI,eAGJ,WACI,iBAGJ,WACI,oBAGJ,WACI,kBAGJ,WACI,mBAGJ,WACI,gBAGJ,WACI,mBA7BJ,YACI,eAGJ,YACI,eAGJ,WACI,iBAGJ,WACI,oBAGJ,WACI,kBAGJ,WACI,mBAGJ,WACI,gBAGJ,WACI,mBA7BJ,YACI,eAGJ,YACI,eAGJ,WACI,iBAGJ,WACI,oBAGJ,WACI,kBAGJ,WACI,mBAGJ,WACI,gBAGJ,WACI,mBA7BJ,YACI,eAGJ,YACI,eAGJ,WACI,iBAGJ,WACI,oBAGJ,WACI,kBAGJ,WACI,mBAGJ,WACI,gBAGJ,WACI,mBA7BJ,aACI,gBAGJ,aACI,gBAGJ,YACI,kBAGJ,YACI,qBAGJ,YACI,mBAGJ,YACI,oBAGJ,YACI,iBAGJ,YACI,qBAMZ,0CACI,aACI,2BAEJ,WACI,qBAEJ,UACI,0BAEJ,UACI,2BAEJ,UACI,yBAEJ,UACI,4BAEJ,UACI,0BAEJ,UACI,yBAEJ,UACI,wBAEJ,UACI,2BAEJ,YACI,6BAEJ,YACI,gCAGA,WACI,cAEJ,WACI,cAEJ,UACI,gBAGJ,UACI,mBAGJ,UACI,iBAGJ,UACI,kBAGJ,UACI,eAGJ,UACI,gBAGJ,UACI,iBAGJ,UACI,kBAnCJ,YACI,eAEJ,YACI,eAEJ,WACI,iBAGJ,WACI,oBAGJ,WACI,kBAGJ,WACI,mBAGJ,WACI,gBAGJ,WACI,iBAGJ,WACI,kBAGJ,WACI,mBAnCJ,YACI,eAEJ,YACI,eAEJ,WACI,iBAGJ,WACI,oBAGJ,WACI,kBAGJ,WACI,mBAGJ,WACI,gBAGJ,WACI,iBAGJ,WACI,kBAGJ,WACI,mBAnCJ,YACI,eAEJ,YACI,eAEJ,WACI,iBAGJ,WACI,oBAGJ,WACI,kBAGJ,WACI,mBAGJ,WACI,gBAGJ,WACI,iBAGJ,WACI,kBAGJ,WACI,mBAnCJ,YACI,eAEJ,YACI,eAEJ,WACI,iBAGJ,WACI,oBAGJ,WACI,kBAGJ,WACI,mBAGJ,WACI,gBAGJ,WACI,iBAGJ,WACI,kBAGJ,WACI,mBAnCJ,YACI,eAEJ,YACI,eAEJ,WACI,iBAGJ,WACI,oBAGJ,WACI,kBAGJ,WACI,mBAGJ,WACI,gBAGJ,WACI,iBAGJ,WACI,kBAGJ,WACI,mBAnCJ,YACI,eAEJ,YACI,eAEJ,WACI,iBAGJ,WACI,oBAGJ,WACI,kBAGJ,WACI,mBAGJ,WACI,gBAGJ,WACI,iBAGJ,WACI,kBAGJ,WACI,mBAnCJ,YACI,eAEJ,YACI,eAEJ,WACI,iBAGJ,WACI,oBAGJ,WACI,kBAGJ,WACI,mBAGJ,WACI,gBAGJ,WACI,iBAGJ,WACI,kBAGJ,WACI,mBAnCJ,YACI,eAEJ,YACI,eAEJ,WACI,iBAGJ,WACI,oBAGJ,WACI,kBAGJ,WACI,mBAGJ,WACI,gBAGJ,WACI,iBAGJ,WACI,kBAGJ,WACI,mBAnCJ,YACI,eAEJ,YACI,eAEJ,WACI,iBAGJ,WACI,oBAGJ,WACI,kBAGJ,WACI,mBAGJ,WACI,gBAGJ,WACI,iBAGJ,WACI,kBAGJ,WACI,mBAnCJ,YACI,eAEJ,YACI,eAEJ,WACI,iBAGJ,WACI,oBAGJ,WACI,kBAGJ,WACI,mBAGJ,WACI,gBAGJ,WACI,iBAGJ,WACI,kBAGJ,WACI,mBAnCJ,YACI,eAEJ,YACI,eAEJ,WACI,iBAGJ,WACI,oBAGJ,WACI,kBAGJ,WACI,mBAGJ,WACI,gBAGJ,WACI,iBAGJ,WACI,kBAGJ,WACI,mBAnCJ,YACI,eAEJ,YACI,eAEJ,WACI,iBAGJ,WACI,oBAGJ,WACI,kBAGJ,WACI,mBAGJ,WACI,gBAGJ,WACI,iBAGJ,WACI,kBAGJ,WACI,mBAnCJ,YACI,eAEJ,YACI,eAEJ,WACI,iBAGJ,WACI,oBAGJ,WACI,kBAGJ,WACI,mBAGJ,WACI,gBAGJ,WACI,iBAGJ,WACI,kBAGJ,WACI,mBAnCJ,YACI,eAEJ,YACI,eAEJ,WACI,iBAGJ,WACI,oBAGJ,WACI,kBAGJ,WACI,mBAGJ,WACI,gBAGJ,WACI,iBAGJ,WACI,kBAGJ,WACI,mBAnCJ,YACI,eAEJ,YACI,eAEJ,WACI,iBAGJ,WACI,oBAGJ,WACI,kBAGJ,WACI,mBAGJ,WACI,gBAGJ,WACI,iBAGJ,WACI,kBAGJ,WACI,mBAnCJ,YACI,eAEJ,YACI,eAEJ,WACI,iBAGJ,WACI,oBAGJ,WACI,kBAGJ,WACI,mBAGJ,WACI,gBAGJ,WACI,iBAGJ,WACI,kBAGJ,WACI,mBAnCJ,YACI,eAEJ,YACI,eAEJ,WACI,iBAGJ,WACI,oBAGJ,WACI,kBAGJ,WACI,mBAGJ,WACI,gBAGJ,WACI,iBAGJ,WACI,kBAGJ,WACI,mBAnCJ,YACI,eAEJ,YACI,eAEJ,WACI,iBAGJ,WACI,oBAGJ,WACI,kBAGJ,WACI,mBAGJ,WACI,gBAGJ,WACI,iBAGJ,WACI,kBAGJ,WACI,mBAnCJ,aACI,gBAEJ,aACI,gBAEJ,YACI,kBAGJ,YACI,qBAGJ,YACI,mBAGJ,YACI,oBAGJ,YACI,iBAGJ,YACI,kBAGJ,YACI,mBAGJ,YACI,oBAIR,UACI,eAEJ,UACI,gBAEJ,UACI,cAEJ,UACI,iBAEJ,UACI,eAEJ,UACI,cAEJ,UACI,aAEJ,UACI,iBAMR,0CAEQ,eACI,cAEJ,eACI,cAEJ,cACI,gBAEJ,cACI,mBAEJ,cACI,iBAEJ,cACI,kBAEJ,cACI,eAEJ,cACI,kBAtBJ,gBACI,eAEJ,gBACI,eAEJ,eACI,iBAEJ,eACI,oBAEJ,eACI,kBAEJ,eACI,mBAEJ,eACI,gBAEJ,eACI,mBAtBJ,gBACI,eAEJ,gBACI,eAEJ,eACI,iBAEJ,eACI,oBAEJ,eACI,kBAEJ,eACI,mBAEJ,eACI,gBAEJ,eACI,mBAtBJ,gBACI,eAEJ,gBACI,eAEJ,eACI,iBAEJ,eACI,oBAEJ,eACI,kBAEJ,eACI,mBAEJ,eACI,gBAEJ,eACI,mBAtBJ,gBACI,eAEJ,gBACI,eAEJ,eACI,iBAEJ,eACI,oBAEJ,eACI,kBAEJ,eACI,mBAEJ,eACI,gBAEJ,eACI,mBAtBJ,gBACI,eAEJ,gBACI,eAEJ,eACI,iBAEJ,eACI,oBAEJ,eACI,kBAEJ,eACI,mBAEJ,eACI,gBAEJ,eACI,mBAtBJ,gBACI,eAEJ,gBACI,eAEJ,eACI,iBAEJ,eACI,oBAEJ,eACI,kBAEJ,eACI,mBAEJ,eACI,gBAEJ,eACI,mBAtBJ,gBACI,eAEJ,gBACI,eAEJ,eACI,iBAEJ,eACI,oBAEJ,eACI,kBAEJ,eACI,mBAEJ,eACI,gBAEJ,eACI,mBAtBJ,gBACI,eAEJ,gBACI,eAEJ,eACI,iBAEJ,eACI,oBAEJ,eACI,kBAEJ,eACI,mBAEJ,eACI,gBAEJ,eACI,mBAtBJ,gBACI,eAEJ,gBACI,eAEJ,eACI,iBAEJ,eACI,oBAEJ,eACI,kBAEJ,eACI,mBAEJ,eACI,gBAEJ,eACI,mBAtBJ,gBACI,eAEJ,gBACI,eAEJ,eACI,iBAEJ,eACI,oBAEJ,eACI,kBAEJ,eACI,mBAEJ,eACI,gBAEJ,eACI,mBAtBJ,gBACI,eAEJ,gBACI,eAEJ,eACI,iBAEJ,eACI,oBAEJ,eACI,kBAEJ,eACI,mBAEJ,eACI,gBAEJ,eACI,mBAtBJ,gBACI,eAEJ,gBACI,eAEJ,eACI,iBAEJ,eACI,oBAEJ,eACI,kBAEJ,eACI,mBAEJ,eACI,gBAEJ,eACI,mBAtBJ,gBACI,eAEJ,gBACI,eAEJ,eACI,iBAEJ,eACI,oBAEJ,eACI,kBAEJ,eACI,mBAEJ,eACI,gBAEJ,eACI,mBAtBJ,gBACI,eAEJ,gBACI,eAEJ,eACI,iBAEJ,eACI,oBAEJ,eACI,kBAEJ,eACI,mBAEJ,eACI,gBAEJ,eACI,mBAtBJ,gBACI,eAEJ,gBACI,eAEJ,eACI,iBAEJ,eACI,oBAEJ,eACI,kBAEJ,eACI,mBAEJ,eACI,gBAEJ,eACI,mBAtBJ,gBACI,eAEJ,gBACI,eAEJ,eACI,iBAEJ,eACI,oBAEJ,eACI,kBAEJ,eACI,mBAEJ,eACI,gBAEJ,eACI,mBAtBJ,gBACI,eAEJ,gBACI,eAEJ,eACI,iBAEJ,eACI,oBAEJ,eACI,kBAEJ,eACI,mBAEJ,eACI,gBAEJ,eACI,mBAtBJ,gBACI,eAEJ,gBACI,eAEJ,eACI,iBAEJ,eACI,oBAEJ,eACI,kBAEJ,eACI,mBAEJ,eACI,gBAEJ,eACI,mBAtBJ,iBACI,gBAEJ,iBACI,gBAEJ,gBACI,kBAEJ,gBACI,qBAEJ,gBACI,mBAEJ,gBACI,oBAEJ,gBACI,iBAEJ,gBACI", "file": "../scss/style.min.css", "sourcesContent": ["/*----------------------\r\nShortcode\r\n-----------------------*/\r\n/*==============================\r\n *  Utilities\r\n=================================*/\r\n\r\n@import url('https://fonts.googleapis.com/css2?family=DM+Sans:ital,wght@0,400;0,500;0,700;1,400&family=Poppins:wght@400;500;600;700&display=swap');\r\n\r\n.clearfix:before,\r\n.clearfix:after {\r\n    content: \" \";\r\n    display: table;\r\n}\r\n\r\n.clearfix:after {\r\n    clear: both;\r\n}\r\n\r\n.fix {\r\n    overflow: hidden;\r\n}\r\n\r\n.slick-initialized .slick-slide {\r\n    margin-bottom: -10px;\r\n}\r\n\r\n\r\n/*===============================\r\n    Background Color \r\n=================================*/\r\n\r\n@include config-bg-colors('bg-color-',\r\n'primary'var(--color-primary),\r\n'secondary'var(--color-secondary),\r\n'tertiary'var(--color-tertiary),\r\n'grey'#F0F2F5,\r\n'white'#FFFFFF,\r\n'dark'var(--color-dark),\r\n'lighter'var(--color-lighter));\r\n\r\n/*===========================\r\n    Background Image \r\n=============================*/\r\n\r\n.bg_image {\r\n    @extend %bgImagePosition;\r\n}\r\n\r\n@for $i from 1 through 20 {\r\n    .bg_image--#{$i} {\r\n        background-image: url(../images/bg/bg-image-#{$i}.jpg);\r\n    }\r\n}\r\n\r\n\r\n/* Height and width */\r\n\r\n.fullscreen {\r\n    min-height: 100vh;\r\n    width: 100%;\r\n}\r\n\r\n.flex-center {\r\n    display: flex;\r\n    align-items: center;\r\n}\r\n\r\n.require {\r\n    color: var(--color-chart03);\r\n}\r\n\r\n\r\n/*===================\r\nCustom Row\r\n======================*/\r\n\r\n.container, \r\n.container-fluid, \r\n.container-lg, \r\n.container-md, \r\n.container-sm, \r\n.container-xl, \r\n.container-xxl {\r\n    padding-left: 15px;\r\n    padding-right: 15px;\r\n}\r\n\r\n.row {\r\n    margin-right: -15px;\r\n    margin-left: -15px;\r\n    &>[class*=\"col\"] {\r\n        padding-left: 15px;\r\n        padding-right: 15px;\r\n    }\r\n}\r\n\r\n\r\n.row--0 {\r\n    margin-left: -0px;\r\n    margin-right: -0px;\r\n    &>[class*=\"col\"] {\r\n        padding-left: 0px;\r\n        padding-right: 0px;\r\n    }\r\n}\r\n\r\n.row--5 {\r\n    margin-left: -5px;\r\n    margin-right: -5px;\r\n    &>[class*=\"col\"] {\r\n        padding-left: 5px;\r\n        padding-right: 5px;\r\n    }\r\n}\r\n\r\n.row--10 {\r\n    margin-left: -10px;\r\n    margin-right: -10px;\r\n    &>[class*=\"col\"] {\r\n        padding-left: 10px;\r\n        padding-right: 10px;\r\n    }\r\n}\r\n\r\n.row--20 {\r\n    margin-left: -20px;\r\n    margin-right: -20px;\r\n    // Responsive\r\n    @media #{$laptop-device} {\r\n        margin-left: -15px;\r\n        margin-right: -15px;\r\n    }\r\n    @media #{$lg-layout} {\r\n        margin-left: -15px;\r\n        margin-right: -15px;\r\n    }\r\n    @media #{$md-layout} {\r\n        margin-left: -15px;\r\n        margin-right: -15px;\r\n    }\r\n    @media #{$sm-layout} {\r\n        margin-left: -15px !important;\r\n        margin-right: -15px !important;\r\n    }\r\n    &>[class*=\"col\"],\r\n    &>[class*=\"col-\"] {\r\n        padding-left: 20px;\r\n        padding-right: 20px;\r\n        // Responsive\r\n        @media #{$laptop-device} {\r\n            padding-left: 15px;\r\n            padding-right: 15px;\r\n        }\r\n        @media #{$lg-layout} {\r\n            padding-left: 15px;\r\n            padding-right: 15px;\r\n        }\r\n        @media #{$md-layout} {\r\n            padding-left: 15px !important;\r\n            padding-right: 15px !important;\r\n        }\r\n        @media #{$sm-layout} {\r\n            padding-left: 15px !important;\r\n            padding-right: 15px !important;\r\n        }\r\n    }\r\n}\r\n\r\n.row--25 {\r\n    margin-left: -25px;\r\n    margin-right: -25px;\r\n    // Responsive\r\n    @media #{$laptop-device} {\r\n        margin-left: -15px;\r\n        margin-right: -15px;\r\n    }\r\n    @media #{$lg-layout} {\r\n        margin-left: -15px;\r\n        margin-right: -15px;\r\n    }\r\n    @media #{$md-layout} {\r\n        margin-left: -15px;\r\n        margin-right: -15px;\r\n    }\r\n    @media #{$sm-layout} {\r\n        margin-left: -15px !important;\r\n        margin-right: -15px !important;\r\n    }\r\n    &>[class*=\"col\"],\r\n    &>[class*=\"col-\"] {\r\n        padding-left: 25px;\r\n        padding-right: 25px;\r\n        // Responsive\r\n        @media #{$laptop-device} {\r\n            padding-left: 15px;\r\n            padding-right: 15px;\r\n        }\r\n        @media #{$lg-layout} {\r\n            padding-left: 15px;\r\n            padding-right: 15px;\r\n        }\r\n        @media #{$md-layout} {\r\n            padding-left: 15px !important;\r\n            padding-right: 15px !important;\r\n        }\r\n        @media #{$sm-layout} {\r\n            padding-left: 15px !important;\r\n            padding-right: 15px !important;\r\n        }\r\n    }\r\n}\r\n\r\n.row--30 {\r\n    margin-left: -30px;\r\n    margin-right: -30px;\r\n    // Responsive\r\n    @media #{$laptop-device} {\r\n        margin-left: -15px;\r\n        margin-right: -15px;\r\n    }\r\n    @media #{$lg-layout} {\r\n        margin-left: -15px;\r\n        margin-right: -15px;\r\n    }\r\n    @media #{$md-layout} {\r\n        margin-left: -15px;\r\n        margin-right: -15px;\r\n    }\r\n    @media #{$sm-layout} {\r\n        margin-left: -15px !important;\r\n        margin-right: -15px !important;\r\n    }\r\n    &>[class*=\"col\"],\r\n    &>[class*=\"col-\"] {\r\n        padding-left: 30px;\r\n        padding-right: 30px;\r\n        // Responsive\r\n        @media #{$laptop-device} {\r\n            padding-left: 15px;\r\n            padding-right: 15px;\r\n        }\r\n        @media #{$lg-layout} {\r\n            padding-left: 15px;\r\n            padding-right: 15px;\r\n        }\r\n        @media #{$md-layout} {\r\n            padding-left: 15px !important;\r\n            padding-right: 15px !important;\r\n        }\r\n        @media #{$sm-layout} {\r\n            padding-left: 15px !important;\r\n            padding-right: 15px !important;\r\n        }\r\n    }\r\n}\r\n\r\n.row--45 {\r\n    margin-left: -45px;\r\n    margin-right: -45px;\r\n    // Responsive\r\n    @media #{$laptop-device} {\r\n        margin-left: -15px;\r\n        margin-right: -15px;\r\n    }\r\n    @media #{$lg-layout} {\r\n        margin-left: -15px;\r\n        margin-right: -15px;\r\n    }\r\n    @media #{$md-layout} {\r\n        margin-left: -15px;\r\n        margin-right: -15px;\r\n    }\r\n    @media #{$sm-layout} {\r\n        margin-left: -15px !important;\r\n        margin-right: -15px !important;\r\n    }\r\n    &>[class*=\"col\"],\r\n    &>[class*=\"col-\"] {\r\n        padding-left: 45px;\r\n        padding-right: 45px;\r\n        // Responsive\r\n        @media #{$laptop-device} {\r\n            padding-left: 15px;\r\n            padding-right: 15px;\r\n        }\r\n        @media #{$lg-layout} {\r\n            padding-left: 15px;\r\n            padding-right: 15px;\r\n        }\r\n        @media #{$md-layout} {\r\n            padding-left: 15px !important;\r\n            padding-right: 15px !important;\r\n        }\r\n        @media #{$sm-layout} {\r\n            padding-left: 15px !important;\r\n            padding-right: 15px !important;\r\n        }\r\n    }\r\n}\r\n\r\n.row--40 {\r\n    margin-left: -40px;\r\n    margin-right: -40px;\r\n    // Responsive\r\n    @media #{$laptop-device} {\r\n        margin-left: -15px;\r\n        margin-right: -15px;\r\n    }\r\n    @media #{$lg-layout} {\r\n        margin-left: -15px;\r\n        margin-right: -15px;\r\n    }\r\n    @media #{$md-layout} {\r\n        margin-left: -15px;\r\n        margin-right: -15px;\r\n    }\r\n    @media #{$sm-layout} {\r\n        margin-left: -15px !important;\r\n        margin-right: -15px !important;\r\n    }\r\n    &>[class*=\"col\"],\r\n    &>[class*=\"col-\"] {\r\n        padding-left: 40px;\r\n        padding-right: 40px;\r\n        // Responsive\r\n        @media #{$laptop-device} {\r\n            padding-left: 15px;\r\n            padding-right: 15px;\r\n        }\r\n        @media #{$lg-layout} {\r\n            padding-left: 15px;\r\n            padding-right: 15px;\r\n        }\r\n        @media #{$md-layout} {\r\n            padding-left: 15px !important;\r\n            padding-right: 15px !important;\r\n        }\r\n        @media #{$sm-layout} {\r\n            padding-left: 15px !important;\r\n            padding-right: 15px !important;\r\n        }\r\n    }\r\n}\r\n\r\n.row--50 {\r\n    margin-left: -50px;\r\n    margin-right: -50px;\r\n    // Responsive\r\n    @media only screen and (min-width: 1400px) and (max-width: 1599px) {\r\n        margin-left: -30px;\r\n        margin-right: -30px;\r\n    }\r\n    @media only screen and (min-width: 1200px) and (max-width: 1399px) {\r\n        margin-left: -15px;\r\n        margin-right: -15px;\r\n    }\r\n    @media #{$lg-layout} {\r\n        margin-left: -15px;\r\n        margin-right: -15px;\r\n    }\r\n    @media #{$md-layout} {\r\n        margin-left: -15px;\r\n        margin-right: -15px;\r\n    }\r\n    @media #{$sm-layout} {\r\n        margin-left: -15px !important;\r\n        margin-right: -15px !important;\r\n    }\r\n    &>[class*=\"col\"],\r\n    &>[class*=\"col-\"] {\r\n        padding-left: 50px;\r\n        padding-right: 50px;\r\n        // Responsive\r\n        @media only screen and (min-width: 1400px) and (max-width: 1599px) {\r\n            padding-left: 30px;\r\n            padding-right: 30px;\r\n        }\r\n        @media only screen and (min-width: 1200px) and (max-width: 1399px) {\r\n            padding-left: 15px;\r\n            padding-right: 15px;\r\n        }\r\n        @media #{$lg-layout} {\r\n            padding-left: 15px;\r\n            padding-right: 15px;\r\n        }\r\n        @media #{$md-layout} {\r\n            padding-left: 15px !important;\r\n            padding-right: 15px !important;\r\n        }\r\n        @media #{$sm-layout} {\r\n            padding-left: 15px !important;\r\n            padding-right: 15px !important;\r\n        }\r\n    }\r\n}\r\n\r\n.row--60 {\r\n    margin-left: -60px;\r\n    margin-right: -60px;\r\n    // Responsive\r\n    @media #{$laptop-device} {\r\n        margin-left: -15px;\r\n        margin-right: -15px;\r\n    }\r\n    @media #{$lg-layout} {\r\n        margin-left: -15px;\r\n        margin-right: -15px;\r\n    }\r\n    @media #{$md-layout} {\r\n        margin-left: -15px;\r\n        margin-right: -15px;\r\n    }\r\n    @media #{$sm-layout} {\r\n        margin-left: -15px !important;\r\n        margin-right: -15px !important;\r\n    }\r\n    &>[class*=\"col\"],\r\n    &>[class*=\"col-\"] {\r\n        padding-left: 60px;\r\n        padding-right: 60px;\r\n        // Responsive\r\n        @media #{$laptop-device} {\r\n            padding-left: 15px;\r\n            padding-right: 15px;\r\n        }\r\n        @media #{$lg-layout} {\r\n            padding-left: 15px;\r\n            padding-right: 15px;\r\n        }\r\n        @media #{$md-layout} {\r\n            padding-left: 15px !important;\r\n            padding-right: 15px !important;\r\n        }\r\n        @media #{$sm-layout} {\r\n            padding-left: 15px !important;\r\n            padding-right: 15px !important;\r\n        }\r\n    }\r\n}\r\n\r\n\r\n/*===========================\r\n    Input Placeholder\r\n=============================*/\r\n\r\ninput:-moz-placeholder,\r\ntextarea:-moz-placeholder {\r\n    opacity: 1;\r\n    -ms-filter: \"progid:DXImageTransform.Microsoft.Alpha(Opacity=100)\";\r\n}\r\n\r\ninput::-webkit-input-placeholder,\r\ntextarea::-webkit-input-placeholder {\r\n    opacity: 1;\r\n    -ms-filter: \"progid:DXImageTransform.Microsoft.Alpha(Opacity=100)\";\r\n}\r\n\r\ninput::-moz-placeholder,\r\ntextarea::-moz-placeholder {\r\n    opacity: 1;\r\n    -ms-filter: \"progid:DXImageTransform.Microsoft.Alpha(Opacity=100)\";\r\n}\r\n\r\ninput:-ms-input-placeholder,\r\ntextarea:-ms-input-placeholder {\r\n    opacity: 1;\r\n    -ms-filter: \"progid:DXImageTransform.Microsoft.Alpha(Opacity=100)\";\r\n}\r\n\r\n\r\n/*=============================\r\n\tOverlay styles \r\n==============================*/\r\n\r\n[data-overlay],\r\n[data-black-overlay],\r\n[data-white-overlay] {\r\n    position: relative;\r\n}\r\n\r\n[data-overlay]>div,\r\n[data-overlay]>*,\r\n[data-black-overlay]>div,\r\n[data-black-overlay]>*,\r\n[data-white-overlay]>div,\r\n[data-white-overlay]>* {\r\n    position: relative;\r\n    z-index: 2;\r\n}\r\n\r\n[data-overlay]:before,\r\n[data-black-overlay]:before,\r\n[data-white-overlay]:before {\r\n    content: \"\";\r\n    position: absolute;\r\n    left: 0;\r\n    top: 0;\r\n    height: 100%;\r\n    width: 100%;\r\n    z-index: 2;\r\n}\r\n\r\n[data-overlay]:before {\r\n    background-color: var(--color-primary);\r\n}\r\n\r\n[data-black-overlay]:before {\r\n    background-color: #000000;\r\n}\r\n\r\n[data-white-overlay]:before {\r\n    background-color: #ffffff;\r\n}\r\n\r\n@for $i from 1 through 10 {\r\n    [data-overlay=\"#{$i}\"]:before,\r\n    [data-black-overlay=\"#{$i}\"]:before,\r\n    [data-white-overlay=\"#{$i}\"]:before {\r\n        opacity: #{$i * 0.10};\r\n    }\r\n}\r\n\r\n\r\n/*------------------------------\r\n    Scroll Up \r\n--------------------------------*/\r\n\r\n#scrollUp {\r\n    width: 70px;\r\n    height: 80px;\r\n    right: 100px;\r\n    bottom: 60px;\r\n    text-align: center;\r\n    z-index: 9811 !important;\r\n    text-decoration: none;\r\n    background: #fff;\r\n    line-height: 80px;\r\n    color: #757589;\r\n    font-size: 15px;\r\n    font-weight: 400;\r\n    @extend %transition;\r\n    display: inline-block;\r\n    background: #ffffff;\r\n    @media #{$md-layout} {\r\n        right: 20px;\r\n        bottom: 40px;\r\n    }\r\n    @media #{$sm-layout} {\r\n        right: 20px;\r\n        bottom: 40px;\r\n    }\r\n    &::before {\r\n        width: 100%;\r\n        height: 100%;\r\n        left: 0;\r\n        bottom: 0;\r\n        background: rgba(0, 2, 72, 0.1);\r\n        content: \"\";\r\n        position: absolute;\r\n        z-index: -1;\r\n        transform-style: preserve-3d;\r\n        transform: rotateY(-10deg);\r\n        filter: blur(50px);\r\n    }\r\n    &::after {\r\n        background: #ffffff;\r\n        position: absolute;\r\n        content: \"\";\r\n        top: 0;\r\n        bottom: 0;\r\n        left: 0;\r\n        right: 0;\r\n        width: 100%;\r\n        height: 100%;\r\n        z-index: -1;\r\n        transform-style: preserve-3d;\r\n        transform: rotateY(-10deg);\r\n    }\r\n    @media #{$sm-layout} {\r\n        right: 20px;\r\n        bottom: 30px;\r\n        width: 50px;\r\n        height: 60px;\r\n        line-height: 60px;\r\n    }\r\n    span {\r\n        &.text {\r\n            position: relative;\r\n            display: inline-block;\r\n            margin-top: 7px;\r\n            @media #{$sm-layout} {\r\n                margin-top: 3px;\r\n            }\r\n            &::after {\r\n                width: 0;\r\n                height: 0;\r\n                border-style: solid;\r\n                border-width: 0 5px 7px 5px;\r\n                border-color: transparent transparent var(--color-primary) transparent;\r\n                position: absolute;\r\n                content: \"\";\r\n                left: 50%;\r\n                top: 21%;\r\n                transform: translateX(-50%);\r\n            }\r\n        }\r\n    }\r\n    &:hover {\r\n        span {\r\n            &.text {\r\n                color: var(--color-primary);\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n\r\n/*--------------------\r\n    Contact Form \r\n----------------------*/\r\n\r\n.form-message {\r\n    margin-bottom: 0;\r\n    text-align: center;\r\n    &.error {\r\n        margin-top: 20px;\r\n        color: #f80707;\r\n    }\r\n    &.success {\r\n        margin-top: 20px;\r\n        color: #0d8d2d;\r\n    }\r\n}", "/*----------------------\r\nVariables \r\n-----------------------*/\r\n:root {\r\n    //Themes colors\r\n    --color-primary: #3577f0;\r\n    --light-primary: #8c71db;\r\n    --color-secondary: #ff497c;\r\n    --color-tertiary: #FAB8C4;\r\n    --color-white: #ffffff;\r\n    --color-dark: #27272E;\r\n    --color-black: #000000;\r\n    --color-light: #CBD3D9;\r\n    --color-lighter: #F6F7FB;\r\n    --color-lightest: #C4C4C4;\r\n\r\n    // Chart Color \r\n    --color-chart01: #896BA7;\r\n    --color-chart02: #BADEFF;\r\n    --color-chart03: #E76458;\r\n    // Typo Color \r\n    --color-heading: #292930; \r\n    --color-body: #777777;\r\n    // Border Color \r\n    --color-border-light: #E3E6E9;\r\n    --color-border-dark: #42424A;\r\n    // Gery Colors \r\n    --color-gray: #999FAE;\r\n    --color-midgray: #878787;\r\n    // Extra Color \r\n    --color-extra01: #999FAE;\r\n    // Notify Colors \r\n    --color-success: #3EB75E;\r\n    --color-danger: #FF0003;\r\n    --color-warning: #FF8F3C;\r\n    --color-info: #1BA2DB;\r\n    //Social icon colors\r\n    --color-facebook: #3B5997;\r\n    --color-twitter: #1BA1F2;\r\n    --color-youtube: #ED4141;\r\n    --color-linkedin: #0077B5;\r\n    --color-pinterest: #E60022;\r\n    --color-instagram: #C231A1;\r\n    --color-vimeo: #00ADEF;\r\n    --color-twitch: #6441A3;\r\n    --color-discord: #7289da;\r\n    //Border-radius\r\n    --radius: 6px;\r\n    --radius-big: 16px;\r\n    --radius-small: 6px;\r\n    --border-width: 2px;\r\n    --border-thin: 1px;\r\n    //Font weight\r\n    //primary font\r\n    --p-light: 300;\r\n    --p-regular: 400;\r\n    --p-medium: 500;\r\n    --p-semi-bold: 600;\r\n    --p-bold: 700;\r\n    --p-extra-bold: 800;\r\n    --p-black: 900;\r\n    //secondary font\r\n    --s-light: 300;\r\n    --s-regular: 400;\r\n    --s-medium: 500;\r\n    --s-bold: 700;\r\n    --s-black: 900;\r\n    //Shadows\r\n    --shadow-primary: 0px 4px 10px rgba(37, 47, 63, 0.1);\r\n    --shadow-light: 0 2px 6px 0 rgba(0, 0, 0, 0.05);\r\n    --shadow-dark: 0 16px 32px 0 rgba(0, 0, 0, 0.04);\r\n    //transition easing\r\n    --transition: 0.3s;\r\n    //Font Family\r\n    --font-primary: 'DM Sans', sans-serif;\r\n    --font-secondary: 'DM Sans', sans-serif;\r\n    --font-awesome: 'Font Awesome 6 Pro';\r\n    //Fonts Size\r\n    --font-size-b1: 16px;\r\n    --font-size-b2: 14px;\r\n    --font-size-b3: 12px;\r\n    //Line Height\r\n    --line-height-b1: 1.7;\r\n    --line-height-b2: 1.65;\r\n    --line-height-b3: 1.6;\r\n    // Heading Font \r\n    --h1: 46px;\r\n    --h2: 36px;\r\n    --h3: 28px;\r\n    --h4: 24px;\r\n    --h5: 20px; \r\n    --h6: 18px; \r\n}\r\n\r\n// Layouts Variation\r\n$smlg-device: 'only screen and (max-width: 1199px)';\r\n$extra-device: 'only screen and (min-width: 1600px) and (max-width: 1919px)';\r\n$laptop-device: 'only screen and (min-width: 1200px) and (max-width: 1599px)';\r\n$lg-layout: 'only screen and (min-width: 992px) and (max-width: 1199px)';\r\n$md-layout:'only screen and (min-width: 768px) and (max-width: 991px)';\r\n$sm-layout:'only screen and (max-width: 767px)';\r\n$large-mobile: 'only screen and (max-width: 575px)';\r\n$small-mobile: 'only screen and (max-width: 479px)';", "\r\n/*----------------------\r\nReset Styels\r\n-----------------------*/\r\n\r\n * {\r\n    -webkit-box-sizing: border-box;\r\n    -moz-box-sizing: border-box;\r\n    box-sizing: border-box;\r\n}\r\n\r\n\r\narticle,\r\naside,\r\ndetails,\r\nfigcaption,\r\nfigure,\r\nfooter,\r\nheader,\r\nnav,\r\nsection,\r\nsummary {\r\n    display: block;\r\n}\r\n\r\naudio,\r\ncanvas,\r\nvideo {\r\n    display: inline-block;\r\n}\r\n\r\naudio:not([controls]) {\r\n    display: none;\r\n    height: 0;\r\n}\r\n\r\n[hidden] {\r\n    display: none;\r\n}\r\n\r\n\r\na {\r\n    color: var(--color-heading);\r\n    text-decoration: none;\r\n    outline: none;\r\n}\r\n\r\n\r\na:hover,\r\na:focus,\r\na:active {\r\n\ttext-decoration: none;\r\n\toutline: none;\r\n\tcolor: var(--color-primary);\r\n}\r\n\r\na:focus {\r\n    outline: none;\r\n}\r\naddress {\r\n    margin: 0 0 24px;\r\n}\r\n\r\nabbr[title] {\r\n    border-bottom: 1px dotted;\r\n}\r\n\r\nb,\r\nstrong {\r\n    font-weight: bold;\r\n}\r\nmark {\r\n    background: var(--color-primary);\r\n    color: #ffffff;\r\n}\r\ncode,\r\nkbd,\r\npre,\r\nsamp {\r\n    font-size: var(--font-size-b3);\r\n    -webkit-hyphens: none;\r\n    -moz-hyphens: none;\r\n    -ms-hyphens: none;\r\n    hyphens: none;\r\n    color: var(--color-primary);\r\n}\r\nkbd ,\r\nins{\r\n    color: #ffffff;\r\n}\r\n\r\npre  {\r\n    font-family: \"Courier 10 Pitch\", Courier, monospace;\r\n    font-size: var(--font-size-b3);\r\n    margin: 10px 0;\r\n    overflow: auto;\r\n    padding: 20px;\r\n    white-space: pre;\r\n    white-space: pre-wrap;\r\n    word-wrap: break-word;\r\n    color: var(--color-body);\r\n    background: var(--color-lighter);\r\n}\r\n\r\n\r\nsmall {\r\n    font-size: smaller;\r\n}\r\n\r\nsub,\r\nsup {\r\n    font-size: 75%;\r\n    line-height: 0;\r\n    position: relative;\r\n    vertical-align: baseline;\r\n}\r\nsup {\r\n    top: -0.5em;\r\n}\r\nsub {\r\n    bottom: -0.25em;\r\n}\r\n\r\ndl {\r\n    margin-top: 0;\r\n    margin-bottom: 10px;\r\n}\r\n\r\ndd {\r\n    margin: 0 15px 15px;\r\n}\r\ndt {\r\n    font-weight: bold;\r\n    color: var(--color-heading);\r\n}\r\n\r\nmenu,\r\nol,\r\nul {\r\n    margin: 16px 0;\r\n    padding: 0 0 0 40px;\r\n}\r\n\r\nnav ul,\r\nnav ol {\r\n    list-style: none;\r\n    list-style-image: none;\r\n}\r\nli>ul,\r\nli>ol {\r\n    margin: 0;\r\n}\r\n\r\nol {\r\n    ul {\r\n        margin-bottom: 0;\r\n    }\r\n}\r\n\r\nimg {\r\n    -ms-interpolation-mode: bicubic;\r\n    border: 0;\r\n    vertical-align: middle;\r\n    max-width: 100%;\r\n    height: auto;\r\n}\r\n\r\nsvg:not(:root) {\r\n    overflow: hidden;\r\n}\r\nfigure {\r\n    margin: 0;\r\n}\r\nform {\r\n    margin: 0;\r\n}\r\nfieldset {\r\n    border: 1px solid var(--color-border);\r\n    margin: 0 2px;\r\n    min-width: inherit;\r\n    padding: 0.35em 0.625em 0.75em;\r\n}\r\nlegend {\r\n    border: 0;\r\n    padding: 0;\r\n    white-space: normal;\r\n}\r\n\r\nbutton,\r\ninput,\r\nselect,\r\ntextarea {\r\n    font-size: 100%;\r\n    margin: 0;\r\n    max-width: 100%;\r\n    vertical-align: baseline;\r\n}\r\n\r\nbutton,\r\ninput {\r\n    line-height: normal;\r\n}\r\n\r\nbutton,\r\nhtml input[type=\"button\"],\r\ninput[type=\"reset\"],\r\ninput[type=\"submit\"] {\r\n    -webkit-appearance: button;\r\n    -moz-appearance: button;\r\n    appearance: button;\r\n    cursor: pointer;\r\n}\r\n\r\nbutton[disabled],\r\ninput[disabled] {\r\n    cursor: default;\r\n}\r\n\r\ninput[type=\"checkbox\"],\r\ninput[type=\"radio\"] {\r\n    padding: 0;\r\n}\r\n\r\ninput[type=\"search\"] {\r\n    -webkit-appearance: textfield;\r\n    -moz-appearance: textfield;\r\n    appearance: textfield;\r\n    appearance: textfield;\r\n    padding-right: 2px;\r\n    width: 270px;\r\n    cursor: text;\r\n}\r\n\r\ninput[type=\"search\"]::-webkit-search-decoration {\r\n    -webkit-appearance: none;\r\n    appearance: none;\r\n}\r\n\r\nbutton::-moz-focus-inner,\r\ninput::-moz-focus-inner {\r\n    border: 0;\r\n    padding: 0;\r\n}\r\ntextarea {\r\n    overflow: auto;\r\n    vertical-align: top;\r\n}\r\ncaption,\r\nth,\r\ntd {\r\n    font-weight: normal;\r\n}\r\n\r\nth {\r\n    font-weight: 500;\r\n    text-transform: uppercase;\r\n}\r\ntd,\r\n.wp-block-calendar tfoot td {\r\n    border: 1px solid var(--color-border);\r\n    padding: 7px 10px;\r\n}\r\ndel {\r\n    color: #656973;\r\n}\r\nins {\r\n    background: rgba(255, 47, 47, 0.4);\r\n    text-decoration: none;\r\n}\r\nhr {\r\n    background-size: 4px 4px;\r\n    border: 0;\r\n    height: 1px;\r\n    margin: 0 0 24px;\r\n}\r\n\r\ntable a,\r\ntable a:link, \r\ntable a:visited {\r\n    text-decoration: underline;\r\n}\r\n\r\ndt {\r\n    font-weight: bold;\r\n    margin-bottom: 10px;\r\n}\r\n\r\ndd {\r\n    margin: 0 15px 15px;\r\n}\r\n\r\ncaption {\r\n    caption-side: top;\r\n}\r\n\r\nkbd {\r\n    background: var(--heading-color);\r\n}\r\n\r\ndfn,\r\ncite,\r\nem {\r\n    font-style: italic;\r\n}\r\n\r\n\r\n/* BlockQuote  */\r\nblockquote,\r\nq {\r\n    -webkit-hyphens: none;\r\n    -moz-hyphens: none;\r\n    -ms-hyphens: none;\r\n    hyphens: none;\r\n    quotes: none;\r\n}\r\n\r\nblockquote:before,\r\nblockquote:after,\r\nq:before,\r\nq:after {\r\n    content: \"\";\r\n    content: none;\r\n}\r\n\r\nblockquote {\r\n    font-size: var(--font-size-b1);\r\n    font-style: italic;\r\n    font-weight: var(--p-light);\r\n    margin: 24px 40px;\r\n}\r\n\r\nblockquote blockquote {\r\n    margin-right: 0;\r\n}\r\n\r\nblockquote cite,\r\nblockquote small {\r\n    font-size: var(--font-size-b3);\r\n    font-weight: normal;\r\n}\r\n\r\nblockquote strong,\r\nblockquote b {\r\n    font-weight: 700;\r\n}\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n", "/*----------------------\r\n Typography\r\n-----------------------*/\r\n\r\n * {\r\n\tbox-sizing: border-box;\r\n}\r\n\r\nhtml {\r\n\toverflow: hidden;\r\n\toverflow-y: auto;\r\n\tmargin: 0;\r\n\tpadding: 0;\r\n\tfont-size: 10px;\r\n}\r\n@media only screen and (min-width: 992px) {\r\n\tbody.overflow-visible {\r\n\t\toverflow: visible;\r\n\t\toverflow-y: visible;\r\n\t\t\r\n\t}\r\n}\r\n\r\nbody {\r\n\tfont-size: var(--font-size-b1);;\r\n\tline-height: var(--line-height-b1);;\r\n\t-webkit-font-smoothing: antialiased;\r\n\t-moz-osx-font-smoothing: grayscale;\r\n\tfont-family: var(--font-primary);\r\n\tcolor: var(--color-body);\r\n\tfont-weight: var(--p-regular);\r\n\toverflow: hidden;\r\n\toverflow-y: auto;\r\n}\r\n\r\nh1,\r\nh2,\r\nh3,\r\nh4,\r\nh5,\r\nh6,\r\n.h1,\r\n.h2,\r\n.h3,\r\n.h4,\r\n.h5,\r\n.h6,\r\naddress,\r\np,\r\npre,\r\nblockquote,\r\nmenu,\r\nol,\r\nul,\r\ntable,\r\nhr {\r\n\tmargin: 0;\r\n\tmargin-bottom: 30px;\r\n}\r\n\r\nh1,\r\nh2,\r\nh3,\r\nh4,\r\nh5,\r\nh6,\r\n.h1,\r\n.h2,\r\n.h3,\r\n.h4,\r\n.h5,\r\n.h6 {\r\n\tword-break: break-word;\r\n\tfont-family: var(--font-secondary);\r\n\tline-height: 1.3;\r\n\tcolor: var(--color-heading);\r\n}\r\n\r\nh1,\r\n.h1 {\r\n\tfont-size: var(--h1);\r\n}\r\n\r\nh2,\r\n.h2 {\r\n\tfont-size: var(--h2);\r\n}\r\n\r\nh3,\r\n.h3 {\r\n\tfont-size: var(--h3);\r\n}\r\n\r\nh4,\r\n.h4 {\r\n\tfont-size: var(--h4);\r\n}\r\n\r\nh5,\r\n.h5 {\r\n\tfont-size: var(--h5);\r\n}\r\n\r\nh6,\r\n.h6 {\r\n\tfont-size: var(--h6);\r\n}\r\n\r\nh1,\r\nh2,\r\nh3,\r\nh4,\r\nh5,\r\nh6,\r\n.h1,\r\n.h2,\r\n.h3,\r\n.h4,\r\n.h5,\r\n.h6 {\r\n\ta {\r\n\t\tcolor: inherit;\r\n\t}\r\n}\r\n\r\n@media #{$md-layout} {\r\n\th1,\r\n\t.h1 {\r\n\t\tfont-size: 40px;\r\n\t}\r\n\th2,\r\n\t.h2 {\r\n\t\tfont-size: 30px;\r\n\t}\r\n\r\n\th3,\r\n\t.h3 {\r\n\t\tfont-size: 26px;\r\n\t}\r\n\r\n\th4,\r\n\t.h4 {\r\n\t\tfont-size: 22px;\r\n\t}\r\n}\r\n\r\n\r\n@media #{$sm-layout} {\r\n\th1,\r\n\t.h1 {\r\n\t\tfont-size: 34px;\r\n\t}\r\n\th2,\r\n\t.h2 {\r\n\t\tfont-size: 26px;\r\n\t}\r\n\r\n\th3,\r\n\t.h3 {\r\n\t\tfont-size: 24px;\r\n\t}\r\n\r\n\th4,\r\n\t.h4 {\r\n\t\tfont-size: 20px;\r\n\t}\r\n}\r\n\r\n\r\nh1,\r\n.h1,\r\nh2,\r\n.h2,\r\nh3,\r\n.h3 {\r\n\tfont-weight: var(--s-bold);\r\n}\r\n\r\nh4,\r\n.h4,\r\nh5,\r\n.h5 {\r\n\tfont-weight: var(--s-bold);\r\n}\r\n\r\nh6,\r\n.h6 {\r\n\tfont-weight: var(--s-medium);\r\n}\r\n\r\n\r\nh1,\r\nh2,\r\nh3,\r\nh4,\r\nh5,\r\nh6 {\r\n\t&.b1 {\r\n\t\tfont-size: var(--font-size-b1);\r\n\t\tline-height: var(--line-height-b1);\r\n\t}\r\n\t\r\n\t&.b2 {\r\n\t\tfont-size: var(--font-size-b2);\r\n\t\tline-height: var(--line-height-b2);\r\n\t}\r\n\r\n\t&.b3 {\r\n\t\tfont-size: var(--font-size-b3);\r\n\t\tline-height: var(--line-height-b3);\r\n\t}\r\n}\r\n\r\np {\r\n\tfont-size: var(--font-size-b1);\r\n\tline-height: var(--line-height-b1);\r\n\tfont-weight: var(--p-regular);\r\n\tcolor: var(--color-body);\r\n\tmargin: 0 0 30px;\r\n\t&.has-large-font-size {\r\n\t\tline-height: 1.5;\r\n\t\tfont-size: 36px;\r\n\t}\r\n\t&.has-medium-font-size {\r\n\t\tfont-size: 24px;\r\n\t\tline-height: 36px;\r\n\t}\r\n\r\n\t&.has-small-font-size {\r\n\t\tfont-size: 13px;\r\n\t}\r\n\r\n\t&.has-very-light-gray-color {\r\n\t\tcolor: var(--color-white);\r\n\t}\r\n\r\n\t&.has-background {\r\n\t\tpadding: 20px 30px;\r\n\t}\r\n\r\n\t&.b1 {\r\n\t\tfont-size: var(--font-size-b1);\r\n\t\tline-height: var(--line-height-b1);\r\n\t}\r\n\t\r\n\t&.b2 {\r\n\t\tfont-size: var(--font-size-b2);\r\n\t\tline-height: var(--line-height-b2);\r\n\t}\r\n\r\n\t&.b3 {\r\n\t\tfont-size: var(--font-size-b3);\r\n\t\tline-height: var(--line-height-b3);\r\n\t}\r\n\r\n\t&:last-child {\r\n\t\tmargin-bottom: 0;\r\n\t}\r\n}\r\n\r\n.b1 {\r\n\tfont-size: var(--font-size-b1);\r\n\tline-height: var(--line-height-b1);\r\n}\r\n\r\n.b2 {\r\n\tfont-size: var(--font-size-b2);\r\n\tline-height: var(--line-height-b2);\r\n}\r\n\r\n.b3 {\r\n\tfont-size: var(--font-size-b3);\r\n\tline-height: var(--line-height-b3);\r\n}\r\n\r\n\r\ntable {\r\n\tborder-collapse: collapse;\r\n\tborder-spacing: 0;\r\n\tmargin: 0 0 20px;\r\n\twidth: 100%;\r\n}\r\n\r\ntable a,\r\ntable a:link,\r\ntable a:visited {\r\n\ttext-decoration: none;\r\n}\r\n\r\ncite,\r\n.wp-block-pullquote cite,\r\n.wp-block-pullquote.is-style-solid-color blockquote cite,\r\n.wp-block-quote cite {\r\n\tcolor: var(--heading-color);\r\n}\r\n\r\nvar {\r\n\tfont-family: \"Courier 10 Pitch\", Courier, monospace;\r\n}\r\n\r\n/*---------------------------\r\n\tList Style \r\n---------------------------*/\r\nul,\r\nol {\r\n\tpadding-left: 18px;\r\n}\r\n\r\nul {\r\n    list-style: square;\r\n\tmargin-bottom: 30px;\r\n\tpadding-left: 20px;\r\n\t&.liststyle {\r\n\t\t&.bullet {\r\n\t\t\tli {\r\n\t\t\t\tfont-size: 18px;\r\n\t\t\t\tline-height: 30px;\r\n\t\t\t\tcolor: var(--color-body);\r\n\t\t\t\tposition: relative;\r\n\t\t\t\tpadding-left: 30px;\r\n\r\n\t\t\t\t@media #{$sm-layout} {\r\n\t\t\t\t\tpadding-left: 19px;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t&::before {\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\tcontent: \"\";\r\n\t\t\t\t\twidth: 6px;\r\n\t\t\t\t\theight: 6px;\r\n\t\t\t\t\tborder-radius: 100%;\r\n\t\t\t\t\tbackground: var(--color-body);\r\n\t\t\t\t\tleft: 0;\r\n\t\t\t\t\ttop: 10px;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t&+li {\r\n\t\t\t\t\tmargin-top: 8px;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\tli {\r\n\t\tfont-size: var(--font-size-b1);\r\n\t\tline-height: var(--line-height-b1);\r\n\t\tmargin-top: 8px;\r\n\t\tmargin-bottom: 8px;\r\n\t\tcolor: var(--color-body);\r\n\t\tfont-family: var(--font-secondary);\r\n\t\ta {\r\n\t\t\ttext-decoration: none;\r\n\t\t\tcolor: var(--color-heading);\r\n\t\t\t@extend %transition;\r\n\r\n\t\t\t&:hover {\r\n\t\t\t\tcolor: var(--color-primary);\r\n\t\t\t}\r\n\t\t}\r\n\t\t&::marker {\r\n\t\t\tcolor: var(--color-body);\r\n\t\t}\r\n\t}\r\n\tul {\r\n\t\tmargin-bottom: 0;\r\n\t}\r\n}\r\n\r\nol {\r\n\tmargin-bottom: 30px;\r\n\tli {\r\n\t\tfont-size: var(--font-size-b1);\r\n\t\tline-height: var(--line-height-b1);\r\n\t\tcolor: var(--color-body);\r\n\t\tmargin-top: 8px;\r\n\t\tmargin-bottom: 8px;\r\n\r\n\t\ta {\r\n\t\t\tcolor: var(--heading-color);\r\n\t\t\t@extend %transition;\r\n\t\t\ttext-decoration: none;\r\n\r\n\t\t\t&:hover {\r\n\t\t\t\tcolor: var(--color-primary);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\tul {\r\n\t\tpadding-left: 30px;\r\n\t}\r\n}", "/*----------------------\r\nExtend \r\n-----------------------*/\r\n// Others \r\n\r\n%box-shadow {\r\n    box-shadow: var(--shadow-primary);\r\n}\r\n\r\n%radius {\r\n    border-radius: var(--radius);\r\n}\r\n\r\n.radius {\r\n    @extend %radius;\r\n}\r\n\r\n\r\n/*=============== Style Css =============*/\r\n\r\n%liststyle {\r\n    padding: 0;\r\n    margin: 0;\r\n    list-style: none;\r\n}\r\n.liststyle {\r\n    padding: 0;\r\n    margin: 0;\r\n    list-style: none;\r\n}\r\n\r\n%transition {\r\n    transition: var(--transition);\r\n}\r\n\r\n%bgImagePosition {\r\n    background-repeat: no-repeat;\r\n    background-size: cover;\r\n    background-position: center center;\r\n}\r\n\r\n.bgImagePosition {\r\n    @extend %bgImagePosition;\r\n}", "/*----------------------\r\nAnimation  \r\n-----------------------*/\r\n.post-scale {\r\n    overflow: hidden;\r\n    @extend %radius;\r\n    img {\r\n        transition: 0.5s;\r\n    }\r\n    &:hover {\r\n        img {\r\n            transform: scale(1.1);\r\n        }\r\n    }\r\n}\r\n\r\n@keyframes signalanimation {\r\n    0% { \r\n        opacity: 1;\r\n    }\r\n    100% { \r\n        opacity: 0; \r\n    }\r\n}\r\n\r\n@keyframes customOne {\r\n    from {\r\n        transform: scale(1);\r\n    }\r\n\r\n    50% {\r\n        transform: scale(0.90);\r\n    }\r\n\r\n    to {\r\n        transform: scale(1);\r\n    }\r\n}\r\n\r\n@keyframes customTwo {\r\n    0% {\r\n        transform: (translate(0.0px, 0.0px));\r\n    }\r\n\r\n    50% {\r\n        transform: (translate(100.0px, 0.0px));\r\n    }\r\n\r\n    100% {\r\n        transform: (translate(50.0px, 50.0px));\r\n    }\r\n}\r\n\r\n.customOne {\r\n    animation: customOne 2s infinite;\r\n}\r\n\r\n@keyframes pulse {\r\n    0% {\r\n        box-shadow: 0 0 0 0 var(--color-primary);\r\n    }\r\n\r\n    70% {\r\n        box-shadow: 0 0 0 20px rgba(255, 255, 255, 0);\r\n    }\r\n\r\n    100% {\r\n        box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);\r\n    }\r\n}\r\n\r\n\r\n\r\n/* ------------------------\r\n    Custom Animation 01 \r\n----------------------------*/\r\n\r\n@-webkit-keyframes headerSlideDown {\r\n    0% {\r\n        transform: translateY(-100px);\r\n    }\r\n\r\n    to {\r\n       transform: translateY(0);\r\n    }\r\n}\r\n\r\n@keyframes headerSlideDown {\r\n    0% {\r\n        transform: translateY(-100px);\r\n    }\r\n\r\n    to {\r\n       transform: translateY(0);\r\n    }\r\n}\r\n\r\n\r\n/*------------------------\r\n\tslidefadeinup\r\n--------------------------*/\r\n\r\n@-webkit-keyframes slideFadeInUp {\r\n    from {\r\n        opacity: 0;\r\n        -webkit-transform: translate3d(0, 20%, 0);\r\n        transform: translate3d(0, 20%, 0);\r\n    }\r\n\r\n    to {\r\n        opacity: 1;\r\n        -webkit-transform: none;\r\n        transform: none;\r\n    }\r\n}\r\n\r\n@keyframes slideFadeInUp {\r\n    from {\r\n        opacity: 0;\r\n        -webkit-transform: translate3d(0, 20%, 0);\r\n        transform: translate3d(0, 20%, 0);\r\n    }\r\n\r\n    to {\r\n        opacity: 1;\r\n        -webkit-transform: none;\r\n        transform: none;\r\n    }\r\n}\r\n\r\n.slideFadeInUp {\r\n    -webkit-animation-name: slideFadeInUp;\r\n    animation-name: slideFadeInUp;\r\n}\r\n\r\n/* -----------------------------------\r\n    Custom Animation For All Page\r\n---------------------------------------*/\r\n\r\n@-webkit-keyframes moveVertical {\r\n    to {\r\n        opacity: 1;\r\n        -webkit-transform: translateY(0)\r\n    }\r\n}\r\n\r\n@keyframes moveVertical {\r\n    to {\r\n        opacity: 1;\r\n        -webkit-transform: translateY(0);\r\n        transform: translateY(0)\r\n    }\r\n}\r\n\r\n\r\n/*--------------------------------\r\nScroll Down Button Animation  \r\n----------------------------------*/\r\n@keyframes scrollDown {\r\n    0% {\r\n        opacity: 0;\r\n    }\r\n\r\n    10% {\r\n        transform: translateY(0);\r\n        opacity: 1;\r\n    }\r\n\r\n    100% {\r\n        transform: translateY(10px);\r\n        opacity: 0;\r\n    }\r\n}\r\n\r\n@keyframes btnIconSlide {\r\n    0% {\r\n        transform: translateY(0);\r\n    }\r\n\r\n    40% {\r\n        transform: translateY(-5px);\r\n        opacity: 0;\r\n    }\r\n    80% {\r\n        transform: translateY(5px);\r\n        opacity: 0;\r\n    }\r\n\r\n    100% {\r\n        transform: translateY(0);\r\n        opacity: 1;\r\n    }\r\n}\r\n\r\n@keyframes prevNavSlide {\r\n    0% {\r\n        transform: translateX(0);\r\n    }\r\n\r\n    40% {\r\n        transform: translateX(-5px);\r\n        opacity: 0;\r\n    }\r\n    80% {\r\n        transform: translateX(5px);\r\n        opacity: 0;\r\n    }\r\n\r\n    100% {\r\n        transform: translateX(0);\r\n        opacity: 1;\r\n    }\r\n}\r\n\r\n@keyframes nextNavSlide {\r\n    0% {\r\n        transform: translateX(0);\r\n    }\r\n\r\n    40% {\r\n        transform: translateX(5px);\r\n        opacity: 0;\r\n    }\r\n    80% {\r\n        transform: translateX(-5px);\r\n        opacity: 0;\r\n    }\r\n\r\n    100% {\r\n        transform: translateX(0);\r\n        opacity: 1;\r\n    }\r\n}\r\n", "@mixin clearfix() {\r\n\t&::after {\r\n\t\tcontent: \"\";\r\n\t\tclear: both;\r\n\t\tdisplay: table;\r\n\t}\r\n}\r\n\r\n@mixin config-bg-colors($prefix, $bg-color-...) {\r\n\t@each $i in $bg-color- {\r\n\t\t.#{$prefix}#{nth($i, 1)} {\r\n\t\t\tbackground: nth($i, 2);\r\n\t\t}\r\n\t}\r\n}\r\n\r\n@mixin placeholder {\r\n    &::-webkit-input-placeholder {\r\n        @content;\r\n    }\r\n    &:-moz-placeholder {\r\n        @content;\r\n    }\r\n    &::-moz-placeholder { \r\n        @content;\r\n    }\r\n    &:-ms-input-placeholder {\r\n        @content;\r\n    }\r\n}\r\n", "/*----------------------\r\nCommon Style \r\n-----------------------*/\r\n\r\n/* Theme Gradient */\r\n\r\n%axil-gradient {\r\n    background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, #000000 100%);\r\n}\r\n\r\n\r\n.bg-gradient-1 {\r\n    background-image: radial-gradient(134.22% 147.34% at -14.53% -24.7%, #FFFFFF 0%, #FEEBED 100%);\r\n}\r\n\r\n.bg-gradient-2 {\r\n    background: radial-gradient(77.67% 226.43% at 30.03% 4.61%, #FFFFFF 0%, #F1E6FF 100%);\r\n\r\n}\r\n\r\n.bg-gradient-3 {\r\n    background: radial-gradient(119.73% 312.23% at 62.29% -39.18%, #FFFFFF 0%, #F0E6FF 100%);\r\n}\r\n\r\n.bg-gradient-4 {\r\n   \tbackground-image: radial-gradient(115.16% 203.59% at 65.89% 10.39%, #FFFFFF 0%, #FEEBED 100%);\r\n}\r\n\r\n.bg-gradient-5 {\r\n    background-image: radial-gradient(106.12% 118.09% at 67.29% -3.46%, #FFFFFF 0%, #FEEBED 100%);\r\n}\r\n\r\n.bg-gradient-6 {\r\n    background-image: radial-gradient(53.86% 87.31% at 67.29% -3.46%, #FFFFFF 0%, #FEEBED 100%);\r\n}\r\n\r\n.bg-gradient-7 {\r\n    background-image: radial-gradient(95.25% 95.25% at 50% 4.75%, #FFFFFF 0%, rgba(255, 255, 255, 0) 100%);\r\n}\r\n\r\n.bg-gradient-8 {\r\n    background: radial-gradient(171.05% 478.76% at 62.29% -39.18%, #FFFFFF 0%, #F0E6FF 100%);\r\n}\r\n\r\n.bg-primary {\r\n    background-color: var(--color-primary);\r\n}\r\n.bg-tertiary {\r\n    background-color: var(--color-tertiary);\r\n}\r\n\r\n.bg-tertiary-2 {\r\n    background-color: #FFECEE;\r\n}\r\n\r\n.bg-lightest {\r\n    background-color: var(--color-lightest);\r\n}\r\n.bg-lighter {\r\n    background-color: var(--color-lighter);\r\n}\r\n\r\n.bg-vista-white {\r\n    background-color: #f9f3f0;\r\n}\r\n.bg-wild-sand {\r\n    background-color: #f6f6f6;\r\n}\r\n\r\n.primary-color {\r\n    color: var(--color-primary);\r\n}\r\n\r\n.secondary-color {\r\n    color: var(--color-secondary);\r\n}\r\n\r\n.tertiary-color {\r\n    color: var(--color-tertiary);\r\n}\r\n\r\n.black-color {\r\n    color: var(--color-black);\r\n}\r\n\r\n.white-color {\r\n    color: var(--color-white);\r\n}\r\n\r\n.heading-color {\r\n    color: var(--color-heading);\r\n}\r\n\r\n.d-flex-center {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    align-items: center;\r\n}\r\n\r\n.overflow-md-visible {\r\n    @media only screen and (min-width: 992px) {\r\n        overflow: visible !important;\r\n    }\r\n}", "/*----------------------\r\nForms Styles\r\n-----------------------*/\r\n\r\ninput,\r\nbutton,\r\nselect,\r\ntextarea {\r\n    background: var(--color-lighter);\r\n    border: 1px solid var(--color-border);\r\n    transition: all 0.4s ease-out 0s;\r\n    color: var(--color-heading);\r\n    width: 100%;\r\n    &:focus,\r\n    &:active {\r\n        outline: none;\r\n        border-color: var(--color-primary);\r\n    }\r\n}\r\n\r\nbutton,\r\n[type=\"button\"],\r\n[type=\"reset\"],\r\n[type=\"submit\"] {\r\n    -webkit-appearance: button;\r\n}\r\n\r\ninput {\r\n    height: 40px;\r\n    padding: 0 15px;\r\n}\r\n\r\nselect,\r\n.select2 {\r\n    cursor: pointer;\r\n    transition: 0.3s;\r\n    height: 55px;\r\n    padding: 0 30px;\r\n    outline: none;\r\n    color: var(--color-body);\r\n    -moz-appearance: none;\r\n    -webkit-appearance: none;\r\n    appearance: none;\r\n    border: 1px solid var(--color-border-light);\r\n    border-radius: 6px;\r\n    background: url(../images/icons/arrow-icon.png) 95% center no-repeat transparent;\r\n    padding-right: 32px;\r\n    font-size: var(--font-size-b1);\r\n    line-height: var(--line-height-b1);\r\n    font-family: var(--font-secondary);\r\n}\r\n\r\ninput[type=\"text\"],\r\ninput[type=\"password\"],\r\ninput[type=\"email\"],\r\ninput[type=\"number\"],\r\ninput[type=\"tel\"],\r\ntextarea {\r\n    font-size: var(--font-size-b2);\r\n    font-weight: 400;\r\n    height: auto;\r\n    line-height: 60px;\r\n    background: #fff;\r\n    -webkit-box-shadow: none;\r\n    box-shadow: none;\r\n    padding: 0 30px;\r\n    outline: none;\r\n    border: var(--border-width) solid var(--color-border);\r\n    border-radius: var(--radius);\r\n    /* -- Placeholder -- */\r\n    &::placeholder {\r\n        color: var(--color-body);\r\n        /* Firefox */\r\n        opacity: 1;\r\n    }\r\n    &:-ms-input-placeholder {\r\n        /* Internet Explorer 10-11 */\r\n        color: var(--color-body);\r\n    }\r\n    &::-ms-input-placeholder {\r\n        /* Microsoft Edge */\r\n        color: var(--color-body);\r\n    }\r\n    &.p-holder__active {\r\n        border-color: var(--color-primary);\r\n        /* -- Placeholder -- */\r\n        &::placeholder {\r\n            color: var(--color-primary);\r\n            /* Firefox */\r\n            opacity: 1;\r\n        }\r\n        &:-ms-input-placeholder {\r\n            /* Internet Explorer 10-11 */\r\n            color: var(--color-primary);\r\n        }\r\n        &::-ms-input-placeholder {\r\n            /* Microsoft Edge */\r\n            color: var(--color-primary);\r\n        }\r\n    }\r\n    &.p-holder__error {\r\n        border-color: #f4282d;\r\n        /* -- Placeholder -- */\r\n        &::placeholder {\r\n            color: #f4282d;\r\n            /* Firefox */\r\n            opacity: 1;\r\n        }\r\n        &:-ms-input-placeholder {\r\n            /* Internet Explorer 10-11 */\r\n            color: #f4282d;\r\n        }\r\n        &::-ms-input-placeholder {\r\n            /* Microsoft Edge */\r\n            color: #f4282d;\r\n        }\r\n        &:focus {\r\n            border-color: #f4282d;\r\n        }\r\n    }\r\n    &:focus {\r\n        border-color: var(--color-primary);\r\n    }\r\n}\r\n\r\n.input-active {\r\n    @extend .p-holder__active;\r\n    input {\r\n        @extend .p-holder__active;\r\n    }\r\n}\r\n\r\n.input-error {\r\n    @extend .p-holder__error;\r\n    input {\r\n        @extend .p-holder__error;\r\n    }\r\n}\r\n\r\n// Custom Checkbox and radio button \r\ninput[type=\"checkbox\"],\r\ninput[type=\"radio\"] {\r\n    opacity: 0;\r\n    position: absolute;\r\n    ~label {\r\n        position: relative;\r\n        font-size: 16px;\r\n        line-height: 20px;\r\n        color: var(--color-body);\r\n        font-weight: 500;\r\n        padding-left: 28px;\r\n        cursor: pointer;\r\n        &::before {\r\n            content: \" \";\r\n            position: absolute;\r\n            top: 2px;\r\n            left: 0;\r\n            width: 16px;\r\n            height: 16px;\r\n            background-color: #fff;\r\n            border: var(--border-thin) solid var(--color-body);\r\n            border-radius: 2px;\r\n            transition: all .3s;\r\n        }\r\n        &::after {\r\n            content: \" \";\r\n            position: absolute;\r\n            top: 5px;\r\n            left: 2px;\r\n            width: 10px;\r\n            height: 5px;\r\n            background-color: transparent;\r\n            border-bottom: var(--border-thin) solid #fff;\r\n            border-left: var(--border-thin) solid #fff;\r\n            border-radius: 2px;\r\n            transform: rotate(-45deg);\r\n            opacity: 0;\r\n            transition: all .3s;\r\n        }\r\n    }\r\n    &:checked {\r\n        ~label {\r\n            &::before {\r\n                background-color: var(--color-primary);\r\n                border: var(--border-width) solid var(--color-primary);\r\n            }\r\n            &::after {\r\n                opacity: 1;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\ninput[type=\"radio\"] {\r\n    ~label {\r\n        &::before {\r\n            border-radius: 50%;\r\n        }\r\n        &::after {\r\n            width: 8px;\r\n            height: 8px;\r\n            left: 4px;\r\n            top: 6px;\r\n            background: #fff;\r\n            border-radius: 50%;\r\n        }\r\n    }\r\n}\r\n\r\n.form-group {\r\n    margin-bottom: 30px;\r\n    position: relative;\r\n    label {\r\n        margin-bottom: 6px;\r\n        font-size: 14px;\r\n        line-height: 22px;\r\n        font-weight: 500;\r\n        color: var(--color-body);\r\n    }\r\n    input {\r\n        border: 0 none;\r\n        border-radius: 6px;\r\n        height: 50px;\r\n        font-size: var(--font-size-b2);\r\n        @extend %transition;\r\n        padding: 0 20px;\r\n        background-color: #fff;\r\n        border: 1px solid var(--color-light);\r\n        @extend %transition;\r\n        &:focus {\r\n            border-color: var(--color-primary);\r\n            box-shadow: none;\r\n        }\r\n    }\r\n    textarea {\r\n        min-height: 160px;\r\n        border: 0 none;\r\n        border-radius: 6px;\r\n        resize: none;\r\n        padding: 15px;\r\n        font-size: var(--font-size-b2);\r\n        @extend %transition;\r\n        background-color: #fff;\r\n        border: 1px solid var(--color-light);\r\n        line-height: 1.5;\r\n        padding-left: 30px;\r\n        padding-top: 20px;\r\n        &:focus {\r\n            border-color: var(--color-primary);\r\n        }\r\n    }\r\n}\r\n\r\ninput[type=\"submit\"] {\r\n    width: auto;\r\n    padding: 0 30px;\r\n    border-radius: 6px;\r\n    display: inline-block;\r\n    font-weight: 500;\r\n    transition: 0.3s;\r\n    height: 60px;\r\n    background: var(--color-primary);\r\n    color: var(--color-white);\r\n    font-weight: var(--p-medium);\r\n    font-size: var(--font-size-b2);\r\n    line-height: var(--line-height-b3);\r\n    border: 2px solid var(--color-primary);\r\n    @extend %transition;\r\n    &:hover {\r\n        background: transparent;\r\n        color: var(--color-primary);\r\n    }\r\n}\r\n\r\n.error-msg,\r\n.success-msg {\r\n    p {\r\n        width: 100%;\r\n        margin: 20px 0 0 !important;\r\n    }\r\n}\r\n.error-msg {\r\n    p {\r\n        color: #ff0000;\r\n    }\r\n}\r\n\r\n.success-msg {\r\n    p {\r\n        color: #5956e9;\r\n    }\r\n}", "/*-------------------------\r\nAbout Us  \r\n--------------------------*/\r\n.axil-about-area {\r\n\t.about-thumbnail {\r\n\t\t@media only screen and (max-width: 991px) {\r\n\t\t\tmargin-bottom: 50px;\r\n\t\t\ttext-align: center;\r\n\t\t}\r\n\t\t@media only screen and (max-width: 767px) {\r\n\t\t\tmargin-bottom: 40px;\r\n\t\t}\r\n\t\timg {\r\n\t\t\tborder-radius: 10px;\r\n\t\t\twidth: 100%;\r\n\t\t}\r\n\t}\r\n\t.about-content {\r\n\t\t.title {\r\n\t\t\tmargin-bottom: 24px;\r\n\t\t\tline-height: 1.2;\r\n\t\t\t@media #{$smlg-device} {\r\n\t\t\t\tfont-size: 36px;\r\n\t\t\t}\r\n\t\t\t@media #{$sm-layout} {\r\n\t\t\t\tfont-size: 30px;\r\n\t\t\t}\r\n\t\t}\r\n\t\t.text-heading {\r\n\t\t\tfont-size: 20px;\r\n\t\t\tmargin-bottom: 22px;\r\n\t\t\tdisplay: block;\r\n\t\t}\r\n\t\tp {\r\n\t\t\tfont-size: var(--font-size-b2);\r\n\t\t\tmargin-bottom: 34px;\r\n\t\t}\r\n\t\t&.content-right {\r\n\t\t\tpadding-left: 50px;\r\n\t\t\t@media #{$smlg-device} {\r\n\t\t\t\tpadding-left: 0;\r\n\t\t\t}\r\n\t\t}\r\n\t\t&.content-left {\r\n\t\t\tpadding-right: 60px;\r\n\t\t\t@media #{$smlg-device} {\r\n\t\t\t\tpadding-right: 0;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t&.about-style-2 {\r\n\t\tpadding-top: 80px;\r\n\t\t@media only screen and (max-width: 767px) {\r\n\t\t\tpadding-top: 60px;\r\n\t\t}\r\n\t\t.about-content {\r\n\t\t\t.subtitle {\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\tmargin-bottom: 5px;\r\n\t\t\t\tdisplay: block;\r\n\t\t\t}\r\n\t\t\t.title {\r\n\t\t\t\tfont-size: 40px;\r\n\t\t\t\t@media #{$smlg-device} {\r\n\t\t\t\t\tfont-size: 34px;\r\n\t\t\t\t}\r\n\t\t\t\t@media #{$sm-layout} {\r\n\t\t\t\t\tfont-size: 30px;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t.axil-btn {\r\n\t\t\t\tborder-color: #efefef;\r\n\t\t\t\t&:hover {\r\n\t\t\t\t\tborder-color: var(--color-primary);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.about-info-area {\r\n\tposition: relative;\r\n\tz-index: 1;\r\n\t&:after {\r\n\t\tcontent: \"\";\r\n\t\theight: 50%;\r\n\t\twidth: 100%;\r\n\t\tbackground-color: #f6f6f6;\r\n\t\tposition: absolute;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tbottom: 0;\r\n\t\tz-index: -1;\r\n\t}\r\n}\r\n\r\n.about-info-box {\r\n\tbox-shadow: 0 16px 32px 0 rgba(0, 0, 0, .04);\r\n\tpadding: 40px 50px;\r\n\tborder: 1px solid var(--color-white);\r\n\tborder-radius: 5px;\r\n\tbackground-color: var(--color-white);\r\n\ttransition: var(--transition);\r\n\tmargin-bottom: 30px;\r\n\t@media #{$sm-layout} {\r\n\t\tpadding: 30px;\r\n\t}\r\n\t.thumb {\r\n\t\tmargin-bottom: 26px;\r\n\t}\r\n\t.content {\r\n\t\t.title {\r\n\t\t\tmargin-bottom: 12px;\r\n\t\t\tfont-weight: 700;\r\n\t\t}\r\n\t\tp {\r\n\t\t\tfont-size: var(--font-size-b2);\r\n\t\t}\r\n\t}\r\n\t&:hover {\r\n\t\tborder-color: var(--color-primary);\r\n\t}\r\n}\r\n\r\n.about-style-3 {\r\n\tpadding: 80px 0 0;\r\n\tmargin-bottom: -20px;\r\n\t@media #{$sm-layout} {\r\n\t\tpadding: 60px 0 0;\r\n\t}\r\n\t.section-title-wrapper {\r\n\t\tpadding-right: 0;\r\n\t\t.title {\r\n\t\t\tmargin-bottom: 10px;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.about-features {\r\n\tmargin-bottom: 50px;\r\n\t@media #{$sm-layout} {\r\n\t\tmargin-bottom: 30px;\r\n\t}\r\n\t.sl-number {\r\n\t\tfont-size: 40px;\r\n\t\tfont-weight: 700;\r\n\t\tcolor: var(--color-lightest);\r\n\t\tmargin-bottom: 10px;\r\n\t}\r\n\t.title {\r\n\t\tmargin-bottom: 10px;\r\n\t}\r\n}\r\n\r\n.about-gallery {\r\n\t.thumbnail {\r\n\t\tmargin-bottom: 20px;\r\n\t\timg {\r\n\t\t\tborder-radius: 6px;\r\n\t\t\twidth: 100%;\r\n\t\t}\r\n\t\t&.thumbnail-1 {\r\n\t\t\tmargin-top: 30px;\r\n\t\t\t@media only screen and (max-width: 991px) {\r\n\t\t\t\tmargin-top: 0;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t}\r\n\t}\r\n}", "/*-------------------------\r\n    Back To Top  \r\n--------------------------*/\r\n.back-to-top {\r\n    position: fixed;\r\n    bottom: -40px;\r\n    right: 40px;\r\n    display: block;\r\n    width: 45px;\r\n    height: 45px;\r\n    line-height: 46px;\r\n    background: var(--color-primary);\r\n    color: #fff;\r\n    text-align: center;\r\n    text-decoration: none;\r\n    border-radius: var(--radius);\r\n    opacity: 0;\r\n    transform: scale(0.3);\r\n    box-shadow: 4px 4px 10px rgba(0, 0, 0, 0.2);\r\n    z-index: 9;\r\n    transition: all .3s;\r\n    @media #{$large-mobile} {\r\n        width: 40px;\r\n        height: 40px;\r\n        line-height: 40px;\r\n    }\r\n    &:focus {\r\n    \tcolor: var(--color-white);\r\n    }\r\n    &.show {\r\n\t \tbottom: 40px;\r\n\t    right: 40px;\r\n\t    opacity: 1;\r\n\t    transform: scale(1);\r\n        @media #{$large-mobile} {\r\n            bottom: 10px;\r\n            right: 10px;\r\n        }\r\n        &:hover {\r\n        color: var(--color-white);\r\n           bottom: 45px;\r\n           opacity: 1;\r\n           @media #{$large-mobile} {\r\n               bottom: 10px;\r\n           }\r\n       }\r\n    }\r\n}\r\n\r\n", "/*-------------------------\r\n    Breadcrumb Styles  \r\n--------------------------*/\r\n.axil-breadcrumb-area {\r\n    position: relative;\r\n    background-color: #f8f8f8;\r\n    padding: 40px 0 45px;\r\n    .inner {\r\n        .title {\r\n            font-size: 40px;\r\n            margin-bottom: 0;\r\n            @media #{$md-layout} {\r\n               font-size: 34px;\r\n            }\r\n            @media #{$sm-layout} {\r\n               font-size: 28px;\r\n            }\r\n            @media #{$large-mobile} {\r\n               font-size: 24px;\r\n            }\r\n        }\r\n        .bradcrumb-thumb {\r\n            text-align: right;\r\n            position: relative;\r\n            z-index: 1;\r\n            @media #{$sm-layout} {\r\n                display: none;\r\n            }\r\n            &::after {\r\n                content: \"\";\r\n                height: 110px;\r\n                width: 110px;\r\n                background-color: var(--color-white);\r\n                border-radius: 50%;\r\n                position: absolute;\r\n                top: -10px;\r\n                right: 60px;\r\n                z-index: -1;\r\n            }\r\n        }\r\n    }\r\n}\r\n.axil-breadcrumb {\r\n    display: flex;\r\n    padding: 0;\r\n    margin: 0 0 15px;\r\n    list-style: none;\r\n    align-items: center;\r\n    li {\r\n        margin-top: 0;\r\n        margin-bottom: 0;\r\n        font-size: var(--font-size-b1);\r\n        line-height: var(--line-height-b1);\r\n        font-weight: 500;\r\n        a {\r\n            color: #999999;\r\n            display: block;\r\n        }\r\n        &.axil-breadcrumb-item {\r\n            &.active {\r\n                color: var(--color-primary);\r\n            }\r\n        }\r\n        &.separator {\r\n            height: 11px;\r\n            width: 2px;\r\n            background-color: #e5e5e5;\r\n            margin: 0 8px;\r\n        }\r\n    }\r\n}\r\n\r\n    \r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n", "/*----------------------\r\n    <PERSON><PERSON> Styles  \r\n-----------------------*/\r\n\r\na,\r\nbutton {\r\n    &.axil-btn {\r\n        border-radius: 6px;\r\n        font-size: var(--font-size-b1);\r\n        line-height: var(--line-height-b1);\r\n        font-weight: 700;\r\n        display: inline-block;\r\n        padding: 16px 38px;\r\n        position: relative;\r\n        transition: all 0.3s ease-in-out;\r\n        z-index: 1;\r\n        &:before {\r\n            content: \"\";\r\n            height: 100%;\r\n            width: 100%;\r\n            border-radius: 6px;\r\n            position: absolute;\r\n            top: 0;\r\n            bottom: 0;\r\n            left: 0;\r\n            right: 0;\r\n            z-index: -1;\r\n            transition: transform .5s cubic-bezier(.165,.84,.44,1);\r\n        }\r\n        @media #{$sm-layout} {\r\n            padding: 12px 25px;\r\n            font-size: 14px;\r\n        }\r\n        i {\r\n            font-weight: 400;\r\n            margin-right: 10px;\r\n            color: var(--color-heading);\r\n            transition: 0.3s;\r\n        }\r\n        &:hover {\r\n            &:before {\r\n                transform: scale(1.1);\r\n            }\r\n        }\r\n        &.right-icon {\r\n            i {\r\n                margin-left: 5px;\r\n                margin-right: 0;\r\n                color: var(--color-heading);\r\n                position: relative;\r\n                top: 2px;\r\n            }\r\n        }\r\n        &.btn-bg-white {\r\n            background-color: var(--color-white);\r\n            color: var(--color-heading);\r\n            box-shadow: 0 16px 32px 0 rgba(103, 103, 103, .06);\r\n            &:before {\r\n                background-color: var(--color-white);\r\n            }\r\n        }\r\n        &.btn-bg-lighter {\r\n            background-color: var(--color-lighter);\r\n            color: var(--color-body);\r\n            &:before {\r\n                background-color: var(--color-lighter);\r\n            }\r\n        }\r\n        &.btn-bg-primary {\r\n            background-color: var(--color-primary);\r\n            color: var(--color-white);\r\n            &:before {\r\n                background-color: var(--color-primary);\r\n            }\r\n            i {\r\n                color: var(--color-white);\r\n            }\r\n            &:hover {\r\n                // background-color: var(--color-secondary);\r\n                &:after {\r\n                    transform: scale(1.1);\r\n                }\r\n            }\r\n        }\r\n        &.btn-bg-secondary {\r\n            background-color: var(--color-secondary);\r\n            color: var(--color-white);\r\n            i {\r\n                color: var(--color-white);\r\n            }\r\n            &:before {\r\n                background-color: var(--color-secondary);\r\n            }\r\n        }\r\n        &.btn-outline {\r\n            border: 2px solid var(--color-heading);\r\n            &:hover {\r\n                background-color: var(--color-primary);\r\n                border-color: var(--color-primary);\r\n                color: var(--color-white);\r\n            }\r\n        }\r\n        &.wishlist-btn {\r\n            border: 2px solid var(--color-light);\r\n            padding: 15px 16px 10px;\r\n            @media #{$sm-layout} {\r\n                padding: 11px 18px 9px;\r\n            }\r\n            i {\r\n                font-size: 20px;\r\n                color: var(--color-body);\r\n                margin: 0;\r\n                @media #{$sm-layout} {\r\n                    font-size: 16px;\r\n                }\r\n            }\r\n            &:before {\r\n                background-color: var(--color-primary);\r\n                opacity: 0;\r\n                visibility: hidden;\r\n                transform: scale(.8);\r\n                transition: .3s;\r\n            }\r\n            &:hover {\r\n                border-color: var(--color-primary);\r\n                i {\r\n                    color: var(--color-white);\r\n\r\n                }\r\n                &:before {\r\n                    visibility: visible;\r\n                    opacity: 1;\r\n                    transform: scale(1.2);\r\n                }\r\n            }\r\n        }\r\n        &.btn-size-md {\r\n            font-size: 14px;\r\n            font-weight: 500;\r\n            display: inline-flex;\r\n            align-items: center;\r\n            padding: 10px 30px 9px;\r\n            border-radius: 8px;\r\n            i {\r\n                font-size: 21px;\r\n            }\r\n        }\r\n    }\r\n}", "/*-------------------------\r\nCategories \r\n--------------------------*/\r\n.categrie-product {\r\n    min-width: 120px;\r\n    position: relative;\r\n    text-align: center;\r\n    border-radius: 6px;\r\n    margin-bottom: 30px;\r\n    box-shadow: 0 15px 20px -10px rgba(0, 0, 0, 0.04);\r\n    transition: .5s ease-in-out;\r\n    z-index: 1;\r\n    margin-top: 30px;\r\n    &:before {\r\n        content: \"\";\r\n        height: 100%;\r\n        width: 100%;\r\n        background-color: var(--color-white);\r\n        border: 1px solid #f0f0f0;\r\n        border-radius: 4px;\r\n        position: absolute;\r\n        top: 0;\r\n        bottom: 0;\r\n        left: 0;\r\n        right: 0;\r\n        z-index: -1;\r\n        transition: transform .5s cubic-bezier(.165,.84,.44,1);\r\n    }\r\n    a {\r\n        padding: 28px 12px;\r\n        display: block;\r\n    }\r\n    img {\r\n        margin: 0 auto 8px;\r\n        min-height: 41px;\r\n    }\r\n    .cat-title {\r\n        margin-bottom: 0;\r\n        font-size: 16px;\r\n        line-height: 24px;\r\n    }\r\n    &:hover {\r\n        border-color: var(--color-white);\r\n        box-shadow: 0 20px 20px -10px rgba(0, 0, 0, .1);\r\n        \r\n        &:before {\r\n            transform: scale(1.1);\r\n        }\r\n    }\r\n}\r\n\r\n.categrie-product-2 {\r\n    border: 1px solid #f0f0f0;\r\n    position: relative;\r\n    text-align: center;\r\n    border-radius: 6px;\r\n    transition: 0.3s;\r\n    box-shadow: var(--shadow-dark);\r\n    margin-bottom: 30px;\r\n    a {\r\n        padding: 17px 10px;\r\n        display: flex;\r\n        justify-content: center;\r\n    }\r\n  \r\n    img {\r\n        margin-right: 10px;\r\n        max-height: 24px;\r\n    }\r\n    .cat-title {\r\n        margin-bottom: 0;\r\n        font-size: 16px;\r\n        line-height: 24px;\r\n    }\r\n    &:hover {\r\n        background: #fff;\r\n        box-shadow: none;\r\n    }\r\n}\r\n\r\n.categrie-product-3 {\r\n    border: 1px solid #f0f0f0;\r\n    &:before {\r\n        display: none;\r\n    }\r\n    a {\r\n        padding: 12px 12px;\r\n        img {\r\n            border-radius: 4px;\r\n            width: 100%;\r\n        }\r\n    }\r\n}\r\n\r\n.categrie-product-4 {\r\n    box-shadow: none;\r\n    margin-top: 0;\r\n    &:before {\r\n        display: none;\r\n    }\r\n    .cate-thumb {\r\n        padding: 0;\r\n        img {\r\n            min-height: auto;\r\n            margin: 0 auto;\r\n        }\r\n    }\r\n    .cat-title {\r\n        margin-top: 16px;\r\n        font-size: 20px;\r\n        font-weight: 500;\r\n    }\r\n    &:hover {\r\n        box-shadow: none;\r\n    }\r\n}\r\n\r\n.categorie-product-two {\r\n    .slick-arrow {\r\n        display: none !important;\r\n    }\r\n}\r\n\r\n.axil-categorie-area {\r\n    .section-title-wrapper {\r\n        margin-bottom: 10px;\r\n        .title {\r\n            margin-bottom: 0;\r\n        }\r\n        @media #{$sm-layout} {\r\n            margin-bottom: 0;\r\n        }\r\n    }\r\n    .arrow-top-slide .slide-arrow {\r\n        top: -60px;\r\n    }\r\n}\r\n", "/*-------------------------\r\n Contact \r\n--------------------------*/\r\n#gmap_canvas {\r\n\twidth: 100%;\r\n\tborder-radius: 6px;\r\n\tborder: none;\r\n}\r\n.axil-contact-page {\r\n\t.title {\r\n\t\tfont-weight: var(--s-medium);\r\n\t\tcolor: var(--color-black);\r\n\t}\r\n\t.contact-form {\r\n\t\t@media only screen and (max-width: 991px) {\r\n\t\t\tmargin-bottom: 50px;\t\r\n\t\t}\r\n\t\tp {\r\n\t\t\twidth: 80%;\r\n\t\t\tmargin-bottom: 45px;\r\n\t\t}\r\n\t}\r\n\t.form-group {\r\n\t\tlabel {\r\n\t\t\tspan {\r\n\t\t\t\tcolor: \tvar(--color-chart03);\r\n\t\t\t}\r\n\t\t}\r\n\t\tinput {\r\n\t\t\theight: 60px;\r\n\t\t}\r\n\t\t.axil-btn {\r\n\t\t\twidth: auto;\r\n\t\t}\r\n\t}\r\n\t.contact-location {\r\n\t\tspan {\r\n\t\t\tdisplay: block;\r\n\t\t\t@media #{$sm-layout} {\r\n\t\t\t\tfont-size: var(--font-size-b2);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}", "/*-------------------------\r\nCountdown \r\n--------------------------*/\r\n.countdown {\r\n    display: flex;\r\n    .countdown-section {\r\n        height: 80px;\r\n        width: 80px;\r\n        background-color: var(--color-white);\r\n        border-radius: 50%;\r\n        margin-right: 15px;\r\n        display: flex;  \r\n        align-items: center;\r\n        justify-content: center;\r\n        text-align: center; \r\n        @media #{$md-layout} {\r\n            height: 70px;\r\n            width: 70px;\r\n        } \r\n        @media #{$sm-layout} {\r\n            height: 60px;\r\n            width: 60px;\r\n            margin-right: 10px;\r\n        }\r\n        @media #{$small-mobile} {\r\n            margin-right: 6px;\r\n        }\r\n        &:last-child {\r\n            margin-right: 0;\r\n            &:after {\r\n                display: none;\r\n            }\r\n        }\r\n    }\r\n    .countdown-number {\r\n        font-size: 24px;\r\n        font-weight: var(--s-medium);\r\n        color: var(--color-black);\r\n        line-height: 1;\r\n        margin-bottom: 5px;\r\n        @media #{$md-layout} {\r\n            font-size: 20px;\r\n        }\r\n        @media #{$sm-layout} {\r\n            font-size: 18px;\r\n        }\r\n    }\r\n    .countdown-unit {\r\n        line-height: 1;\r\n        font-size: 14px;\r\n        font-weight: var(--s-medium);\r\n    }\r\n}\r\n\r\n.sale-countdown {\r\n    .countdown-section {\r\n        background-color: var(--color-lighter);\r\n        height: 50px;\r\n        width: 50px;\r\n        margin-right: 25px;\r\n        position: relative;\r\n        &:after {\r\n            content:\":\";\r\n            font-size: var(--font-size-b2);\r\n            color: var(--color-heading);\r\n            font-weight: var(--s-medium);\r\n            position: absolute;\r\n            right: -14px;\r\n        }\r\n    }\r\n    .countdown-number {\r\n        margin-bottom: 0;\r\n        font-size: var(--font-size-b2);\r\n        color: var(--color-heading);\r\n    }\r\n    .countdown-unit {   \r\n        display: none;\r\n    }\r\n}\r\n", "/*-------------------------\r\n    404 <USER>\r\n<GROUP>*/\r\n.onepage-screen-area {\r\n    position: relative;\r\n    z-index: 1;\r\n    background: var(--gradient-primary);\r\n    min-height: 500px;\r\n    padding: 100px 0;\r\n    @media #{$md-layout} {\r\n        text-align: center;\r\n        padding: 80px 0;\r\n    }\r\n    @media #{$sm-layout} {\r\n        text-align: center;\r\n        padding: 60px 0;\r\n    }\r\n    .content {\r\n        padding-right: 100px;\r\n        @media only screen and (max-width: 991px) {\r\n            padding-right: 0;\r\n            margin-bottom: 50px;\r\n        }\r\n        .title {\r\n            margin-bottom: 30px;\r\n        }\r\n        @media only screen and (max-width: 991px) {\r\n            .title-highlighter {\r\n                justify-content: center;\r\n            }\r\n        }\r\n        p {\r\n            margin-bottom: 45px;\r\n        }\r\n     \r\n      \r\n    }\r\n}\r\n\r\n\r\n.comming-soon-area {\r\n\tposition: fixed;\r\n\ttop: 0;\r\n\tbottom: 0;\r\n\tleft: 0;\r\n\tright: 0;\r\n\theight: 100%;\r\n\twidth: 100%;\r\n\toverflow: hidden;\r\n    @media only screen and (max-width: 991px) {\r\n        padding: 60px 15px;\r\n    }\r\n}\r\n\r\n.comming-soon-banner {\r\n\tmin-height: 100vh;\r\n\theight: 100vh;\r\n\tpadding: 100px 50px;\r\n\t@media #{$lg-layout} {\r\n\t\tmargin-right: 50px;\r\n\t}\r\n\t@media only screen and (max-width: 991px) {\r\n\t\tdisplay: none;\t\r\n\t}\r\n}\r\n\r\n.comming-soon-content {\r\n    text-align: center;\r\n    .brand-logo {\r\n        margin-bottom: 30px;\r\n    }\r\n    .title {\r\n        margin-bottom: 15px;\r\n    }\r\n    .countdown {\r\n        margin-bottom: 50px;\r\n        justify-content: center;\r\n        @media #{$smlg-device} {\r\n        }\r\n        .countdown-section {\r\n            box-shadow: 0 8px 16px 0 rgba(53, 119, 240, .3);\r\n            height: 100px;\r\n            width: 100px;\r\n            background-color: var(--color-primary);\r\n            color: var(--color-white);\r\n            @media #{$large-mobile} {\r\n                height: 60px;\r\n                width: 60px;\r\n            }\r\n        }\r\n        .countdown-number {\r\n            font-size: 30px;\r\n            color: var(--color-white);\r\n            @media #{$large-mobile} {\r\n                font-size: 20px;\r\n            }\r\n        }\r\n    }\r\n    .newsletter-form {\r\n        justify-content: center;\r\n        input {\r\n            background-color: var(--color-lighter);\r\n            \r\n        }\r\n    }\r\n}\r\n\r\n", "/*-------------------------\r\nNewsletter  \r\n--------------------------*/\r\n.etrade-newsletter-wrapper{\r\n  padding: 100px 107px 85px;\r\n  border-radius: 6px;\r\n  @media #{$smlg-device} {\r\n    padding: 80px 30px 65px;\r\n  }\r\n\r\n  @media #{$sm-layout} {\r\n    padding: 60px 20px 45px;\r\n  }\r\n  \r\n}\r\n.newsletter-content {\r\n  .title {\r\n    @media #{$large-mobile} {\r\n      letter-spacing: -0.045em;\r\n      font-size: 30px;\r\n\r\n    }\r\n  }\r\n}\r\n.newsletter-form{\r\n  display: flex;\r\n  align-items: center;\r\n  flex-wrap: wrap;\r\n  @media #{$sm-layout} {\r\n    display: block;\r\n  }\r\n  button{\r\n    width: auto;\r\n    border-radius: 6px !important;\r\n    background-color: var(--color-heading);\r\n    color: var(--color-white);\r\n    padding: 16px 38px 17px;\r\n    &:before {\r\n      background-color: var(--color-heading);\r\n    }\r\n    &:hover {\r\n      background-color: var(--color-secondary);\r\n    }\r\n    @media #{$sm-layout} {\r\n      padding: 16px 35px;\r\n    }\r\n  }\r\n}\r\n.newsletter-inner{\r\n  margin-right: 20px;\r\n  @media #{$sm-layout} {\r\n    margin-right: 0;\r\n  }\r\n  input{\r\n    padding-left: 66px;\r\n    width: 390px;\r\n  }\r\n}\r\n.send-mail-icon {\r\n  position: absolute;\r\n  max-width: 100%;\r\n  top: 17px;\r\n  left: 30px;\r\n}\r\n\r\n", "/* -----------------------\r\nPagination \r\n--------------------------*/\r\n.post-pagination {\r\n  margin-top: 80px;\r\n  width: 100%;\r\n  @media #{$lg-layout} {\r\n      margin-top: 40px;\r\n  }\r\n  @media #{$md-layout} {\r\n      margin-top: 30px;\r\n  }\r\n  @media #{$sm-layout} {\r\n      margin-top: 30px;\r\n  }\r\n  nav {\r\n      &.pagination {\r\n          display: block;\r\n          .screen-reader-text {\r\n              display: none;\r\n          }\r\n          ul {\r\n              position: relative;\r\n              display: flex;\r\n              list-style: none;\r\n              flex-wrap: wrap;\r\n              align-items: center;\r\n              margin: -3px;\r\n              padding: 0;\r\n              li {\r\n                  margin: 3px;\r\n                  span {\r\n                      line-height: 42px;\r\n                      min-width: 42px;\r\n                      text-align: center;\r\n                      color: var(--color-heading);\r\n                      transition: all 0.5s;\r\n                      display: block;\r\n                      padding: 0 15px;\r\n                      transition: all 0.5s;\r\n                      border: 1px solid var(--color-border-light);\r\n                      border-radius: var( --radius-small);\r\n                      &.current {\r\n                          background: var(--color-primary);\r\n                          color: #ffffff;\r\n                          border-color: var(--color-primary);\r\n                      }\r\n                  }\r\n                  a {\r\n                      line-height: 42px;\r\n                      min-width: 42px;\r\n                      text-align: center;\r\n                      color: var(--color-heading);\r\n                      transition: all 0.5s;\r\n                      display: block;\r\n                      padding: 0 15px;\r\n                      transition: all 0.5s;\r\n                      border: 1px solid var(--color-border-light);\r\n                      border-radius: var( --radius-small);\r\n                      &:hover {\r\n                          background: var(--color-primary);\r\n                          color: #ffffff;\r\n                          border-color: var(--color-primary);\r\n                      }\r\n                  }\r\n              }\r\n          }\r\n      }\r\n  }\r\n}", "/*-------------------------\r\nPoster\r\n--------------------------*/\r\n.single-poster {\r\n    position: relative;\r\n    overflow: hidden;\r\n    border-radius: 6px;\r\n    a {\r\n        display: block;\r\n        img {\r\n            transition: 0.5s;\r\n            width: 100%;\r\n        }\r\n    }\r\n    .poster-content {\r\n        position: absolute;\r\n        top: 50%;\r\n        right: 0;\r\n        transform: translateY(-50%);\r\n        z-index: 2;\r\n        padding-right: 100px;\r\n        pointer-events: none;\r\n        @media #{$small-mobile} {\r\n            padding: 20px;\r\n        }\r\n        &.content-left {\r\n            right: auto;\r\n            left: 0;\r\n            padding-right: 0;\r\n            padding-left: 60px;\r\n            @media only screen and (max-width: 991px) {\r\n                padding-left: 30px;\r\n            }\r\n            .title {\r\n                margin-bottom: 0;\r\n                margin-top: 8px;\r\n            }\r\n            .sub-title {\r\n                color: rgba(255, 255, 255, .6);\r\n                transition: all 0.3s ease-in-out;\r\n            }\r\n        }\r\n    }\r\n    .inner {\r\n        .title {\r\n            line-height: 1;\r\n            margin-bottom: 20px;\r\n            font-size: 40px;\r\n            color: var(--color-white);\r\n            @media #{$sm-layout} {\r\n               font-size: 30px;\r\n            }\r\n            @media #{$large-mobile} {\r\n               font-size: 22px;\r\n            }\r\n            @media #{$small-mobile} {\r\n                margin-bottom: 10px;\r\n            }\r\n        }\r\n        .sub-title {    \r\n            font-size: 16px;\r\n            line-height: 24px;\r\n            position: relative;\r\n            color: rgba(255, 255, 255, .4);\r\n            transition: all 0.3s ease-in-out;\r\n            i {\r\n                vertical-align: middle;\r\n                margin-left: 10px;\r\n            }\r\n        }\r\n    }\r\n    &:hover {\r\n        img {\r\n            transform: scale(1.1);\r\n            @media #{$large-mobile} {\r\n                transform: scale(1.25);\r\n            }\r\n        }\r\n        .poster-content {\r\n            .sub-title {\r\n                color: var(--color-white);\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.poster-countdown-wrap {\r\n    padding: 65px 80px;\r\n    border-radius: 6px;\r\n    margin-top: 95px;\r\n    @media #{$smlg-device} {\r\n        margin-top: 0;\r\n    }\r\n    @media only screen and (max-width: 991px) {\r\n        text-align: center;\r\n        .poster-countdown {\r\n            justify-content: center;\r\n        }  \r\n    }\r\n    @media #{$sm-layout} {\r\n        padding: 50px 30px;\r\n    }\r\n    @media #{$small-mobile} {\r\n        padding: 40px 15px;\r\n    }\r\n\r\n    .section-title-wrapper {\r\n        margin-bottom: 0;\r\n        padding-right: 0;\r\n        .title {\r\n            font-size: 48px;\r\n            @media #{$smlg-device} {\r\n                font-size: 40px;\r\n            }\r\n            @media #{$sm-layout} {\r\n                font-size: 28px;\r\n            }\r\n\r\n        }\r\n        .title-highlighter {\r\n            @media only screen and (max-width: 991px) {\r\n                justify-content: center;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.poster-countdown-thumbnail {\r\n    text-align: center;\r\n    margin-top: -160px;\r\n    position: relative;\r\n    @media #{$smlg-device} {\r\n        margin-top: 0;\r\n    }\r\n    @media only screen and (max-width: 991px) {\r\n        margin-top: 50px;  \r\n    }\r\n}\r\n\r\n.music-singnal {\r\n    display: block;\r\n    position: absolute;\r\n    top: 48%;\r\n    left: 45%;\r\n    width: 100px;\r\n    height: 100px;\r\n    transform: rotate(-95deg);\r\n    @media #{$lg-layout} {\r\n        top: 44%;\r\n        left: 40%;\r\n    } \r\n    @media #{$large-mobile} {\r\n        display: none;\r\n    }\r\n\r\n    .item-circle {\r\n        display: block;\r\n        width: 100%;\r\n        height: 100%;\r\n        position: absolute;\r\n        bottom: 0;\r\n        left: 0;\r\n        border-color: var(--color-chart03);\r\n        border-style: solid;\r\n        border-width: 1px 4px 0 0;\r\n        border-radius: 0 100% 0 0;\r\n        opacity: 0;\r\n        animation: signalanimation 2s infinite;\r\n\r\n        &.circle-1 {\r\n            animation-delay: 800ms;\r\n        }\r\n        &.circle-2 {\r\n            width: 80px;\r\n            height: 80px;\r\n            animation-delay: 600ms;\r\n        }\r\n        &.circle-3 {\r\n            width: 60px;\r\n            height: 60px;\r\n            animation-delay: 400ms;\r\n        }\r\n        &.circle-4 {\r\n            width: 40px;\r\n            height: 40px;\r\n            animation-delay: 200ms;\r\n        }\r\n        &.circle-5 {\r\n            width: 20px;\r\n            height: 20px;\r\n            animation-delay: 0ms;\r\n        }\r\n    }\r\n}\r\n\r\n// Sale Banner \r\n.sale-banner-thumb {\r\n    img {\r\n        border-radius: 8px;\r\n    }\r\n}\r\n\r\n\r\n// Delivery Poster\r\n.delivery-poster-area {\r\n    padding-bottom: 50px;\r\n}\r\n.delivery-poster {\r\n    display: flex;\r\n    background: linear-gradient(97.29deg, #FFFFFF -1.43%, rgba(230, 222, 255, 0.634676) 43.99%, rgba(242, 238, 255, 0) 111.12%);\r\n    border: 3px solid #EAE3FF;\r\n    border-radius: 8px;\r\n    padding: 30px 70px 12px 30px;\r\n    margin-bottom: 30px;\r\n    @media (max-width: 575px) {\r\n        padding: 30px;\r\n    }\r\n    .content {\r\n        flex: 1;\r\n        padding-right: 50px;\r\n        @media (max-width: 575px) {\r\n            padding-right: 0;\r\n        }\r\n        .badge {\r\n            font-size: 12px;\r\n            font-weight: 500;\r\n            color: var(--color-white);\r\n            background-color: var(--color-secondary);\r\n            padding: 8px 10px;\r\n            border-radius: 4px;\r\n            display: inline-block;\r\n            text-transform: uppercase;\r\n            margin-bottom: 34px;\r\n        }\r\n        .title {\r\n            font-weight: 700;\r\n            color: var(--color-primary);\r\n            margin-bottom: 8px;\r\n        }\r\n        p {\r\n            margin-bottom: 0;\r\n            color: var(--color-heading);\r\n        }\r\n    }\r\n    .thumbnail {\r\n        @media (max-width: 575px) {\r\n            display: none;\r\n        }\r\n    }\r\n    &.delivery {\r\n        background: linear-gradient(97.29deg, #FFFFFF -1.43%, rgba(255, 224, 222, 0.64) 43.99%, rgba(242, 238, 255, 0) 111.12%);\r\n        \r\n    }\r\n}", "/*-------------------------\r\nPrice Slider\r\n--------------------------*/\r\n\r\n.ui-slider-handle.ui-state-default.ui-corner-all {\r\n    background: #5956E9 none repeat scroll 0 0;\r\n    border: medium none;\r\n    border-radius: 50%;\r\n    height: 13px;\r\n    margin-left: -1px;\r\n    top: 50%;\r\n    -webkit-transform: translateY(-50%);\r\n    transform: translateY(-50%);\r\n    width: 13px;\r\n}\r\n\r\n.ui-slider-range.ui-widget-header.ui-corner-all {\r\n    background: #DBDEFF none repeat scroll 0 0;\r\n    border-radius: 0;\r\n    height: 4px;\r\n}\r\n\r\n.ui-widget.ui-widget-content {\r\n    background: #CBD3D9 none repeat scroll 0 0;\r\n    border: medium none;\r\n    height: 4px;\r\n}\r\n\r\n.ui-slider-horizontal .ui-slider-handle {\r\n    margin-left: 0;\r\n}\r\n\r\n.ui-slider-handle.ui-corner-all.ui-state-default.ui-state-focus {\r\n    outline: medium none;\r\n}\r\n\r\n.amount-range {\r\n    background: rgba(0, 0, 0, 0) none repeat scroll 0 0;\r\n    border: medium none;\r\n    color: #666;\r\n    font-size: 15px;\r\n    margin-top: 10px;\r\n    padding: 5px 0;\r\n}\r\n\r\n.amount-range,\r\n.price-button {\r\n    width: 100%;\r\n    word-spacing: 10px;\r\n    padding: 0!important;\r\n    font-weight: 500!important;\r\n    font-size: 16px!important;\r\n    line-height: 1!important;\r\n    margin-top: 0!important;\r\n}\r\n\r\n.input-range {\r\n    font-weight: 500;\r\n    padding-right: 3px;\r\n}\r\n\r\n.flter-option.mb-80 {\r\n    padding-right: 15px;\r\n}", "/*-------------------------\r\nPrivacy Policy\r\n--------------------------*/\r\n.axil-privacy-policy {\r\n\t.policy-published {\r\n\t\tfont-size: 22px;\r\n\t\tfont-weight: var(--s-medium);\r\n\t\tcolor: var(--color-dark);\r\n\t\tposition: relative;\r\n\t\tpadding-bottom: 20px;\r\n\t\tmargin-bottom: 70px;\r\n\t\tdisplay: inline-block;\r\n\t\t&:after {\r\n\t\t\tcontent: \"\";\r\n\t\t\theight: 3px;\r\n\t\t\twidth: 100%;\r\n\t\t\tbackground-color: var(--color-primary);\r\n\t\t\tposition: absolute;\r\n\t\t\tbottom: 0;\r\n\t\t\tleft: 0;\r\n\t\t\tright: 0;\r\n\t\t}\r\n\t}\r\n\t.title {\r\n\t\tmargin-bottom: 20px;\r\n\t\tcolor: var(--color-dark);\r\n\t\tfont-weight: var(--s-medium);\r\n\t}\r\n\r\n\ta {\r\n\t\tcolor: var(--color-primary);\r\n\t}\r\n\tul {\r\n\t\tlist-style: disc;\r\n\t\tmargin-bottom: 45px;\r\n\t\tli {\r\n\t\t\tmargin: 0 0 12px 0;\r\n\t\t\t&::marker {\r\n\t\t\t\tcolor: var(--color-light);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n", "\r\n/*-------------------------\r\nSection Heading  \r\n--------------------------*/\r\n.title-highlighter {\r\n    font-size: 14px;\r\n    font-weight: var(--s-bold);\r\n    line-height: 1;\r\n    display: flex;\r\n\talign-items: center;\r\n\tmargin-bottom: 10px;\r\n    i {\r\n      height: 24px;\r\n      width: 24px;\r\n      line-height: 24px;\r\n      border-radius: 50%;\r\n      font-size: 12px;\r\n      text-align: center;\r\n      margin-right: 10px;\r\n      \r\n    }\r\n\t&.highlighter-primary {\r\n\t\tcolor: var(--light-primary);\r\n\t\ti {\r\n\t\tbackground-color: var(--light-primary);\r\n\t\tcolor: var(--color-white);\r\n\t\t}\r\n\t}\r\n\t&.highlighter-primary2 {\r\n\t\tcolor: var(--color-primary);\r\n\t\ti {\r\n\t\tbackground-color: var(--color-primary);\r\n\t\tcolor: var(--color-white);\r\n\t\t}\r\n\t}\r\n\t&.highlighter-secondary {\r\n\t\tcolor: var(--color-secondary);\r\n\t\ti {\r\n\t\t\tbackground-color: var(--color-secondary);\r\n\t\t\tcolor: var(--color-white);\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.section-title-wrapper {\r\n   margin-bottom: 40px;\r\n   padding-right: 100px;\r\n   @media only screen and (max-width: 767px) {\r\n\t   margin-bottom: 30px;\r\n\t   \r\n   }\r\n   &.section-title-center {\r\n\t   text-align: center;\r\n\t   padding-right: 0;\r\n\t   .title-highlighter {\r\n\t\t   justify-content: center;\r\n\t   }\r\n   }\r\n}\r\n\r\n.section-title-border {\r\n\tborder-bottom: 1px solid #EBEBEB;\r\n\tmargin-bottom: 30px;\r\n\tpadding-bottom: 25px;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: space-between;\r\n\tpadding-right: 0;\r\n\t.title {\r\n\t\tmargin-bottom: 0;\r\n\t\tpadding-right: 20px;\r\n\t\tflex: 1;\r\n\t}\r\n\t.view-btn {\r\n\t\ta {\r\n\t\t\tcolor: var(--color-primary);\r\n\t\t\ttext-decoration: underline;\r\n\t\t\tfont-weight: 700;\r\n\t\t\ttransition: 0.3s;\r\n\t\t\t&:hover {\r\n\t\t\t\tcolor: var(--color-secondary);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t&.slider-section-title {\r\n\t\t.title {\r\n\t\t\tpadding-right: 100px;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.flash-sale-section {\r\n\tmargin-bottom: 60px;\r\n\t@media #{$sm-layout} {\r\n\t\tmargin-bottom: 50px;\r\n\t}\r\n\t.section-title-wrapper {\r\n\t\tmargin-bottom: 0;\r\n\t\t@media only screen and (max-width: 991px) {\r\n\t\t\tpadding-right: 80px;\r\n\t\t}\r\n\t\t@media #{$sm-layout} {\r\n\t\t\tpadding-right: 100px;\r\n\t\t\tmargin-bottom: 30px;\r\n\t\t}\r\n\t\t.title {\r\n\t\t\tmargin-bottom: 0;\r\n\t\t}\r\n\t}\r\n}", "/*-------------------------\r\nService\r\n--------------------------*/\r\n.service-box {\r\n    background-color: var(--color-white);\r\n    border: 1px solid #f1f1f1;\r\n    padding: 50px 30px;\r\n    text-align: center;\r\n    margin-bottom: 30px;\r\n    border-radius: 6px;\r\n    position: relative;\r\n    transition: all .3s ease-in-out;\r\n    @media only screen and (min-width: 1400px) {\r\n        padding: 50px 40px;\r\n    }\r\n    @media #{$smlg-device} {\r\n        padding: 40px 30px;\r\n    }\r\n    @media #{$sm-layout} {\r\n        padding: 40px;\r\n    }\r\n    &:before {\r\n        content: \"\";\r\n        height: 100%;\r\n        width: 90%;\r\n        border: 1px solid #f1f1f1;\r\n        border-radius: 6px;\r\n        position: absolute;\r\n        bottom: 0;\r\n        left: 0;\r\n        right: 0;\r\n        margin: 0 auto;\r\n        z-index: -1;\r\n        visibility: hidden;\r\n        opacity: 0;\r\n        transition: all .3s ease-in-out;\r\n    }\r\n    .icon {\r\n        margin-bottom: 20px;\r\n        img {\r\n            max-height: 60px;\r\n        }\r\n    }\r\n    .title {\r\n        font-size: 16px;\r\n        line-height: 26px;\r\n        font-weight: 700;\r\n        margin-bottom: 0;\r\n    }\r\n    &:hover {\r\n        box-shadow: var(--shadow-dark);\r\n        &:before {\r\n            visibility: visible;\r\n            opacity: 1;\r\n            bottom: -12px;\r\n        }\r\n    }\r\n    &.service-style-2 {\r\n        display: flex;\r\n        border: none;\r\n        padding: 0;\r\n        text-align: left;\r\n        background-color: transparent;\r\n        &:before {\r\n            display: none;\r\n        }\r\n        .icon {\r\n            margin-right: 20px;\r\n            margin-bottom: 0;\r\n            max-width: 45px;\r\n            margin-top: 6px;\r\n        }\r\n        .content {\r\n            flex: 1;\r\n            .title {\r\n                line-height: var(--line-height-b1);\r\n            }\r\n        }\r\n        &:hover {\r\n            box-shadow: none;\r\n        }\r\n    }\r\n    &.service-style-3 {\r\n        background-color: #F6F7FB;\r\n        border: none;\r\n        border-radius: 8px;\r\n        padding: 60px 30px 40px;\r\n        &:before {\r\n            display: none;\r\n        }\r\n        .icon {\r\n            position: relative;\r\n            z-index: 1;\r\n            margin-bottom: 40px;\r\n            &:before {\r\n                content: \"\";\r\n                width: 99px;\r\n                height: 99px;\r\n                background-color: var(--color-white);\r\n                border-radius: 50%;\r\n                position: absolute;\r\n                top: 50%;\r\n                transform: translateY(-50%);\r\n                left: 0;\r\n                right: 0;\r\n                margin: 0 auto;\r\n                z-index: -1;\r\n            }\r\n            i {\r\n                font-size: 50px;\r\n                color: var(--color-primary);\r\n            }\r\n        }\r\n        .content {\r\n            .title {\r\n                margin-bottom: 4px;\r\n                font-weight: 700;\r\n            }\r\n            p {\r\n                margin-bottom: 0;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n\r\n// How To Sell\r\n.how-to-sell {\r\n    padding: 30px 20px;\r\n    .title {\r\n        font-size: 18px;\r\n        margin-bottom: 10px;\r\n    }\r\n    p {\r\n        font-size: 16px;\r\n    }\r\n}", "/*----------------------------\r\nSlick Style  \r\n------------------------------*/\r\n\r\n.slick-layout-wrapper--20 {\r\n    .slick-list {\r\n        margin: -20px;\r\n        @media #{$large-mobile} {\r\n            margin: -20px -10px;\r\n        }\r\n    }\r\n    .slick-single-layout {\r\n        padding: 20px;\r\n        @media #{$large-mobile} {\r\n            padding: 20px 10px;\r\n        }\r\n    }\r\n}\r\n\r\n.slick-layout-wrapper--10 {\r\n    .slick-list {\r\n        margin: 0 -10px;\r\n    }\r\n    .slick-single-layout,\r\n    .slick-slide {\r\n        padding: 0 10px;\r\n    }\r\n}\r\n\r\n.slick-layout-wrapper--15 {\r\n    .slick-list {\r\n        margin: 0 -15px;\r\n    }\r\n    .slick-single-layout,\r\n    .slick-slide {\r\n        padding: 0 15px;\r\n    }\r\n}\r\n\r\n.slick-layout-wrapper--30 {\r\n    .slick-list {\r\n        margin: 0 -30px;\r\n        @media #{$sm-layout} {\r\n            margin: 0 -15px;\r\n        }\r\n    }\r\n    .slick-single-layout {\r\n        padding: 0 30px;\r\n        @media #{$sm-layout} {\r\n            padding: 0 15px;\r\n        }\r\n    }\r\n}\r\n\r\n.axil-gallery-activation {\r\n    position: relative;\r\n    &.axil-slick-arrow {\r\n        .slide-arrow {\r\n            background: #fff;\r\n            border: 1px solid #fff;\r\n        }\r\n    }\r\n    &.arrow-between-side {\r\n        .slide-arrow {\r\n            left: 10px;\r\n            &.next-arrow {\r\n                left: auto;\r\n                right: 10px;\r\n            }\r\n        }\r\n        &:hover {\r\n            .slide-arrow {\r\n                left: 30px;\r\n                &.next-arrow {\r\n                    left: auto;\r\n                    right: 30px;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n/* Axil Slick Arrow  */\r\n\r\n.axil-slick-arrow {\r\n    .slide-arrow {\r\n        outline: none;\r\n        width: 50px;\r\n        height: 50px;\r\n        background: var(--color-lighter);\r\n        color: var(--color-body);\r\n        border: none;\r\n        border-radius: 6px;\r\n        z-index: 2;\r\n        &:before {\r\n            content: \"\";\r\n            height: 100%;\r\n            width: 100%;\r\n            background-color: var(--color-lighter);\r\n            border-radius: 6px;\r\n            position: absolute;\r\n            top: 0;\r\n            bottom: 0;\r\n            left: 0;\r\n            right: 0;\r\n            z-index: -1;\r\n            transition: transform .5s cubic-bezier(.165,.84,.44,1);\r\n        }\r\n        &:hover {\r\n            &:before {\r\n                transform: scale(1.1);\r\n            }\r\n        }\r\n        @media #{$sm-layout} {\r\n            width: 40px;\r\n            height: 40px;\r\n            font-size: var(--font-size-b2);\r\n        }\r\n        &.prev-arrow {\r\n            &:hover {\r\n                i {\r\n                    animation: prevNavSlide 400ms;\r\n                }\r\n            }\r\n        }\r\n        &.next-arrow {\r\n            &:hover {\r\n                i {\r\n                    animation: nextNavSlide 400ms;\r\n                }\r\n            }\r\n        }\r\n    }\r\n    &.testimonial-style-two-wrapper {\r\n        .slide-arrow {\r\n            background-color: var(--color-white);\r\n           \r\n            &:hover {\r\n                background-color: var(--color-primary);\r\n            }\r\n        }\r\n    }\r\n    &.header-campaign-arrow {\r\n        .slide-arrow {\r\n            background: transparent;\r\n            color: rgba($color: #ffffff, $alpha: .8);\r\n            &:before {\r\n                display: none;\r\n            }\r\n            &:hover {\r\n                color: var(--color-white);\r\n            }\r\n        }\r\n        .campaign-content {\r\n            margin: 0 50px;\r\n            @media #{$small-mobile} {\r\n                margin: 0 30px;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.axil-slick-angle {\r\n    .slide-arrow {\r\n        outline: none;\r\n        width: 40px;\r\n        height: 40px;\r\n        background: var(--color-lighter);\r\n        color: #D6D6D6;\r\n        border: none;\r\n        border-radius: 50%;\r\n        z-index: 2;\r\n        &:before {\r\n            content: \"\";\r\n            height: 100%;\r\n            width: 100%;\r\n            background-color: var(--color-lighter);\r\n            border-radius: 50%;\r\n            position: absolute;\r\n            top: 0;\r\n            bottom: 0;\r\n            left: 0;\r\n            right: 0;\r\n            z-index: -1;\r\n            transition: transform .5s cubic-bezier(.165,.84,.44,1);\r\n        }\r\n        &:hover {\r\n            color: var(--color-body);\r\n            &:before {\r\n                transform: scale(1.1);\r\n            }\r\n        }\r\n        @media #{$sm-layout} {\r\n            width: 40px;\r\n            height: 40px;\r\n            font-size: var(--font-size-b2);\r\n        }\r\n        &.prev-arrow {\r\n            &:hover {\r\n                i {\r\n                    animation: prevNavSlide 400ms;\r\n                }\r\n            }\r\n        }\r\n        &.next-arrow {\r\n            &:hover {\r\n                i {\r\n                    animation: nextNavSlide 400ms;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.angle-top-slide {\r\n    .slide-arrow {\r\n        position: absolute;\r\n        left: 19px;\r\n        top: -100px;\r\n        @media #{$sm-layout} {\r\n            top: -90px;\r\n        }\r\n        &.next-arrow {\r\n            left: auto;\r\n            right: 0;\r\n        }\r\n        &.prev-arrow {\r\n            left: auto;\r\n            right: 55px;\r\n            @media #{$sm-layout} {\r\n                right: 50px;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n\r\n/* Arrow Between Slide  */\r\n\r\n.arrow-between-side {\r\n    .slide-arrow {\r\n        position: absolute;\r\n        left: 40px;\r\n        top: 50%;\r\n        transform: translateY(-50%);\r\n        @extend %transition;\r\n        &.next-arrow {\r\n            left: auto;\r\n            right: 40px;\r\n        }\r\n    }\r\n    &:hover {\r\n        .slide-arrow {\r\n            left: 10px;\r\n            @media #{$sm-layout} {\r\n                left: 14px;\r\n            }\r\n            &.next-arrow {\r\n                left: auto;\r\n                right: 10px;\r\n                @media #{$sm-layout} {\r\n                    right: 14px;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.arrow-both-side {\r\n    .slide-arrow {\r\n        position: absolute;\r\n        left: -45px;\r\n        top: 50%;\r\n        transform: translateY(-50%);\r\n        @extend %transition;\r\n        @media #{$laptop-device} {\r\n            left: 0;\r\n        }\r\n        @media #{$smlg-device} {\r\n            left: 0;\r\n        }\r\n\r\n        &.next-arrow {\r\n            left: auto;\r\n            right: -45px;\r\n            @media #{$laptop-device} {\r\n                right: 0;\r\n            }\r\n            @media #{$smlg-device} {\r\n                right: 0;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.arrow-both-side-2 {\r\n    .slide-arrow {\r\n        position: absolute;\r\n        left: -68px;\r\n        top: 50%;\r\n        transform: translateY(-50%);\r\n        @extend %transition;\r\n        @media #{$laptop-device} {\r\n            left: 0;\r\n        }\r\n        @media #{$smlg-device} {\r\n            left: 0;\r\n        }\r\n\r\n        &.next-arrow {\r\n            left: auto;\r\n            right: -75px;\r\n            @media #{$laptop-device} {\r\n                right: 0;\r\n            }\r\n            @media #{$smlg-device} {\r\n                right: 0;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.arrow-both-side-3 {\r\n    .slide-arrow {\r\n        position: absolute;\r\n        left: -60px;\r\n        top: 50%;\r\n        transform: translateY(-50%);\r\n        @extend %transition;\r\n        @media #{$laptop-device} {\r\n            left: 0;\r\n        }\r\n        @media #{$smlg-device} {\r\n            left: 0;\r\n        }\r\n\r\n        &.next-arrow {\r\n            left: auto;\r\n            right: -60px;\r\n            @media #{$laptop-device} {\r\n                right: 0;\r\n            }\r\n            @media #{$smlg-device} {\r\n                right: 0;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.arrow-both-side-4 {\r\n    .slide-arrow {\r\n        position: absolute;\r\n        left: 0;\r\n        top: 50%;\r\n        transform: translateY(-50%);\r\n        background-color: transparent;\r\n        height: auto;\r\n        width: auto;\r\n        padding: 0;\r\n        @extend %transition;\r\n        &:hover {\r\n            background-color: transparent;\r\n            box-shadow: none;\r\n        }\r\n        &.next-arrow {\r\n            left: auto;\r\n            right: 0;\r\n        }\r\n    }\r\n}\r\n\r\n/* Arrow Top Slide  */\r\n\r\n.arrow-top-slide {\r\n    .slide-arrow {\r\n        position: absolute;\r\n        left: 19px;\r\n        top: -100px;\r\n        @media #{$sm-layout} {\r\n            top: -77px;\r\n        }\r\n        &.next-arrow {\r\n            left: auto;\r\n            right: 0;\r\n        }\r\n        &.prev-arrow {\r\n            left: auto;\r\n            right: 58px;\r\n            @media #{$sm-layout} {\r\n                right: 50px;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.post-list-view {\r\n    .post-gallery-activation {\r\n        &.axil-slick-arrow {\r\n            .slide-arrow {\r\n                background: #fff;\r\n                border: 1px solid #fff;\r\n                width: 30px;\r\n                height: 30px;\r\n                font-size: 14px;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.slick-initialized .slick-slide {\r\n    outline: none;\r\n    margin-bottom: 0;\r\n}\r\n\r\n.slider-activation .slick-dots {\r\n    bottom: 50px;\r\n}\r\n\r\n.arrow-bottom-slide {\r\n    .slide-arrow {\r\n        position: absolute;\r\n        bottom: -130px;\r\n        &.next-arrow {\r\n            left: 65px;\r\n        }\r\n        &.prev-arrow {\r\n            left: 0;\r\n        }\r\n    }\r\n}\r\n\r\n// Pagination\r\n.axil-slick-dots {\r\n    .slick-dots {\r\n        bottom: -50px;\r\n        li {\r\n            margin: 0 5px;\r\n            height: 4px;\r\n            width: 24px;\r\n            button {\r\n                height: 4px;\r\n                width: 24px;\r\n                border-radius: 6px;\r\n                background-color: #e6e6e6;\r\n                padding: 0;\r\n                &:before {\r\n                    display: none;\r\n                }\r\n            }\r\n            &.slick-active {\r\n                width: 34px;\r\n                button {\r\n                    background-color: var(--color-heading);\r\n                    width: 34px;\r\n                }\r\n            }\r\n        }\r\n    }\r\n    &.testimonial-style-two-wrapper {\r\n        .slick-dots {\r\n            text-align: left;\r\n            bottom: -50px;\r\n           li {\r\n               &.slick-active {\r\n                   button {\r\n                       background-color: var(--color-primary);\r\n                   }\r\n               }\r\n           }\r\n        }\r\n    }\r\n    &.slick-dots-bottom {\r\n        .slick-dots {\r\n            bottom: 20px;\r\n            li {\r\n                button {\r\n                    border-color: var(--color-white);\r\n                    &:before {\r\n                        color: var(--color-white);\r\n                        opacity: 1;\r\n                    }\r\n                }\r\n                &.slick-active {\r\n                    button {\r\n                        border-color: var(--color-primary);\r\n                        &:before {\r\n                            color: var(--color-primary);\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}", "/*-------------------------\r\nSlider Style\r\n--------------------------*/\r\n.main-slider-content {\r\n    .subtitle {\r\n        font-size: 14px;\r\n        font-weight: var(--s-bold);\r\n        line-height: 1;\r\n        display: flex;\r\n        align-items: center;\r\n        margin-bottom: 16px;\r\n        color: var(--color-secondary);\r\n        i {\r\n            height: 24px;\r\n            width: 24px;\r\n            line-height: 24px;\r\n            border-radius: 50%;\r\n            font-size: 12px;\r\n            text-align: center;\r\n            margin-right: 10px;\r\n            background-color: var(--color-secondary);\r\n            color: var(--color-white);\r\n        }\r\n    }\r\n    .title {\r\n        letter-spacing: -0.03em;\r\n        margin-bottom: 50px;\r\n        font-size: 60px;\r\n        line-height: 1.2;\r\n        @media #{$smlg-device} {\r\n            font-size: 50px;\r\n        }\r\n        @media #{$md-layout} {\r\n            font-size: 44px;\r\n        }\r\n        @media #{$sm-layout} {\r\n            font-size: 30px;\r\n        }\r\n    }\r\n    .slide-action {\r\n        display: flex;\r\n        align-items: center;\r\n        @media #{$smlg-device} {\r\n            display: block;\r\n        }\r\n    }\r\n    .item-rating {\r\n        display: flex;\r\n        align-items: center;\r\n        flex: 1;\r\n        margin-left: 30px;\r\n        position: relative;\r\n        top: 2px;\r\n        @media #{$smlg-device} {\r\n            margin-left: 0;\r\n            margin-top: 30px;\r\n        }\r\n        .thumb {\r\n            line-height: 1;\r\n            ul {\r\n                padding-left: 26px;\r\n                margin: 0;\r\n                li {\r\n                    @extend %liststyle;\r\n                    display: inline-block;\r\n                    margin-left: -26px;\r\n                    img {\r\n                        border: 2px solid #f9f3f0;\r\n                        border-radius: 50%;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        .content {\r\n            flex: 1;\r\n            line-height: 1;\r\n            margin-left: 15px;\r\n            .rating-icon {\r\n                display: block;\r\n                font-size: 12px;\r\n                color: #ffa800;\r\n                margin-bottom: 5px;\r\n            }\r\n            .review-text {\r\n                font-size: 12px;\r\n                span {\r\n                    color: var(--color-heading);\r\n                    font-weight: 700;\r\n                }\r\n            }\r\n        }\r\n    }\r\n    .shop-btn {\r\n        a {\r\n            margin-right: 25px;\r\n            @media #{$sm-layout} {\r\n                margin-right: 5px;\r\n            }\r\n            &:last-child {\r\n                margin-right: 0;\r\n            }\r\n        }\r\n    }\r\n    .slick-slide.slick-current {\r\n        .subtitle {\r\n            animation: 800ms ease-in-out 0s normal none 1 running customOne;\r\n        }\r\n        .title {\r\n            animation: 1000ms ease-in-out 0s normal none 1 running customOne;\r\n        }\r\n        .shop-btn {\r\n            animation: 1200ms ease-in-out 0s normal none 1 running customOne;\r\n        }\r\n        .item-rating {\r\n            animation: 1200ms ease-in-out 0s normal none 1 running customOne;\r\n        }\r\n        img {\r\n            animation: 1200ms ease-in-out 0s normal none 1 running customOne;\r\n        }\r\n    }\r\n}\r\n\r\n.main-slider-large-thumb {\r\n    width: 70vw;\r\n    @media only screen and (max-width: 991px) {\r\n        width: auto;\r\n    }\r\n    .slick-track {\r\n        display: flex;\r\n        align-items: flex-end;\r\n\r\n    }\r\n    .single-slide {\r\n        position: relative;\r\n        img {\r\n            display: inline-block;\r\n        }\r\n        .product-price {\r\n            height: 100px;\r\n            width: 100px;\r\n            background-color: var(--color-white);\r\n            border-radius: 50%;\r\n            display: flex;\r\n            flex-direction: column;\r\n            justify-content: center;\r\n            text-align: center;\r\n            position: absolute;\r\n            top: 50px;\r\n            left: 150px;\r\n            visibility: hidden;\r\n            opacity: 0;\r\n            transform: scale(0);\r\n            transition-delay: 0.5s;\r\n            transition: all .5s ease-in-out;\r\n            @media only screen and (min-width: 1600px) and (max-width: 1920px) {\r\n                top: 100px;\r\n                left: 200px;\r\n            }\r\n            span {\r\n                &.text {\r\n                    color: var(--color-body);\r\n                }\r\n                &.price-amount {\r\n                    font-size: 18px;\r\n                    font-weight: 700;\r\n                    color: var(--color-primary);\r\n                }\r\n            }\r\n\r\n        }\r\n        &.slick-current {\r\n            .product-price {\r\n                visibility: visible;\r\n                opacity: 1;\r\n                transform: scale(1);\r\n            }\r\n        }\r\n    }\r\n    .single-slide.slick-active:not(.slick-current) {\r\n        img {\r\n            width: 160px;\r\n        }\r\n    }\r\n    .axil-slick-dots {\r\n        .slick-dots {\r\n            text-align: left;\r\n            bottom: -70px;\r\n            @media #{$large-mobile} {\r\n                text-align: center;\r\n                bottom: -55px;\r\n            }\r\n            li {\r\n                button {\r\n                    background-color: var(--color-white);\r\n                    opacity: .5;\r\n                }\r\n                &.slick-active {\r\n                    button {\r\n                        background-color: var(--color-secondary);\r\n                        opacity: 1;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.main-slider-style-1 {\r\n    background-color: #f9f3f0;\r\n    padding: 90px 0;\r\n    position: relative;\r\n    z-index: 1;\r\n    overflow: hidden;\r\n    min-height: 550px;\r\n    @media #{$smlg-device} {\r\n        min-height: 500px;\r\n    }\r\n    @media only screen and (max-width: 991px) {\r\n        padding: 40px 0 80px;\r\n        min-height: 450px;\r\n    }\r\n    @media #{$sm-layout} {\r\n        padding: 30px 0 60px;\r\n        min-height: 100%;\r\n    }\r\n    .main-slider-content {\r\n        margin: -30px;\r\n        @media #{$large-mobile} {\r\n            text-align: center;\r\n        }\r\n        .subtitle {\r\n            @media #{$large-mobile} {\r\n               justify-content: center;\r\n            }\r\n        }\r\n        .title {\r\n            @media #{$smlg-device} {\r\n                font-size: 56px;\r\n            }\r\n            @media #{$md-layout} {\r\n                font-size: 44px;\r\n                margin-bottom: 40px;\r\n            }\r\n            @media #{$sm-layout} {\r\n                font-size: 34px;\r\n                margin-bottom: 30px;\r\n            }\r\n\r\n        }\r\n        .item-rating {\r\n            @media #{$smlg-device} {\r\n                justify-content: flex-start;\r\n            }\r\n            @media #{$large-mobile} {\r\n                justify-content: center;\r\n            }\r\n            .content {\r\n                flex: initial;\r\n            }\r\n        }\r\n        .single-slide {\r\n            padding: 30px;\r\n        }\r\n    }\r\n    .main-slider-large-thumb {\r\n        @media #{$large-mobile} {\r\n            text-align: center;\r\n            padding: 40px 40px 0;\r\n        }\r\n        .single-slide {\r\n            .product-price {\r\n                @media #{$sm-layout} {\r\n                    height: 80px;\r\n                    width: 80px;\r\n                    top: 0;\r\n                    left: 0;\r\n                    span {\r\n                        font-size: 15px;\r\n                        &.price-amount {\r\n                            font-size: 15px;\r\n                        }\r\n                    }\r\n                }    \r\n                @media #{$large-mobile} {\r\n                    left: 50px;\r\n                }    \r\n            }\r\n        }\r\n    }\r\n    .shape-group {\r\n        li {\r\n            position: absolute;\r\n            z-index: -1;\r\n            @extend %liststyle;\r\n            &.shape-1 {\r\n                bottom: -100px;\r\n                right: 33%;\r\n                @media #{$laptop-device} {\r\n                    right: 27%;\r\n                }\r\n                @media #{$smlg-device} {\r\n                    right: 20%;\r\n                }\r\n                @media only screen and (max-width: 991px) {\r\n                    display: none;\r\n                }\r\n            }\r\n            &.shape-2 {\r\n                bottom: -65px;\r\n                right: 2%;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.main-slider-style-2 {\r\n    .slider-offset-left {\r\n        margin-left: 290px;\r\n        margin-top: 40px;\r\n        margin-bottom: 40px;\r\n        @media #{$smlg-device} {\r\n            margin-left: 0;\r\n        }\r\n    }\r\n    .slider-box-wrap {\r\n        background-color: #f7f7f7;\r\n        border-radius: 6px;\r\n        padding: 48px 50px;\r\n        @media #{$large-mobile} {\r\n            padding: 40px 30px;\r\n\r\n        }\r\n        .single-slide {\r\n            display: flex;\r\n            align-items: center;\r\n        }\r\n        .axil-slick-dots {\r\n            .slick-dots {\r\n                bottom: -25px;\r\n                @media #{$small-mobile} {\r\n                    bottom: -30px;\r\n                }\r\n                li {\r\n                    button {\r\n                        background-color: var(--color-white);\r\n                        opacity: 1;\r\n                    }\r\n                    &.slick-active {\r\n                        button {\r\n                            background-color: var(--color-secondary);\r\n                            opacity: 1;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n    }\r\n    .main-slider-content {\r\n        flex: 1;\r\n        z-index: 2;\r\n        position: relative;\r\n        @media #{$sm-layout} {\r\n            flex: 2;\r\n        }\r\n        .title {\r\n            font-size: 50px;\r\n            margin-bottom: 45px;\r\n            @media #{$smlg-device} {\r\n                font-size: 40px;\r\n            }\r\n            @media #{$sm-layout} {\r\n                font-size: 38px;\r\n                margin-bottom: 30px;\r\n            }\r\n            @media #{$large-mobile} {\r\n                font-size: 24px;\r\n                margin-bottom: 20px;\r\n            }\r\n        }\r\n        .axil-btn {\r\n            padding: 0;\r\n            position: relative;\r\n            i {\r\n                margin: 0 0 0 16px;\r\n                color: var(--color-heading);\r\n            }\r\n            &:after {\r\n                content: \"\";\r\n                height: 1px;\r\n                width: 100%;\r\n                background-color: var(--color-heading);\r\n                position: absolute;\r\n                bottom: 0;\r\n                left: 0;\r\n            }\r\n            &:hover {\r\n                color: var(--color-primary);\r\n                i {\r\n                    color: var(--color-primary);\r\n                    margin: 0 0 0 10px;\r\n                }\r\n                &:after {\r\n                    background-color: var(--color-primary);\r\n                }\r\n            }\r\n        }\r\n    }\r\n    .main-slider-thumb {\r\n        position: relative;\r\n        margin-left: 30px;\r\n        z-index: 1;\r\n        flex: 1;\r\n        text-align: right;\r\n        @media #{$small-mobile} {\r\n            margin-left: 10px;\r\n        }\r\n        &::after {\r\n            content: \"\";\r\n            height: 288px;\r\n            width: 288px;\r\n            background-color: var(--color-white);\r\n            border-radius: 50%;\r\n            position: absolute;\r\n            top: -5px;\r\n            left: -100px;\r\n            z-index: -1;\r\n            @media #{$sm-layout} {\r\n                height: 150px;\r\n                width: 150px;\r\n                left: 0;\r\n                top: 0;\r\n            }\r\n            @media #{$large-mobile} {\r\n                display: none;\r\n            }\r\n        }\r\n        img {\r\n            display: inline-block;\r\n            max-height: 292px;\r\n        }\r\n\r\n    }\r\n    .slider-product-box {\r\n        background-color: #f7f7f7;\r\n        border-radius: 6px;\r\n        text-align: center;\r\n        padding: 45px 20px;\r\n        overflow: hidden;\r\n        @media only screen and (max-width: 991px) {\r\n            margin-top: 30px;\r\n        }\r\n        .product-thumb {\r\n            margin-bottom: 35px;\r\n            position: relative;\r\n            z-index: 1;\r\n            &::after {\r\n                content: \"\";\r\n                height: 160px;\r\n                width: 160px;\r\n                background-color: var(--color-white);\r\n                border-radius: 50%;\r\n                position: absolute;\r\n                top: 20px;\r\n                right: -10px;\r\n                z-index: -1;\r\n                @media only screen and (max-width: 991px) {\r\n                    left: 50%;\r\n                    transform: translateX(-50%);\r\n                }\r\n            }\r\n        }\r\n        .title {\r\n            font-size: 16px;\r\n            color: var(--color-body);\r\n            margin-bottom: 8px;\r\n            overflow: hidden;\r\n            text-overflow: ellipsis;\r\n            display: -webkit-box;\r\n            -webkit-line-clamp: 1;\r\n            -webkit-box-orient: vertical;\r\n        }\r\n        .price {\r\n            font-size: 20px;\r\n            font-weight: 700;\r\n            color: var(--color-heading);\r\n        }\r\n    }\r\n}\r\n\r\n.main-slider-style-3 {\r\n    background-color: #f3f7f6;\r\n    padding: 110px 0;\r\n    background-image: url('../../assets/images/bg/bg-image-4.jpg');\r\n    background-repeat: no-repeat;\r\n    background-size: cover;\r\n    @media #{$md-layout} {\r\n        padding: 80px 0;\r\n    }\r\n    @media #{$sm-layout} {\r\n        padding: 40px 0 70px;\r\n    }\r\n    .main-slider-content {\r\n        padding-right: 80px;\r\n        @media only screen and (max-width: 991px) {\r\n            padding-right: 0;\r\n            margin-bottom: 50px;\r\n        }\r\n        @media #{$sm-layout} {\r\n            margin-bottom: 25px;\r\n        }\r\n        .title {\r\n            margin-bottom: 40px;\r\n            font-size: 55px;\r\n            line-height: 1.1;\r\n            @media #{$laptop-device} {\r\n                font-size: 60px;\r\n            }\r\n            @media #{$smlg-device} {\r\n                font-size: 50px;\r\n            }\r\n            @media #{$md-layout} {\r\n                font-size: 40px;\r\n            }\r\n            @media #{$sm-layout} {\r\n                font-size: 34px;\r\n            }\r\n\r\n        }\r\n    }\r\n    .main-slider-large-thumb {\r\n        width: 100%;\r\n        position: relative;\r\n        z-index: 1;\r\n        .single-slide {\r\n            text-align: center;\r\n            .axil-product {\r\n                transform: scale(.8);\r\n                transition: .3s;\r\n                margin: 45px -26px;\r\n                position: relative;\r\n                @media #{$smlg-device} {\r\n                    margin: 45px -19px;\r\n                }\r\n                @media #{$large-mobile} {\r\n                    margin: 20px 0;\r\n                }\r\n\r\n            }\r\n            &.slick-active {\r\n                &.slick-center {\r\n                    z-index: 1;\r\n                    .axil-product {\r\n                        transform: scale(1.1);\r\n                        box-shadow: 0px 10px 80px -87px rgba(0, 0, 0, .5);\r\n                        @media #{$large-mobile} {\r\n                            transform: scale(1);\r\n                        }\r\n                        &:before {\r\n                            content: \"\";\r\n                            width: 100%;\r\n                            height: 50px;\r\n                            background: #000000;\r\n                            position: absolute;\r\n                            left: 0;\r\n                            top: 50%;\r\n                            filter: blur(100px);\r\n                            transform: translateY(-50%);\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        \r\n        .single-slide.slick-active:not(.slick-current) {\r\n            img {\r\n                width: 100%;\r\n            }\r\n        }\r\n        .axil-slick-dots {\r\n            .slick-dots {\r\n                text-align: center;\r\n                bottom: -30px;\r\n                li {\r\n                    height: 10px;\r\n                    width: 10px;\r\n                    margin: 0 8px;\r\n                    button {\r\n                        height: 6px;\r\n                        width: 6px;\r\n                        border-radius: 50%;\r\n                        background-color: transparent;\r\n                        box-shadow: inset 0 0 0 5px rgba(0, 0, 0, .5);\r\n                    }\r\n                    &.slick-active {\r\n                        button {\r\n                            transform: scale(2);\r\n                            box-shadow: inset 0 0 0 1px var(--color-primary);\r\n                           \r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.main-slider-style-4 {\r\n    background-color: #F9F3EF;\r\n    padding-top: 50px;\r\n    overflow: hidden;\r\n    min-height: 600px;\r\n    display: flex;\r\n    align-items: flex-end;\r\n    @media (max-width: 1399px) {\r\n        min-height: 100%;\r\n    }\r\n    @media (max-width: 767px) {\r\n        text-align: center;\r\n    }\r\n    .main-slider-content {\r\n        z-index: 2;\r\n        position: relative;\r\n        padding: 10px 0;\r\n        .title {\r\n            line-height: 1.1;\r\n            margin-bottom: 40px;\r\n            animation: 600ms ease-in-out 0s normal none 1 running customOne;\r\n        }\r\n        .shop-btn {\r\n            animation: 800ms ease-in-out 0s normal none 1 running customOne;\r\n            a {\r\n                @media (max-width: 991px) {\r\n                    padding: 12px 20px;\r\n                }\r\n            }\r\n        }\r\n    }\r\n    .slide-thumb-area {\r\n        position: relative;\r\n        margin-right: -100px;\r\n        margin-left: -100px;\r\n        z-index: 1;\r\n        @media (max-width: 991px) {\r\n            margin-right: -30px;\r\n        }\r\n        @media (max-width:767px) {\r\n            margin-right: 0;\r\n            padding: 20px 0 0;\r\n            margin-left: 0;\r\n        }\r\n\r\n        .main-thumb {\r\n            animation: 1200ms ease-in-out 0s normal none 1 running customTwo;\r\n        }\r\n        .shape-group {\r\n            margin: 0;\r\n            list-style: none;\r\n            padding: 0;\r\n            li {\r\n                position: absolute;\r\n                margin: 0;\r\n                z-index: -1;\r\n                transition: all 0.5s ease-in-out;\r\n                visibility: hidden;\r\n                opacity: 0;\r\n                &.shape-1 {\r\n                    bottom: 0;\r\n                    right: -30px;\r\n                    display: none;\r\n                    svg {\r\n                        path {\r\n                            stroke-dasharray: 1190;\r\n                            stroke-dashoffset: 1180;\r\n                            transition: all 3s ease-in-out;\r\n                        }\r\n                    }\r\n                }\r\n                &.shape-2  {\r\n                    top: -50px;\r\n                    right: 50px;\r\n                    @media (max-width: 767px) {\r\n                        top: -250px;\r\n                    }\r\n                    svg {\r\n                        path {\r\n                            stroke-dasharray: 1190;\r\n                            stroke-dashoffset: 1180;\r\n                            transition: all 3s ease-in-out;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        .banner-product {\r\n            position: absolute;\r\n            left: 80px;\r\n            top: 220px;\r\n            // visibility: hidden;\r\n            // opacity: 0;\r\n            transition: 0.3s;\r\n            @media (max-width:767px) {\r\n                display: none;\r\n            }\r\n            &:hover {\r\n                .product-details {\r\n                    visibility: visible;\r\n                    opacity: 1;\r\n                    margin-bottom: 15px;\r\n                }\r\n            }\r\n            .plus-icon {\r\n                border: 1px solid var(--color-dark);\r\n                font-size: 12px;\r\n                color: var(--color-dark);\r\n                height: 29px;\r\n                width: 29px;\r\n                border-radius: 50%;\r\n                line-height: 29px;\r\n                text-align: center;\r\n                cursor: pointer;\r\n                transition: all 0.3s ease-in-out;\r\n                &:hover {\r\n                    background-color: #F4E7DE;\r\n                }\r\n\r\n            }\r\n            .product-details {\r\n                min-width: 250px;\r\n                width: 100%;\r\n                position: absolute;\r\n                background-color: var(--color-white);\r\n                box-shadow: 0px 54px 94px rgba(172, 128, 117, 0.2);\r\n                border-radius: 8px;\r\n                bottom: 100%;\r\n                left: -46px;\r\n                padding: 15px 20px;\r\n                margin-bottom: 30px;\r\n                visibility: hidden;\r\n                opacity: 0;\r\n                transition: 0.3s;\r\n                .title {\r\n                    margin-bottom: 0;\r\n                    font-size: 18px;\r\n                    a {\r\n                        transition: all 0.3s ease-in-out;\r\n                    }\r\n                }\r\n                .price {\r\n                    color: var(--color-secondary);\r\n                    font-size: 22px;\r\n                    font-weight: 700;\r\n                }\r\n                .product-rating {\r\n                    margin-bottom: 5px;\r\n                    .icon {\r\n                        font-size: 16px;\r\n                        color: #FACC15;\r\n                    }\r\n                    .rating-number {\r\n                        font-size: 14px;\r\n                        font-weight: 500;\r\n                        color: var(--color-body);\r\n                        margin-left: 5px;\r\n                    }\r\n                }\r\n                &:after {\r\n                    content: \"\";\r\n                    width: 0; \r\n                    height: 0; \r\n                    border-left: 12px solid transparent;\r\n                    border-right: 12px solid transparent;\r\n                    border-top: 12px solid var(--color-white);\r\n                    position: absolute;\r\n                    bottom: -12px;\r\n                    left: 52px;\r\n                }\r\n            }\r\n        }\r\n        &:hover {\r\n            .banner-product {\r\n                visibility: visible;\r\n                opacity: 1;\r\n            }\r\n        }\r\n    }\r\n    &.animation-init {\r\n        .slide-thumb-area {\r\n            .shape-group {\r\n                li {\r\n                    visibility: visible;\r\n                    opacity: 1;\r\n                    &.shape-1  {\r\n                        svg {\r\n                            path {\r\n                                stroke-dashoffset: 0;\r\n                                stroke-dasharray: 2000;\r\n                            }\r\n                        }\r\n                    }\r\n                    &.shape-2  {\r\n                        svg {\r\n                            path {\r\n                                stroke-dashoffset: 0;\r\n                                stroke-dasharray: 1300;\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.main-slider-style-5 {\r\n    \r\n    .slider-box-wrap {\r\n        background-color: #f7f7f7;\r\n        border-radius: 6px;\r\n        padding: 90px 100px;\r\n        position: relative;\r\n        z-index: 1;\r\n        @media #{$smlg-device} {\r\n            padding: 80px;\r\n        }\r\n        @media #{$md-layout} {\r\n            padding: 60px;\r\n        }\r\n        @media #{$sm-layout} {\r\n            padding: 50px 50px 60px;\r\n        }\r\n        @media #{$small-mobile} {\r\n            padding: 40px 40px 60px;\r\n        }\r\n        &::after {\r\n            content: \"\";\r\n            height: 260px;\r\n            width: 260px;\r\n            background-color: var(--color-white);\r\n            border-radius: 50%;\r\n            position: absolute;\r\n            top: 80px;\r\n            left: 215px;\r\n            z-index: -1;\r\n            @media #{$md-layout} {\r\n               left: 0;\r\n               top: 0;\r\n               transform: scale(.6);\r\n            }\r\n            @media #{$sm-layout} {\r\n                display: none;\r\n            }\r\n        }\r\n        .slider-activation-two {\r\n            margin: -30px;\r\n        }\r\n        .single-slide {\r\n            display: flex;\r\n            align-items: center;\r\n            padding: 30px;\r\n            @media #{$large-mobile} {\r\n                display: block;\r\n            }\r\n        }\r\n        .axil-slick-dots {\r\n            .slick-dots {\r\n                bottom: -15px;\r\n            }\r\n        }\r\n    }\r\n    .main-slider-content {\r\n        flex: 1;\r\n        .title {\r\n            width: 70%;\r\n            @media only screen and (max-width: 1399px) {\r\n                width: 90%;\r\n            }\r\n            @media #{$smlg-device} {\r\n                width: 100%;\r\n            }\r\n            @media #{$sm-layout} {\r\n                margin-bottom: 35px;\r\n            }\r\n        }\r\n    }\r\n    .main-slider-thumb {\r\n        margin-left: 30px;\r\n        flex: 1;\r\n        text-align: right;\r\n        img {\r\n            display: inline-block;\r\n            min-height: 460px;\r\n            max-height: 460px;\r\n            object-fit: contain;\r\n            @media #{$smlg-device} {\r\n                min-height: 300px;\r\n                max-height: 300px;\r\n            }\r\n            @media #{$large-mobile} {\r\n                min-height: 165px;\r\n                max-height: 165px;\r\n            }\r\n\r\n        }\r\n        @media #{$large-mobile} {\r\n            text-align: center;\r\n            margin-left: 0;\r\n            margin-top: 45px;\r\n        }\r\n    }\r\n}\r\n\r\n.main-slider-style-7 {\r\n    padding: 200px 0;\r\n    background-size: cover;\r\n    background-position: center;\r\n    background-repeat: no-repeat;\r\n    @media only screen and (max-width: 991px) {\r\n        padding: 80px 0;\r\n    }\r\n    @media #{$large-mobile} {\r\n        padding: 60px 0;\r\n    }\r\n    @media #{$small-mobile} {\r\n        background-position: right;\r\n    }\r\n    .main-slider-content {\r\n        z-index: 2;\r\n        position: relative;\r\n        padding: 10px 0;\r\n        .subtitle {\r\n            animation: 800ms ease-in-out 0s normal none 1 running customOne;\r\n        }\r\n        .title {\r\n            width: 80%;\r\n            margin-bottom: 20px;\r\n            animation: 600ms ease-in-out 0s normal none 1 running customOne;\r\n            @media only screen and (max-width: 991px) {\r\n                width: 90%;\r\n            }\r\n        }\r\n        p {\r\n            font-size: 20px;\r\n            animation: 1200ms ease-in-out 0s normal none 1 running customOne;\r\n            @media #{$sm-layout} {\r\n                font-size: 16px;\r\n            }\r\n        }\r\n        .shop-btn {\r\n            animation: 1400ms ease-in-out 0s normal none 1 running customOne;\r\n        }\r\n    }\r\n}\r\n\r\n.main-slider-style-8 {\r\n    .slider-offset-left {\r\n        margin-bottom: 0;\r\n    }\r\n    .slider-box-wrap {\r\n        // background-color: #ffefe9;\r\n        padding: 48px 70px;\r\n        @media only screen and (max-width: 575px) {\r\n            padding: 45px 30px;\r\n            \r\n        }\r\n    }\r\n    .main-slider-thumb {\r\n        &:after {\r\n            left: -50px;\r\n        }\r\n    }\r\n}\r\n\r\n\r\n.slick-slide.slick-current {\r\n    .main-slider-content {\r\n        .subtitle {\r\n            animation: 800ms ease-in-out 0s normal none 1 running customOne;\r\n        }\r\n        .title {\r\n            animation: 1000ms ease-in-out 0s normal none 1 running customOne;\r\n        }\r\n        .shop-btn {\r\n            animation: 1200ms ease-in-out 0s normal none 1 running customOne;\r\n        }\r\n    }\r\n    .main-slider-thumb {\r\n        img {\r\n            animation: 1200ms ease-in-out 0s normal none 1 running customTwo;\r\n        }\r\n    }\r\n}\r\n\r\n\r\n// Animation\r\n@keyframes customOne {\r\n    from {\r\n        opacity: 0;\r\n        -webkit-transform: translate3d(0, 50%, 0);\r\n        transform: translate3d(0, 50%, 0);\r\n    }\r\n    to {\r\n        opacity: 1;\r\n        -webkit-transform: none;\r\n        transform: none;\r\n    }\r\n}\r\n\r\n@keyframes customTwo {\r\n    from {\r\n        opacity: 0;\r\n        transform: translate3d(20%, 0, 0);\r\n    }\r\n    to {\r\n        opacity: 1;\r\n        transform: none;\r\n    }\r\n}\r\n\r\n.animationOne {\r\n    animation: 1200ms customOne;\r\n}\r\n\r\n.animationTwo {\r\n    animation: 1200ms customTwo;\r\n}\r\n\r\n\r\n\r\n", "/*-------------------------\r\nSocial Share\r\n--------------------------*/\r\n\r\n.social-share {\r\n    @extend %liststyle;\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    margin: -10px;\r\n    a {\r\n        margin: 10px;\r\n        color:var(--color-body);\r\n        display: flex;\r\n        justify-content: center;\r\n        align-items: center;\r\n        font-size: 18px;\r\n        transition: 0.3s;\r\n        position: relative;\r\n        z-index: 1;\r\n        &:after {\r\n            content: \"\";\r\n            height: 40px;\r\n            width: 40px;\r\n            background-color: var(--color-secondary);\r\n            transform: scale(0);\r\n            border-radius: 50%;\r\n            position: absolute;\r\n            z-index: -1;\r\n            transition: var(--transition);\r\n        }\r\n        &:hover {\r\n            color: var(--color-white);\r\n            &:after {\r\n                transform: scale(1);\r\n            }\r\n        }\r\n        \r\n    }\r\n}\r\n\r\n", "/*-------------------------\r\nTeam Style\r\n--------------------------*/\r\n.axil-team-area {\r\n\tpadding: 50px 0 55px;\r\n}\r\n.team-left-fullwidth {\r\n\tmargin-left: calc((100% - 1320px) / 2);\r\n    overflow: hidden;\r\n\t@media only screen and (max-width: 1349px) {\r\n\t\tmargin-left: auto;\r\n\t\t\r\n\t}\r\n\t.team-slide-activation {\r\n\t\t.slick-list {\r\n\t\t\toverflow: visible;\r\n\t\t\t@media only screen and (max-width: 1349px) {\r\n\t\t\t\toverflow: hidden;\r\n\t\t\t\t\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.axil-team-member {\r\n\t@media #{$large-mobile} {\r\n\t\ttext-align: center;\r\n\t}\r\n\t.thumbnail {\r\n\t\tmargin-bottom: 12px;\r\n\t\toverflow: hidden;\r\n\t\tdisplay: inline-block;\r\n\t\tborder-radius: 6px;\r\n\t\timg {\r\n\t\t\tborder-radius: 6px;\r\n\t\t\ttransition: var(--transition);\r\n\t\t}\r\n\t}\r\n\t.title {\r\n\t\tcolor: #292930;\r\n\t\tmargin-bottom: 0;\r\n\t}\r\n\t.subtitle {\r\n\t\tfont-size: var(--font-size-b2);\r\n\t}\r\n\t&:hover {\r\n\t\t.thumbnail {\r\n\t\t\timg {\r\n\t\t\t\ttransform: scale(1.1);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}", "/*-------------------------\r\nTestimonial Style\r\n--------------------------*/\r\n.testimonial-style-one-wrapper {\r\n    .slide-arrow {\r\n        background-color: var(--color-white);\r\n    }\r\n    .slick-slide {\r\n        // transition: all .3s;\r\n    }\r\n    .slick-current.slick-active + .slick-active {\r\n        margin-top: 30px;\r\n    }\r\n}\r\n.testimonial-style-one {\r\n    .review-speech {\r\n        background-color: var(--color-white);\r\n        border-radius: 10px;\r\n        padding: 40px;\r\n        margin-bottom: 40px;\r\n        position: relative;\r\n        box-shadow: 0 16px 32px 0 rgba(0,0,0,.04);\r\n        &:after {\r\n            content: \"\";\r\n            width: 0;\r\n            height: 0;\r\n            border-top: 25px solid var(--color-white);\r\n            border-right: 50px solid transparent;\r\n            position: absolute;\r\n            bottom: -25px;\r\n            left: 100px;\r\n        }\r\n        p {\r\n            font-size: 16px;\r\n            line-height: 28px;\r\n        }\r\n    }\r\n    .media {\r\n        display: flex;\r\n        align-items: center;\r\n        .thumbnail {\r\n            margin-right: 20px;\r\n            img {\r\n                border-radius: 6px;\r\n            }\r\n        }\r\n        .media-body {\r\n            flex: 1;\r\n            .designation {\r\n                font-size: 14px;\r\n            }\r\n            .title {\r\n                margin-bottom: 0;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.testimonial-style-two-wrapper {\r\n    margin-bottom: 130px !important;\r\n    .thumbnail {\r\n        display: inline-block;\r\n        border-radius: 50%;\r\n        border: 2px solid var(--color-tertiary);\r\n        padding: 8px;\r\n        margin-bottom: 25px;\r\n        transition: var(--transition);\r\n        position: relative;\r\n        z-index: 1;\r\n        margin: 6px;\r\n        &:before {\r\n            content: \"\";\r\n            height: 100%;\r\n            width: 100%;\r\n            background-color: var(--color-white);\r\n            border-radius: 50%;\r\n            position: absolute;\r\n            top: 0;\r\n            left: 0;\r\n            right: 0;\r\n            bottom: 0;\r\n            z-index: -1;\r\n            transform: scale(1);\r\n            transition: .3s;\r\n        }\r\n        img {\r\n            border-radius: 50%;\r\n        }\r\n    }\r\n\r\n    .thumb-content {\r\n        .item-title {\r\n            margin-bottom: 16px;\r\n        }\r\n        p {\r\n            font-size: 24px;\r\n            color: #292930;\r\n            @media #{$sm-layout} {\r\n                font-size: 18px;\r\n            }\r\n        }\r\n    }\r\n\r\n    .slick-single-layout {\r\n        &:hover {\r\n            .thumbnail {\r\n                &:before {\r\n                    transform: scale(1.2);\r\n                }\r\n                background-color: var(--color-tertiary);\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n\r\n.testimonial-container-box {\r\n    background-color: var(--color-lighter);\r\n    border-radius: 8px;\r\n}\r\n.testimonial-video-box {\r\n    position: relative;\r\n    margin-right: 22px;\r\n    height: 100%;\r\n    @media (max-width: 991px) {\r\n        margin-right: 0;\r\n    }\r\n    .thumbnail {\r\n        height: 100%;\r\n        img {\r\n            border-radius: 8px 0 0 8px;\r\n            height: 100%;\r\n            object-fit: cover;\r\n            @media (max-width: 991px) {\r\n               border-radius: 0;\r\n            }\r\n        }\r\n    }\r\n    .play-btn {\r\n        position: absolute;\r\n        top: 50%;\r\n        transform: translateY(-50%);\r\n        left: 0;\r\n        right: 0;\r\n        text-align: center;\r\n        a {\r\n            height: 110px;\r\n            width: 110px;\r\n            line-height: 110px;\r\n            border-radius: 50%;\r\n            background-color: var(--color-secondary);\r\n            display: inline-block;\r\n            font-size: 28px;\r\n            color: var(--color-white);\r\n            transition: 0.3s;\r\n            &:hover {\r\n                background-color: var(--color-primary);\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.testimonial-style-three-wrapper {\r\n    padding-top: 100px;\r\n    padding-left: 50px;\r\n    padding-right: 50px;    \r\n    position: relative;\r\n    height: 100%;\r\n    @media (max-width: 1199px) {\r\n        padding-left: 0;\r\n    }\r\n    @media (max-width: 991px) {\r\n        padding-left: 50px;\r\n        padding-top: 80px;\r\n    }\r\n    @media (max-width: 767px) {\r\n        padding-left: 30px;\r\n        padding-right: 30px;\r\n    }\r\n    .heading-title {\r\n        position: relative;\r\n        margin-bottom: 45px;\r\n        z-index: 1;\r\n        &:before {\r\n            content: url(\"../../assets/images/testimonial/quote-left.png\");\r\n            height: 100%;\r\n            width: 100%;\r\n            position: absolute;\r\n            top: -58px;\r\n            left: -68px;\r\n            z-index: -1;\r\n            @media (max-width: 1199px) {\r\n                left: 0;\r\n            }\r\n        }\r\n        .title {\r\n            font-size: 48px;\r\n            font-weight: 700;\r\n            @media (max-width: 767px) {\r\n                font-size: 34px;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.testimonial-style-three {\r\n    p {\r\n        font-size: 18px;\r\n        font-weight: 500;\r\n        margin-bottom: 45px;\r\n    }\r\n    .author-name {\r\n        margin-bottom: 0;\r\n    }\r\n    .author-desg {\r\n        font-size: 14px;\r\n    }\r\n}\r\n\r\n.testimonial-custom-nav {\r\n    padding-top: 94px;\r\n    padding-bottom: 40px;\r\n    @media (max-width: 991px) {\r\n        padding-top: 50px;\r\n    }\r\n    .slide-custom-nav {\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: flex-end;\r\n        margin: 0 -12px;\r\n        button {\r\n            width: auto;\r\n            line-height: 1;\r\n            font-size: 16px;\r\n            font-weight: 500;\r\n            display: flex;\r\n            align-items: center;\r\n            color: var(--color-body);\r\n            background-color: transparent;\r\n            padding: 0 12px;\r\n            i {\r\n                margin-left: 8px;\r\n            }\r\n            &.prev-custom-nav {\r\n                border-right: 1px solid #D6D6D6;\r\n                i {\r\n                    margin-left: 0;\r\n                    margin-right: 8px;\r\n                }\r\n            }\r\n            &:hover {\r\n                color: var(--color-heading);\r\n            }\r\n        }\r\n    }\r\n\r\n    .slick-slide-count {\r\n        font-weight: 500;\r\n    }\r\n}", "/*-------------------------\r\nVideo Style\r\n--------------------------*/\r\n\r\n.video-banner {\r\n    position: relative;\r\n    &:after {\r\n        content: \"\";\r\n        height: 100%;\r\n        width: 100%;\r\n        background: radial-gradient(circle, rgba(0,0,0,0.4) 0%, rgba(0,0,0,0) 100%);\r\n        border-radius: 6px;\r\n        position: absolute;\r\n        top: 0;\r\n        bottom: 00;\r\n        left: 0;\r\n        right: 0;\r\n    }\r\n    img {\r\n        width: 100%;\r\n        border-radius: 6px;\r\n    }\r\n    .popup-video-icon {\r\n        text-align: center;\r\n        position: absolute;\r\n        top: 50%;\r\n        left: 0;\r\n        right: 0;\r\n        transform: translateY(-50%);\r\n        z-index: 1;\r\n        .video-icon {\r\n            height: 80px;\r\n            width: 80px;\r\n            line-height: 80px;\r\n            background-color: var(--color-white);\r\n            border-radius: 50%;\r\n            display: inline-block;\r\n            font-size: 24px;\r\n            color: var(--color-primary);\r\n            position: relative;\r\n            z-index: 1;\r\n            i {\r\n                position: relative;\r\n                left: 2px;\r\n                transition: var(--transition);\r\n            }\r\n            &:after {\r\n                content: \"\";\r\n                height: 100%;\r\n                width: 100%;\r\n                background-color: var(--color-white);\r\n                border-radius: 50%;\r\n                position: absolute;\r\n                top: 0;\r\n                left: 0;\r\n                right: 0;\r\n                bottom: 0;\r\n                z-index: -1;\r\n                transition: var(--transition);\r\n            }\r\n            &:hover {\r\n                i {\r\n                    transform: scale(.9);\r\n                }\r\n                &:after {\r\n                    transform: scale(1.1);\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n", "/*-----------------------\r\n    Splash Page Styles  \r\n-------------------------*/\r\n.pv-main-wrapper {\r\n    .section-title-wrapper {\r\n        padding-right: 0;\r\n    }\r\n}\r\n\r\n// Main Banner\r\n\r\n.pv-banner-wrapper {\r\n    margin-top: -120px;\r\n    background-image: url('../images/preview/banner-bg.png');\r\n    background-repeat: no-repeat;\r\n    background-size: cover;\r\n    background-position: center;\r\n    padding-top: 140px;\r\n    position: relative;\r\n    z-index: 1;\r\n    @media #{$md-layout} {\r\n        padding: 160px 0 60px;\r\n    }\r\n    \r\n}\r\n\r\n.pv-banner-area {\r\n    display: flex;\r\n    justify-content: center;\r\n    position: relative;\r\n    z-index: 2;\r\n    padding-left: calc((100% - 1290px) / 2);\r\n    @media only screen and (max-width: 1399px) {\r\n        padding-left: calc((100% - 1110px) / 2);\r\n        \r\n    }\r\n    @media only screen and (max-width: 1199px){\r\n        padding-left: calc((100% - 930px) / 2);\r\n        padding-bottom: 50px;\r\n    }\r\n    @media only screen and (max-width: 991px){\r\n        padding-left: calc((100% - 690px) / 2);\r\n        padding-right: calc((100% - 690px) / 2);\r\n        text-align: center;\r\n    }\r\n    @media only screen and (max-width: 767px){\r\n        padding-left: calc((100% - 510px) / 2);\r\n        padding-right: calc((100% - 510px) / 2);\r\n    }\r\n    .container-fluid {\r\n        padding: 0;\r\n    }\r\n    .inner {\r\n        @media #{$large-mobile} {\r\n            padding-top: 30px !important;\r\n        }\r\n        @media #{$sm-layout} {\r\n            padding-bottom: 26px;\r\n        }\r\n        .section-title-wrapper {\r\n            @media #{$sm-layout}{\r\n                margin-bottom: 0;\r\n            }\r\n        }\r\n        .title-highlighter {\r\n            margin-bottom: 16px;\r\n            @media only screen and (max-width: 991px){\r\n              justify-content: center;\r\n            }\r\n        }\r\n        .title {\r\n            margin-bottom: 50px;\r\n            font-size: 50px;\r\n            line-height: 1.3;\r\n            @media #{$laptop-device} {\r\n                font-size: 44px;\r\n            }\r\n            @media only screen and (max-width: 1399px) {\r\n                font-size: 36px;\r\n            }\r\n            @media #{$smlg-device} {\r\n                margin-bottom: 30px;\r\n            }\r\n            @media only screen and (max-width: 991px){\r\n                br {\r\n                    display: none;\r\n                }\r\n            }\r\n            @media #{$sm-layout}{\r\n                margin-bottom: 0;\r\n            }\r\n        }\r\n    }\r\n\r\n    .theme-brief {\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: flex-start;\r\n        margin: 0 -30px;\r\n        @media only screen and (max-width: 991px){\r\n            justify-content: center;\r\n        }\r\n        .single-counter {\r\n            padding: 0 30px;\r\n            @media #{$large-mobile} {\r\n                padding: 0 5px;\r\n                \r\n            }\r\n            span {\r\n                &.subtile {\r\n                    margin-bottom: 10px;\r\n                    color: var(--color-heading);\r\n                    font-size: 16px;\r\n                    line-height: 24px;\r\n                    display: block;\r\n                }\r\n            }\r\n\r\n            h2 {\r\n                &.title {\r\n                    font-size: 48px;\r\n                    line-height: 1;\r\n                    color: var(--color-primary);\r\n                    display: inline-block;\r\n                    position: relative;\r\n                    min-width: 100px;\r\n                    &::after {\r\n                        content: \"+\";\r\n                        right: 0;\r\n                    }\r\n                    @media only screen and (max-width: 1399px) {\r\n                        font-size: 36px;\r\n                    }\r\n\r\n                    @media #{$md-layout} {\r\n                        font-size: 40px;\r\n                        line-height: 58px;\r\n                    }\r\n\r\n                    @media #{$sm-layout} {\r\n                        font-size: 30px;\r\n                        line-height: 40px;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    .theme-powerd {\r\n        background-color: var(--color-white);\r\n        padding: 15px 25px;\r\n        border-radius: 10px;\r\n        display: inline-flex;\r\n        align-items: center;\r\n        box-shadow: 0px 4px 10px rgba(37, 47, 63, 0.1);\r\n        position: absolute;\r\n        bottom: -35px;\r\n        @media only screen and (max-width: 991px) {\r\n            position: initial;\r\n        }\r\n        label {\r\n            margin-right: 20px;\r\n            font-weight: 500;\r\n            color: var(--color-heading);\r\n        }\r\n        .icon-list {\r\n            display: inline-block;\r\n            list-style: none;\r\n            padding: 0;\r\n            margin: -10px -5px;\r\n            li {\r\n                text-align: center;\r\n                display: inline-block;\r\n                margin: 10px 5px;\r\n                background-color: var(--color-lighter);\r\n                border-radius: 50%;\r\n                height: 50px;\r\n                width: 50px;\r\n                line-height: 50px;\r\n                img {\r\n                    width: 25px;\r\n                    height: auto;\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    .banner-thumbnail {\r\n        margin-bottom: -120px;\r\n        @media only screen and (max-width: 991px) {\r\n            margin-bottom: 0;\r\n            padding-top: 50px;\r\n        }\r\n    }\r\n}\r\n\r\n// Demo\r\n.pv-demo-area {\r\n    background-color: #F6F7FB;\r\n    padding: 140px 0 80px;\r\n    .section-title-wrapper {\r\n        margin-bottom: 0;\r\n        .title {\r\n            margin-bottom: 0;\r\n        }\r\n    }\r\n    @media #{$md-layout} {\r\n        padding: 80px 0;\r\n    }\r\n    @media #{$sm-layout} {\r\n        padding: 80px 0;\r\n    }\r\n}\r\n\r\n.pv-inner-demo-area {\r\n    .section-title-wrapper {\r\n        margin-bottom: 0;\r\n        .title {\r\n            margin-bottom: 0;\r\n        }\r\n    }\r\n}\r\n\r\n.pv-single-demo {\r\n    margin-top: 80px;\r\n    text-align: center;\r\n    box-shadow: 0 0 1px transparent;\r\n    position: relative;\r\n    @media #{$lg-layout} {\r\n        margin-top: 40px;\r\n    }\r\n    @media #{$laptop-device} {\r\n        margin-top: 40px;\r\n    }\r\n    @media #{$sm-layout} {\r\n        margin-top: 40px;\r\n    }\r\n    @media #{$md-layout} {\r\n        margin-top: 40px;\r\n    }\r\n    .thumb {\r\n        position: relative;\r\n        overflow: hidden;\r\n        box-shadow:  0px 24px 24px -16px rgba(15, 15, 15, 0.2);\r\n        border-radius: 6px;\r\n        img {\r\n            max-width: 100%;\r\n            width: 100%;\r\n            border-radius: 6px;\r\n            transform: scale(1);\r\n            transition: all 0.5s ease-in-out;\r\n        }\r\n        .view-btn {\r\n            position: absolute;\r\n            top: 55%;\r\n            left: 50%;\r\n            transform: translateY(-50%) translateX(-50%);\r\n            visibility: hidden;\r\n            opacity: 0;\r\n        }\r\n        &:after {\r\n            content: \"\";\r\n            height: 100%;\r\n            width: 100%;\r\n            background-color: rgba(0,0,0,0.5);\r\n            border-radius: 6px;\r\n            position: absolute;\r\n            top: 0;\r\n            left: 0;\r\n            right: 0;\r\n            bottom: 0;\r\n            visibility: hidden;\r\n            opacity: 0;\r\n            transition: all 0.3s ease-in-out;\r\n        }\r\n    }\r\n    \r\n    .title {\r\n        font-weight: 500;\r\n        margin-top: 35px;\r\n        margin-bottom: 0;\r\n        @extend %transition;\r\n        a {\r\n            text-decoration: none;\r\n            span {\r\n                background-color: var(--color-secondary);\r\n                color: var(--color-white);\r\n                padding: 2px 12px 4px;\r\n                margin-left: 5px;\r\n                border-radius: 4px;\r\n                font-size: 16px;\r\n                display: inline-block;\r\n            }\r\n        }\r\n        &:hover {\r\n            color: var(--color-primary);\r\n        }\r\n        @media #{$sm-layout} {\r\n            margin-top: 15px;\r\n            font-size: 18px;\r\n        }\r\n    }\r\n  \r\n    &:hover {\r\n        .thumb {\r\n            &::after {\r\n                visibility: visible;\r\n                opacity: 1;\r\n            }\r\n            .view-btn {\r\n                top: 50%;\r\n                opacity: 1;\r\n                visibility: visible;\r\n            }\r\n        }\r\n    }\r\n    &.commin {\r\n        &:hover {\r\n          .thumb {\r\n              &::after {\r\n                  visibility: hidden;\r\n              }\r\n          }\r\n        }\r\n    }\r\n}\r\n\r\n// Features\r\n\r\n.pv-feature-area {\r\n    padding-bottom: 60px;\r\n}\r\n.pv-feature-box {\r\n    border-top: 1px solid #f1f1f1;\r\n    padding-top: 80px;\r\n}\r\n\r\n.pv-feature {\r\n    text-align: center;\r\n    height: 100%;\r\n    padding-bottom: 30px;\r\n    .service-box {\r\n        height: 100%;\r\n        margin-bottom: 0;\r\n        .title {\r\n            font-size: 24px;\r\n            line-height: 1.2;\r\n            margin-bottom: 15px;\r\n        }\r\n\r\n    }\r\n   \r\n}\r\n\r\n\r\n// Support\r\n.pv-support-area {\r\n    margin-bottom: -134px;\r\n}\r\n.pv-support {\r\n    padding-bottom: 30px;\r\n    height: 100%;\r\n    .inner {\r\n        height: 100%;\r\n        border-radius: 10px;\r\n        padding: 50px 40px;\r\n        transition: all 0.3s ease-in-out;\r\n        display: flex;\r\n        box-shadow: 0px 24px 24px -16px rgba(15, 15, 15, .20);\r\n        @media #{$large-mobile} {\r\n            padding: 30px 20px;\r\n        }\r\n        .icon {\r\n            margin-top: 2px;\r\n            text-align: center;\r\n            position: relative;\r\n            height: 48px;\r\n            width: 48px;\r\n            line-height: 48px;\r\n            border-radius: 50%;\r\n            z-index: 2;\r\n            background-color: var(--color-primary);\r\n            margin-bottom: 30px;\r\n            display: block;\r\n            margin-right: 20px;\r\n            i {\r\n                font-size: 18px;\r\n                color: var(--color-secondary);\r\n            }\r\n            img {\r\n                max-height: 40px;\r\n            }\r\n        }\r\n\r\n        .content {\r\n           flex: 1;\r\n            .title {\r\n                margin-bottom: 10px;\r\n                color: var(--color-white);\r\n                display: block;\r\n            }\r\n            .paragraph {\r\n                color: var(--color-white);\r\n                margin-bottom: 30px;\r\n                display: block;\r\n            }\r\n            .axil-btn {\r\n                font-size: var(--font-size-b1);\r\n                line-height: var(--line-height-b1);\r\n                color: var(--color-white);\r\n                font-weight: 700;\r\n                display: inline-block;\r\n                z-index: 1;\r\n                transition: 0.3s;\r\n                i {\r\n                    margin-left: 10px;\r\n                    position: relative;\r\n                    top: 1px;\r\n                    transition: .3s;\r\n                }\r\n                &:hover {\r\n                    i {\r\n                        margin-left: 15px;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        \r\n    }\r\n    &.online-documentation {\r\n        .inner {\r\n            background: #8E2DE2;  \r\n            background: -webkit-linear-gradient(to right, #4A00E0, #8E2DE2);  \r\n            background: linear-gradient(to right, #4A00E0, #8E2DE2); \r\n            background-position: center center;\r\n            background-size: cover;\r\n            background-repeat: no-repeat;\r\n            .icon {\r\n                background-color: var(--color-white);\r\n\r\n            }\r\n        }\r\n    }\r\n    &.datecated-support {\r\n        .inner {\r\n            background: #FC5C7D;\r\n            background: -webkit-linear-gradient(to right, #6A82FB, #FC5C7D); \r\n            background: linear-gradient(to right, #6A82FB, #FC5C7D);\r\n            .icon {\r\n                background-color: var(--color-white);\r\n\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n\r\n// Call To Action\r\n\r\n.pv-call-to-action {\r\n    background-color: #F6F7FB;\r\n}\r\n\r\n.pv-action-box {\r\n    padding: 185px 30px 0;\r\n    text-align: center;\r\n\r\n    .section-title-wrapper {\r\n        margin-bottom: 40px;\r\n    }\r\n    .title-highlighter {\r\n        justify-content: center;\r\n    }\r\n    .pv-action-thumbnail {\r\n        transform: translateY(20px);\r\n        transition: .3s;\r\n        &:hover {\r\n            transform: translateY(0);\r\n        }\r\n    }\r\n}\r\n\r\n\r\n// Footer\r\n\r\n.pv-footer-area {\r\n    background-color: var(--color-white);\r\n    padding: 10px 0;\r\n    position: relative;\r\n    z-index: 2;\r\n    .copyright-default .quick-link li::after {\r\n        display: none;\r\n    }\r\n}\r\n", "/*------------------------------\r\n    Header Campaign Styles  \r\n-------------------------------*/\r\n.header-top-campaign {\r\n    background-image: url('../images/others/campaign-bg2.png');\r\n    background-position: center;\r\n    background-size: cover;\r\n    background-repeat: no-repeat;\r\n    padding: 8px 0;\r\n    position: relative;\r\n    .campaign-content {\r\n        text-align: center;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        position: relative;\r\n        margin: 0 30px;\r\n        @media #{$sm-layout} {\r\n            display: block;\r\n        }\r\n        p {\r\n            color: #eeb2ff;\r\n            font-size: 14px;\r\n            a {\r\n                font-weight: 700;\r\n                color: var(--color-white);\r\n                text-decoration: underline;\r\n                transition: all .3s ease-in-out;\r\n                &:hover {\r\n                    color: var(--color-secondary);\r\n                }\r\n            }\r\n        }\r\n    }\r\n    .campaign-countdown {\r\n        display: flex;\r\n        align-items: center;\r\n        margin: 0 -5px;\r\n        padding-right: 30px;\r\n        @media #{$sm-layout} {\r\n           justify-content: center;\r\n           padding-right: 0;\r\n        }\r\n        .countdown-section {\r\n            margin: 0 5px;\r\n            position: relative;\r\n            &::after {\r\n                content: \"\";\r\n                height: 14px;\r\n                width: 1px;\r\n                background-color: #c653e2;\r\n                position: absolute;\r\n                top: 50%;\r\n                right: -7px;\r\n                transform: translateY(-50%);\r\n            }\r\n            &:last-child {\r\n                &::after {\r\n                    display: none;\r\n                }\r\n            }\r\n            >div {\r\n                display: flex;\r\n                align-items: center;\r\n            }\r\n            .countdown-number, .countdown-unit {\r\n                font-size: 14px;\r\n                font-weight: 700;\r\n                color: var(--color-white);\r\n            }\r\n        }\r\n    }\r\n    .remove-campaign {\r\n        position: absolute;\r\n        top: 11px;\r\n        right: 30px;\r\n        background-color: transparent;\r\n        color: var(--color-white);\r\n        width: auto;\r\n        font-size: 14px;\r\n        &:hover {\r\n            color: var(--color-primary);\r\n        }\r\n        @media only screen and (max-width: 991px) {\r\n            right: 10px;\r\n        }\r\n    }\r\n}\r\n\r\n/*------------------------------\r\n    Header Top Styles  \r\n-------------------------------*/\r\n.axil-header-top {\r\n    padding: 8px 0;\r\n}\r\n\r\n.header-top-dropdown {\r\n    display: flex;\r\n    align-items: center;\r\n    @media #{$large-mobile} {\r\n        justify-content: center;\r\n    }\r\n    .dropdown {\r\n        max-width: 70px;\r\n        margin-right: 20px;\r\n        @media #{$large-mobile} {\r\n            margin-right: 10px;\r\n        }\r\n        &:last-child {\r\n            margin-right: 0;\r\n        }\r\n        .dropdown-toggle {\r\n            font-size: 14px;\r\n            color: var(--color-body);\r\n            background-color: transparent;\r\n            display: flex;\r\n            align-items: center;\r\n            &:after {\r\n                content: \"\\f107\";\r\n                font-family: var(--font-awesome);\r\n                border: none;\r\n                margin-left: 5px;\r\n                margin-top: 1px;\r\n            }\r\n        }\r\n        .dropdown-menu {\r\n            min-width: 11rem;\r\n            border: none;\r\n            box-shadow: var(--shadow-primary);\r\n            display: block !important;\r\n            top: 35px;\r\n            visibility: hidden;\r\n            opacity: 0;\r\n            transition: var(--transition);\r\n            li {\r\n                margin: 5px 10px;\r\n            }\r\n            .dropdown-item {\r\n                font-size: 14px;\r\n                border-radius: 6px;\r\n                &:hover {\r\n                    background-color: var(--color-secondary);\r\n                    color: var(--color-white);\r\n                }\r\n            }\r\n            &.show {\r\n                visibility: visible;\r\n                opacity: 1;\r\n                top: 30px;\r\n            }\r\n        }\r\n    }\r\n    &.dropdown-box-style {\r\n        .dropdown {\r\n            max-width: 100%;\r\n            .dropdown-toggle {\r\n                color: var(--color-heading);\r\n                font-weight: 700;\r\n                border: 1px solid #f0f0f0;\r\n                border-radius: 6px;\r\n                padding: 10px 20px;\r\n                justify-content: center;\r\n               &:after {\r\n                   font-weight: 400;\r\n               }\r\n               &:hover {\r\n                   box-shadow: 0 16px 32px 0 rgba(0, 0, 0, .06);\r\n               }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.header-top-link {\r\n    .quick-link {\r\n        @extend %liststyle;\r\n        display: flex;\r\n        flex-wrap: wrap;\r\n        margin: 0 -18px;\r\n        justify-content: flex-end;\r\n        @media #{$large-mobile} {\r\n            justify-content: center;\r\n            margin: 5px 0;\r\n        }\r\n        li {\r\n            margin: 0;\r\n            padding: 0 18px;\r\n            position: relative;\r\n            a {\r\n                font-size: var(--font-size-b2);\r\n                color: var(--color-body);\r\n                line-height: var(--line-height-b2);\r\n                display: inline-block;\r\n                &:hover {\r\n                    color: var(--color-primary);\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.header-style-1 {\r\n    .header-top-campaign {\r\n        background-image: url('../images/others/campaign-bg.png');\r\n        .campaign-content {\r\n            p {\r\n                color: var(--color-white);\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.header-style-2 {\r\n    .axil-header-top {\r\n        padding: 25px 0;\r\n        .axil-search {\r\n            flex: 1;\r\n            position: relative;\r\n            margin-left: 70px;\r\n            margin-right: 20px;\r\n            @media only screen and (max-width: 991px) {\r\n                margin-left: 0;\r\n            }\r\n            @media #{$large-mobile} {\r\n               display: none;\r\n            }\r\n            .icon {\r\n                width: auto;\r\n                position: absolute;\r\n                top: 10px;\r\n                left: 15px;\r\n                background-color: transparent;\r\n                font-size: 14px;\r\n                color: var(--color-heading);\r\n                z-index: 1;\r\n                &:hover {\r\n                    color: var(--color-primary);\r\n                }\r\n\r\n            }\r\n            input {\r\n                width: 100%;\r\n                background-color: transparent;\r\n                border: 1px solid #d4d4d4;\r\n                border-radius: 6px;\r\n                font-size: 14px;\r\n                padding-left: 50px;\r\n                color: var(--color-heading);\r\n                font-weight: 500;\r\n                &:focus {\r\n                    border-color: var(--color-primary);\r\n                }\r\n            }\r\n        }\r\n    }\r\n    .header-top-dropdown {\r\n        @media #{$large-mobile} {\r\n            justify-content: flex-end;\r\n        }\r\n    }\r\n}\r\n\r\n.header-style-4 {\r\n    .axil-header-top {\r\n        border-bottom: 1px solid #f1f1f1;\r\n        padding: 25px 0;\r\n        @media only screen and (max-width: 991px) {\r\n            padding: 15px 0;   \r\n            border-bottom: none;\r\n        }\r\n        .header-brand {\r\n            text-align: center;\r\n            @media #{$sm-layout} {\r\n                text-align: left;\r\n            }\r\n        }\r\n        .header-action {\r\n            >ul {\r\n                justify-content: flex-end;\r\n\r\n            }\r\n        }\r\n        .header-top-dropdown {\r\n            @media #{$sm-layout} {\r\n                justify-content: center;\r\n                border-bottom: 1px solid #f1f1f1;\r\n                padding-bottom: 15px;\r\n                margin-bottom: 15px;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.header-style-5 {\r\n    .axil-header-top {\r\n        background-color: var(--color-heading);\r\n        padding: 6px 0;\r\n    }\r\n    .header-top-dropdown {\r\n        .dropdown {\r\n            .dropdown-toggle {\r\n                color: #c2c2cc;\r\n                &:hover {\r\n                    color: var(--color-white);\r\n                }\r\n            }\r\n        }\r\n    }\r\n    .header-top-link {\r\n        .quick-link {\r\n            li {\r\n                a {\r\n                    color: #c2c2cc;\r\n                    &:hover {\r\n                        color: var(--color-primary);\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n    .header-top-campaign {\r\n        background-image: url('../images/others/campaign-bg3.png');\r\n        padding: 20px 0 18px;\r\n        .campaign-content {\r\n            p {\r\n                font-size: 16px;\r\n                font-weight: 700;\r\n                color: var(--color-white);\r\n                a {\r\n                    i {\r\n                        margin-left: 15px;\r\n                    }\r\n                }\r\n                @media #{$small-mobile} {\r\n                    font-size: 12px;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.header-style-7 {\r\n    .axil-header-top {\r\n        background-color: var(--color-heading);\r\n        padding: 7px 45px;\r\n        @media (max-width: 991px) {\r\n            padding: 7px 0;\r\n        }\r\n        @media (max-width: 767px) {\r\n           text-align: center;\r\n        }\r\n    }\r\n    .header-top-text {\r\n        p {\r\n            font-size: 14px;\r\n            color: var(--color-white);\r\n            i {\r\n                color: #FACC15;\r\n                margin-right: 5px;\r\n            }\r\n        }\r\n    }\r\n    .header-top-link {\r\n        .quick-link {\r\n            @media (max-width: 767px) {\r\n                justify-content: center;\r\n            }\r\n            li {\r\n                a {\r\n                    color: var(--color-white);   \r\n                    &:hover {\r\n                        color: var(--color-secondary);\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}", "/*-----------------------\r\n    Header Styles  \r\n-------------------------*/\r\n.header-brand {\r\n    a {\r\n        display: block;\r\n        img {\r\n            @media only screen and (max-width: 991px) {\r\n               max-height: 35px;\r\n            }\r\n            @media only screen and (max-width: 320px) {\r\n               max-height: 30px;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.logo-light {\r\n    display: none !important;\r\n}\r\n\r\n.axil-mainmenu {\r\n    .header-navbar {\r\n        display: flex;\r\n        align-items: center;\r\n        width: 100%;\r\n        .header-main-nav {\r\n            flex: 1;\r\n            margin: 0 50px;\r\n            @media only screen and (max-width: 991px) {\r\n                margin: 0;\r\n            }\r\n        }\r\n    }\r\n    &.axil-sticky {\r\n        position: fixed;\r\n        top: 0;\r\n        right: 0;\r\n        left: 0;\r\n        z-index: 5;\r\n        background-color: var(--color-white);\r\n        box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.10);\r\n        transition: var(--transition);\r\n        .mainmenu {\r\n            >li {\r\n                >a {\r\n                    height: 80px;\r\n                    line-height: 80px;\r\n                    &:before {\r\n                        bottom: 20px;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.header-style-1 {\r\n    background-color: #f9f3f0;\r\n    padding-bottom: 30px;\r\n    .header-navbar {\r\n        background-color: var(--color-white);\r\n        padding: 0 40px;\r\n        border-radius: 10px;\r\n        @media #{$smlg-device} {\r\n            padding: 0 25px;\r\n        }\r\n        @media #{$md-layout} {\r\n            padding: 15px 25px;\r\n        }\r\n        @media #{$sm-layout} {\r\n            padding: 15px 15px;\r\n        }\r\n    }\r\n    .mainmenu > li {\r\n        @media #{$smlg-device} {\r\n            margin: 0 13px;\r\n        }\r\n    }\r\n    .axil-mainmenu {\r\n        &.axil-sticky {\r\n            background-color: transparent;\r\n            box-shadow: none;\r\n            padding-top: 10px;\r\n            .header-navbar {\r\n                box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.10);\r\n            }\r\n        }\r\n    }\r\n    .header-brand {\r\n        @media only screen and (max-width: 575px) {\r\n           margin-right: 10px;\r\n        }\r\n    }\r\n}\r\n\r\n.header-style-3 {\r\n    background-color: #f9f3f0;\r\n    .axil-mainmenu {\r\n        padding: 20px 0;\r\n        position: relative;\r\n        z-index: 2;\r\n        &.axil-sticky {\r\n            position: fixed;\r\n            background-color: transparent;\r\n            box-shadow: none;\r\n            z-index: 4;\r\n            .header-navbar {\r\n                box-shadow: 0 2px 10px 0 rgba(0, 0, 0, .10);\r\n            }\r\n        }\r\n    }\r\n    .header-navbar {\r\n        background-color: var(--color-white);\r\n        padding: 0 20px;\r\n        border-radius: 10px;\r\n        @media #{$md-layout} {\r\n            padding: 10px 20px;\r\n        }\r\n        @media #{$sm-layout} {\r\n            padding: 10px 20px;\r\n        }\r\n    }\r\n    .header-action {\r\n        display: flex;\r\n        align-items: center;\r\n        .header-btn {\r\n            .axil-btn {\r\n                padding: 12px 30px;\r\n                font-size: 15px;\r\n                font-weight: 500;\r\n                @media #{$small-mobile} {\r\n                    padding: 12px 20px;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.header-style-4 {\r\n    .mainmenu {\r\n        >li {\r\n            >a {\r\n                line-height: 60px;\r\n                height: 60px;\r\n                &:before {\r\n                    bottom: 18px;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.header-style-5 {\r\n    .axil-mainmenu {\r\n        @media only screen and (max-width: 991px) {\r\n            padding: 15px 0;\r\n        }\r\n    }\r\n    \r\n    @media only screen and (max-width: 1399px) {\r\n        .header-navbar {\r\n            .header-main-nav {\r\n                margin: 0 20px;\r\n                @media only screen and (max-width: 991px) {\r\n                    margin: 0;\r\n                }\r\n            }\r\n        }\r\n        .mainmenu {\r\n            margin: 0 -18px;\r\n            >li {\r\n                margin: 0 18px;\r\n            }\r\n        }\r\n    }\r\n\r\n    .header-action {\r\n        .axil-search {\r\n            input {\r\n                border: 1px solid #f0f0f0;\r\n                height: 40px;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.header-style-7 {\r\n    position: relative;\r\n    z-index: 5;\r\n    .axil-mainmenu {\r\n        padding: 20px 45px;\r\n        box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.03);\r\n        filter: drop-shadow(0px 4px 10px rgba(0, 0, 0, 0.03));\r\n        @media (max-width: 991px) {\r\n            padding: 20px 0;\r\n            filter: none;\r\n        }\r\n        .header-navbar {\r\n            .header-main-nav {\r\n                margin-left: 90px;\r\n                @media (max-width: 1199px) {\r\n                    margin: 0 30px;\r\n                }\r\n                @media (max-width: 991px) {\r\n                    margin: 0;\r\n                }\r\n                \r\n            }\r\n        }\r\n    }\r\n    .header-action {\r\n        >ul {\r\n            margin: 0 -14px;\r\n            @media (max-width: 767px) {\r\n                margin: 0 -10px;\r\n            }\r\n            >li {\r\n                margin: 0 14px;\r\n                @media (max-width: 767px) {\r\n                    margin: 0 10px;\r\n                }\r\n                >a {\r\n                    font-size: 19px;\r\n                }\r\n            }\r\n        }\r\n        .axil-search {\r\n            margin-right: 110px;\r\n            @media (max-width: 1599px) {\r\n                margin-right: 16px;\r\n            }\r\n            @media (max-width: 767px) {\r\n                margin-right: 10px;\r\n            }\r\n            input {\r\n                border: 1px solid #D6D6D6;\r\n                height: 40px;\r\n                border-radius: 8px;\r\n                padding: 0 15px;\r\n                padding-right: 35px;\r\n                width: 448px;\r\n                max-width: 448px;\r\n            }\r\n            .icon {\r\n                left: auto;\r\n                right: 16px;\r\n                font-size: 18px;\r\n                color: var(--color-body);\r\n                top: 50%;\r\n                &:hover {\r\n                    color: var(--color-heading);\r\n                }\r\n            }\r\n        }\r\n        .shopping-cart {\r\n            .cart-dropdown-btn {\r\n                .cart-count {\r\n                    height: 14px;\r\n                    width: 14px;\r\n                    line-height: 14px;\r\n                    font-size: 10px;\r\n                    border: none;\r\n                    display: block;\r\n                    top: -16px;\r\n                    right: -8px;\r\n                }\r\n            }\r\n        }\r\n        .my-account {\r\n            .my-account-dropdown {\r\n                margin-top: 30px;\r\n            }\r\n        }\r\n    }\r\n\r\n    .mainmenu {\r\n        justify-content: flex-start;\r\n        margin: 0 -20px;\r\n        @media (max-width: 1199px) {\r\n            margin: 0 -15px;\r\n        }\r\n        >li {\r\n            margin: 0 20px;\r\n            @media (max-width: 1199px) {\r\n                margin: 0 15px;\r\n            }\r\n            >a {\r\n                font-size: 16px;\r\n                font-weight: 500;\r\n                line-height: 1 !important;\r\n                height: auto !important;\r\n                i {\r\n                    margin-right: 10px;\r\n                    color: var(--color-body);\r\n                    transition: .3s;\r\n                }\r\n                &:before {\r\n                    display: none;\r\n                }\r\n                &:hover {\r\n                    i {\r\n                        color: var(--color-heading);\r\n                    }\r\n                }\r\n            }\r\n            &.dropdown {\r\n                .dropdown-toggle {\r\n                    border: 1px dashed var(--color-primary);\r\n                    display: flex;\r\n                    align-items: center;\r\n                    font-size: 16px;\r\n                    padding: 9px 14px;\r\n                    border-radius: 8px;\r\n                    i {\r\n                        color: var(--color-primary);\r\n\r\n                    }\r\n                    &:after {\r\n                        content: \"\\f063\";\r\n                        font-family: var(--font-awesome);\r\n                        border: none;\r\n                        vertical-align: middle;\r\n                        font-size: 12px;\r\n                        margin-left: 8px;\r\n                        transition: all 0.3s ease-in-out;\r\n                    }\r\n                    &:hover {\r\n                        color: var(--color-primary);\r\n                        i {\r\n                            color: var(--color-primary);\r\n                        }\r\n                        &:after {\r\n                            color: var(--color-primary);\r\n                        }\r\n                    }\r\n                    @media (max-width: 991px) {\r\n                        border: none;\r\n                        padding: 5px 0;\r\n                        i {\r\n                            color: var(--color-body);\r\n                        }\r\n                    }\r\n                }\r\n                .dropdown-menu {\r\n                    list-style: none;\r\n                    @media (min-width: 992px) {\r\n                        transform: translate(0px, 20px) !important;\r\n                        inset: initial !important;\r\n                        background: #ffffff;\r\n                        min-width: 250px;\r\n                        padding: 15px 10px;\r\n                        border-radius: 4px;\r\n                        display: block !important;\r\n                        visibility: hidden;\r\n                        opacity: 0;\r\n                        box-shadow: var(--shadow-primary);\r\n                        transition: all 0.3s ease-in-out;\r\n                    }\r\n                    @media (max-width: 991px) {\r\n                        position: initial !important;\r\n                        border: none;\r\n                        padding: 0 0 0 10px;\r\n                    }\r\n                    li {\r\n                        margin: 0;\r\n                        a {\r\n                            position: relative;\r\n                            font-size: 15px;\r\n                            text-transform: capitalize;\r\n                            color: var(--color-heading);\r\n                            font-weight: 500;\r\n                            padding: 10px 15px;\r\n                            display: block;\r\n                            transition: all 0.3s ease-in-out;\r\n                            z-index: 1;\r\n                            overflow: hidden;\r\n                            border-bottom: 1px solid #f3f3f3;\r\n                            &:hover {\r\n                                color: var(--color-secondary);\r\n                                background: transparent;\r\n                            }\r\n                            &.active {\r\n                                color: var(--color-secondary);\r\n                                &:hover {\r\n                                    color: var(--color-secondary);\r\n                                }\r\n                            }\r\n                            @media (max-width: 991px) {\r\n                                padding: 10px 0;\r\n                            }\r\n                        }\r\n                        &:last-child {\r\n                            a {\r\n                                border-bottom: none;\r\n                            }\r\n                        }\r\n                    }\r\n                    &.show {\r\n                        visibility: visible;\r\n                        opacity: 1;\r\n                        transform: translate(0px, 10px) !important;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.d-none-laptop {\r\n    @media (max-width: 1599px) {\r\n        display: none;\r\n    }\r\n}\r\n\r\n.d-none-desktop {\r\n    @media (min-width: 1600px) {\r\n        display: none;\r\n    }\r\n}\r\n\r\n/*-----------------------\r\n    Header Action  \r\n-------------------------*/\r\n.header-action {\r\n    @media #{$small-mobile} {\r\n        margin-top: 4px;\r\n    }\r\n    >ul {\r\n        display: flex;\r\n        align-items: center;\r\n        margin: 0 -10px;\r\n        padding: 0;\r\n        @media #{$small-mobile} {\r\n            margin: 0 -6px;\r\n        }\r\n        >li {\r\n            @extend %liststyle;\r\n            margin: 0 10px;\r\n            @media #{$small-mobile} {\r\n                margin: 0 5px;\r\n            }\r\n            >a {\r\n                font-size: 24px;\r\n                font-weight: 500;\r\n                color: var(--color-heading);\r\n                position: relative;\r\n                display: flex;\r\n                align-items: center;\r\n                justify-content: center;\r\n                z-index: 1;\r\n                @media #{$small-mobile} {\r\n                    font-size: 22px;\r\n                }\r\n                >i {\r\n                    display: inline-block;\r\n                    line-height: 0;\r\n                }\r\n                &::after {\r\n                    content: \"\";\r\n                    height: 45px;\r\n                    width: 45px;\r\n                    background-color: var(--color-secondary);\r\n                    transform: scale(0);\r\n                    border-radius: 50%;\r\n                    position: absolute;\r\n                    z-index: -1;\r\n                    transition: var(--transition);\r\n                    @media #{$large-mobile} {\r\n                        height: 35px;\r\n                        width: 35px;\r\n                    }\r\n                }\r\n                &:focus {\r\n                    color: var(--color-heading);\r\n                }\r\n                &:hover {\r\n                    color: var(--color-white);\r\n                    &::after {\r\n                        transform: scale(1);\r\n                    }\r\n                }\r\n                &.open {\r\n                    color: var(--color-white);\r\n                    &::after {\r\n                        transform: scale(1);\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n    .shopping-cart {\r\n        .cart-dropdown-btn {\r\n            .cart-count {\r\n                text-align: center;\r\n                background-color: var(--color-primary);\r\n                border: 2px solid var(--color-white);\r\n                font-size: 12px;\r\n                font-weight: 500;\r\n                color: var(--color-white);\r\n                border-radius: 50%;\r\n                height: 22px;\r\n                width: 22px;\r\n                line-height: 19px;\r\n                position: absolute;\r\n                top: -12px;\r\n                right: -12px;\r\n            }\r\n        }\r\n    }\r\n    .my-account {\r\n        position: relative;\r\n        .my-account-dropdown {\r\n            position: absolute;\r\n            top: 100%;\r\n            right: 0;\r\n            background: #ffffff;\r\n            z-index: -1;\r\n            opacity: 0;\r\n            visibility: hidden;\r\n            min-width: 250px;\r\n            padding: 20px;\r\n            border-radius: 4px;\r\n            box-shadow: var(--shadow-primary);\r\n            @extend %transition;\r\n            list-style: none;\r\n            transform: translateY(30px);\r\n            margin-top: 20px;\r\n            @media #{$small-mobile} {\r\n                right: -30px;\r\n            }\r\n            ul {\r\n                list-style: none;\r\n                padding-left: 0;\r\n            }\r\n            .title {\r\n                font-size: 12px;\r\n                font-weight: 500;\r\n            }\r\n            li {\r\n                margin: 0;\r\n                a {\r\n                    font-size: 16px;\r\n                    border-bottom: 1px solid #eeeeee;\r\n                    padding: 12px 0;\r\n                    display: block;\r\n                }\r\n                &:hover {\r\n                    >a {\r\n                        color: var(--color-primary);\r\n                    }\r\n                }\r\n            }\r\n            .login-btn {\r\n                text-align: center;\r\n                text-align: center;\r\n                margin-top: 30px;\r\n                margin-bottom: 25px;\r\n            }\r\n            .axil-btn {\r\n               padding: 10px 35px;\r\n               width: 100%;\r\n            }\r\n            .reg-footer {\r\n                font-size: 12px;\r\n                .btn-link {\r\n                    margin-left: 7px;\r\n                    font-weight: 700;\r\n                    text-transform: uppercase;\r\n                    color: var(--color-dark);\r\n                    position: relative;\r\n                    line-height: 1;\r\n                    border-bottom: 2px solid #999FAE;\r\n                    text-decoration: none;\r\n                    &:hover {\r\n                        color: var(--color-primary);\r\n                        border-color: var(--color-primary);\r\n                    }\r\n                }\r\n            }\r\n            \r\n          \r\n            &.open {\r\n                opacity: 1;\r\n                visibility: visible;\r\n                z-index: 9;\r\n                transform: translateY(0);\r\n            }\r\n        }\r\n    }\r\n    .axil-search {\r\n        position: relative;\r\n        .icon {\r\n            position: absolute;\r\n            left: 15px;\r\n            width: auto;\r\n            padding: 0;\r\n            top: 52%;\r\n            transform: translateY(-50%);\r\n            line-height: 1;\r\n            background-color: transparent;\r\n            font-size: 22px;\r\n            color: var(--color-heading);\r\n            i {\r\n                display: inline-block;\r\n                line-height: 0;\r\n            }\r\n        }\r\n        input {\r\n            background: var(--color-white);\r\n            color: var(--color-heading);\r\n            border-radius: 6px;\r\n            padding-left: 40px;\r\n            padding-right: 10px;\r\n            max-width: 250px;\r\n            height: 50px;\r\n            opacity: 1;\r\n            font-size: 14px;\r\n        }\r\n    }\r\n}\r\n\r\n\r\n/*-----------------------\r\n    Header Search\r\n-------------------------*/\r\n.header-search-modal {\r\n    position: fixed;\r\n    top: 50%;\r\n    left: 50%;\r\n    transform: translateY(-50%) translateX(-50%) scale(.8);\r\n    opacity: 0;\r\n    visibility: hidden;\r\n    z-index: 10;\r\n    @media only screen and (max-width: 991px) {\r\n        width: 92%; \r\n        right: -100%;\r\n    }\r\n    .card-close {\r\n        height: 40px;\r\n        width: 40px;\r\n        font-size: 16px;\r\n        color: var(--color_black);\r\n        border-radius: 50%;\r\n        @extend %transition;\r\n        position: absolute;\r\n        right: -60px;\r\n        top: 0;\r\n        z-index: 1;\r\n        &:hover {\r\n            background-color: var(--color-primary);\r\n            color: var(--color-white);\r\n        }\r\n        @media only screen and (max-width: 991px) {\r\n            height: 35px;\r\n            width: 35px;\r\n            font-size: 15px;\r\n            right: 30px;\r\n            top: 12px;\r\n        }\r\n        @media #{$small-mobile} {\r\n            right: 15px;\r\n            top: 14px;\r\n        }\r\n    }\r\n    .header-search-wrap {\r\n        background-color: var(--color-white);\r\n        border-radius: 10px;\r\n        padding: 40px 30px;\r\n        width: 800px;\r\n        height: 575px;\r\n        max-height: 90vh;\r\n        overflow: auto;\r\n        @media only screen and (max-width: 991px) {\r\n            width: 100%;   \r\n            padding: 70px 30px 30px;\r\n        }\r\n        @media only screen and (max-width: 479px) {  \r\n            padding: 70px 15px 30px;\r\n        }\r\n    }\r\n    .card-header {\r\n        background-color: transparent;\r\n        padding: 0;\r\n        border-bottom: none;\r\n        form {\r\n            padding-bottom: 30px;\r\n        }\r\n        .form-control {\r\n            border: 1px solid #f1f1f1;\r\n            border-radius: 6px !important;\r\n            font-size: 15px;\r\n            height: 55px;\r\n            padding: 5px 20px 5px 50px;\r\n            color: var(--color-);\r\n            &:focus {\r\n                box-shadow: 0 16px 32px 0 rgba(0, 0, 0, 0.04);\r\n            }\r\n            &::placeholder {\r\n                color: var(--color-heading);\r\n                opacity: 1;\r\n            }\r\n            &:-ms-input-placeholder {\r\n                color: var(--color-heading);\r\n            }\r\n            &::-ms-input-placeholder {\r\n                color: var(--color-heading);\r\n            }\r\n        }\r\n        .axil-btn {\r\n            width: auto;\r\n            padding: 5px 20px;\r\n            font-size: 15px;\r\n            background-color: transparent;\r\n            margin: 5px;\r\n            border-radius: 6px !important;\r\n            z-index: 1;\r\n            position: absolute;\r\n            top: 0;\r\n            left: 0;\r\n            bottom: 0;\r\n            z-index: 10;\r\n            pointer-events: none;\r\n            \r\n            &:before {\r\n                display: none;\r\n            }\r\n            i {\r\n                margin-right: 0;\r\n                color: var(--color-lightest);\r\n            }\r\n            &:hover {\r\n                i {\r\n                    color: var(--color-heading);\r\n                }\r\n            }\r\n        }\r\n    }\r\n    .card-body {\r\n        padding: 0;\r\n    }\r\n    .search-result-header {\r\n        border-bottom: 1px solid #F6F7FB;\r\n        padding-bottom: 15px;\r\n        margin-bottom: 25px;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: space-between;\r\n        .title {\r\n            margin-bottom: 0;\r\n            font-size: 14px;\r\n            font-weight: 400;\r\n            color: var(--color-heading);\r\n        }\r\n        .view-all {\r\n            font-size: 14px;\r\n            transition: .3s;\r\n            color: var(--color-heading);\r\n            position: relative;\r\n            &:after {\r\n                content: \"\";\r\n                height: 2px;\r\n                width: 0;\r\n                background-color: var(--color-heading);\r\n                position: absolute;\r\n                bottom: -2px;\r\n                right: 0;\r\n                opacity: 0;\r\n                transition: 0.5s;\r\n            }\r\n            &:hover {\r\n                color: var(--color-heading);\r\n                &:after {\r\n                    width: 100%;\r\n                    opacity: 1;\r\n                    left: 0;\r\n                }\r\n            }\r\n        }\r\n    }\r\n    .psearch-results {\r\n        .axil-product-list {\r\n            padding: 20px;\r\n            margin-bottom: 20px;\r\n            @media #{$large-mobile} {\r\n                display: flex;\r\n                text-align: left;\r\n                align-items: flex-start;\r\n                .thumbnail {\r\n                    margin-bottom: 0;\r\n                    margin-right: 15px;\r\n                }\r\n                .product-rating {\r\n                    justify-content: flex-start;\r\n                }\r\n            }\r\n            @media #{$small-mobile} {\r\n                padding: 15px;\r\n                .thumbnail {\r\n                    width: 60px;\r\n                }\r\n                .product-content {\r\n                    .product-title {\r\n                        margin-bottom: 4px;\r\n                    }\r\n                    .product-price-variant {\r\n                        font-size: 16px;\r\n                    }\r\n                    .rating-icon {\r\n                        margin-right: 10px;\r\n                    }\r\n                    .product-rating {\r\n                        display: block;\r\n                        margin-bottom: 5px;\r\n                        .rating-number {\r\n                            margin-left: 0;\r\n                        }\r\n                    }\r\n                    .product-cart {\r\n                        margin-top: 10px;\r\n                    }\r\n                }\r\n            }\r\n            &:last-child {\r\n                margin-bottom: 0;\r\n            }\r\n        }\r\n        &.show {\r\n            visibility: visible;\r\n            opacity: 1;\r\n        }\r\n    }\r\n    &.open {\r\n        visibility: visible;\r\n        opacity: 1;\r\n        transform: translate(-50%, -50%) scale(1);\r\n        transition: all .3s cubic-bezier(0.29, 1.39, 0.86, 1.15);\r\n        \r\n    }\r\n}\r\n\r\n/*-----------------------\r\n    Header Aside Menu\r\n-------------------------*/\r\n.axil-mainmenu.aside-category-menu {\r\n    background-color: #f7f7f7;\r\n    @media only screen and (max-width: 991px) {\r\n        padding: 10px 0;   \r\n    }\r\n    .header-main-nav {\r\n        margin-right: 0;\r\n        margin-left: 40px;\r\n        @media only screen and (max-width: 991px) {\r\n            margin-left: 0;   \r\n        }\r\n    }\r\n    .header-nav-department {\r\n        width: 250px;\r\n        @media only screen and (max-width: 1199px) {\r\n            width: auto;\r\n        }\r\n    }\r\n    .header-department {\r\n        position: relative;\r\n        .header-department-text {\r\n            font-size: 16px;\r\n            background: var(--color-primary);\r\n            margin-bottom: 0;\r\n            display: flex;\r\n            padding: 17px 30px;\r\n            position: relative;\r\n            @media only screen and (max-width: 991px) {\r\n                padding: 10px 20px;  \r\n                border-radius: 6px;\r\n            }\r\n            .icon {\r\n                margin-right: 20px;\r\n                color: var(--color-white);\r\n                font-size: 18px;\r\n                @media only screen and (max-width: 991px) {\r\n                    margin-right: 15px;   \r\n                }\r\n                @media #{$small-mobile} {\r\n                    display: none;\r\n                }\r\n            }\r\n            .text {\r\n                color: var(--color-white);\r\n                margin: 0;\r\n                cursor: pointer;\r\n            }\r\n        }\r\n        .department-nav-menu {\r\n            position: absolute;\r\n            top: 100%;\r\n            left: 0;\r\n            right: 0;\r\n            background-color: var(--color-white);\r\n            border: 1px solid #f3f3f3;\r\n            padding: 6px 0;\r\n            transition: var(--transition);\r\n            z-index: 5;\r\n            @media #{$smlg-device} {\r\n                position: fixed;\r\n                top: 0;\r\n                left: -260px;\r\n                bottom: 0;\r\n                z-index: 50;\r\n                width: 250px;\r\n                padding: 70px 0 20px;\r\n                height: 100%;\r\n                border-radius: 0;\r\n            }\r\n            .sidebar-close {\r\n                font-size: 14px;\r\n                color: var(--color-black);\r\n                position: absolute;\r\n                top: 8px;\r\n                right: 15px;\r\n                height: 30px;\r\n                width: 30px;\r\n                line-height: 30px;\r\n                background-color: var(--color-lighter);\r\n                border-radius: 10px;\r\n                display: none;\r\n                &:hover {\r\n                    background-color: var(--color-primary);\r\n                    color: var(--color-white);\r\n                }\r\n                @media only screen and (max-width: 1199px) {\r\n                    display: block;\r\n                }\r\n            }\r\n            .nav-menu-list {\r\n                @extend %liststyle;\r\n                @media only screen and (max-width: 1199px) {\r\n                    height: 100%;\r\n                    overflow: auto;\r\n                }\r\n            }\r\n            >ul {\r\n                >li {\r\n                    padding: 0 30px;\r\n                    margin: 0;\r\n                    position: relative;\r\n                    &:hover {\r\n                        .department-megamenu {\r\n                            pointer-events: auto;\r\n                            visibility: visible;\r\n                            opacity: 1;\r\n                            transform: translateX(0);\r\n                            .department-submenu {\r\n                                opacity: 1;\r\n                                transform: translateX(0);\r\n                            }\r\n                            .featured-product {\r\n                                opacity: 1;\r\n                                transform: translateY(0);\r\n                            }\r\n                        }\r\n                    }\r\n                    &:last-child {\r\n                        .nav-link {\r\n                            border-bottom: none;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n            .nav-link {\r\n                display: flex;\r\n                align-items: center;\r\n                font-size: 14px;\r\n                font-weight: 500;\r\n                color: #999999;\r\n                padding: 13px 0;\r\n                border-bottom: 1px solid #f3f3f3;\r\n                position: relative;\r\n                @media #{$smlg-device} {\r\n                    font-size: 16px;\r\n                    padding: 12px 0;    \r\n                }\r\n                .menu-icon {\r\n                    margin-right: 14px;\r\n                    position: relative;\r\n                    top: -2px;\r\n                    img {\r\n                        width: 25px;\r\n                        height: auto;\r\n                    }\r\n                }\r\n                &.has-megamenu {\r\n                    &:after {\r\n                        content: \"\\f107\";\r\n                        font-family: var(--font-awesome);\r\n                        font-weight: 400;\r\n                        color: #c5c5c5;\r\n                        font-size: 14px;\r\n                        position: absolute;\r\n                        top: 50%;\r\n                        transform: translateY(-50%);\r\n                        right: 0;\r\n                        transition: var(--transition);\r\n                    }\r\n                }\r\n                &:hover {\r\n                    color: var(--color-heading);\r\n                    &:after {\r\n                        color: var(--color-primary);\r\n                        transform: translateY(-50%) rotate(-90deg);\r\n                    }\r\n                }\r\n            }\r\n            &.open {\r\n                left: 0;\r\n            }\r\n        }\r\n        .department-megamenu {\r\n            position: absolute;\r\n            top: 0;\r\n            left: 100%;\r\n            width: 990px;\r\n            z-index: 3;\r\n            transform: translateX(10px);\r\n            visibility: hidden;\r\n            opacity: 0;\r\n            pointer-events: none;\r\n            transition: all 0.3s ease-in-out;\r\n            margin-left: 1px;\r\n            @media only screen and (max-width: 1320px) {\r\n                width: 870px; \r\n            }\r\n            @media only screen and (max-width: 1199px) {\r\n                position: initial;\r\n                visibility: visible;\r\n                opacity: 1;\r\n                transform: translateX(0);\r\n                pointer-events: auto;\r\n                width: auto;\r\n                display: none;\r\n                transition: initial;\r\n            }\r\n            .department-megamenu-wrap {\r\n                background-color: var(--color-white);\r\n                border-radius: 0 0 24px 0;\r\n                box-shadow: 40px 40px 48px 0px rgba(36, 41, 47, 0.1);\r\n                padding: 30px;\r\n                display: flex;\r\n                @media only screen and (max-width: 1320px) {\r\n                    padding: 15px;\r\n                }\r\n                @media only screen and (max-width: 1199px) {\r\n                    display: block;\r\n                    padding: 0;\r\n                    box-shadow: none;\r\n                    padding: 20px 0;\r\n                }\r\n            }\r\n            .department-submenu-wrap {\r\n                flex: auto;\r\n                padding: 30px;\r\n                display: grid;\r\n                grid-template-columns: repeat(3, 1fr);\r\n                column-gap: 20px;\r\n                border-right: 2px solid #F6F7FB;\r\n                @media only screen and (max-width: 1199px) {\r\n                    grid-template-columns: repeat(1, 1fr);\r\n                    padding: 0;\r\n                    border: none;\r\n                }\r\n            }\r\n            .department-submenu {\r\n                opacity: 0;\r\n                transform: translateX(10px);\r\n                transition: all 0.3s ease-in-out;\r\n                @media only screen and (max-width: 1199px) {\r\n                    opacity: 1;\r\n                    transform: translateX(0);\r\n                }\r\n                &:nth-child(1n) {\r\n                    transition-delay: 0.1s;\r\n                }       \r\n                &:nth-child(2n) {\r\n                    transition-delay: 0.2s;\r\n                }       \r\n                &:nth-child(3n) {\r\n                    transition-delay: 0.3s;\r\n                }\r\n                .submenu-heading {\r\n                    font-size: 16px;\r\n                    color: var(--color-black);\r\n                    margin-bottom: 12px;\r\n                    @media #{$smlg-device} {\r\n                        font-size: 16px;\r\n                    }\r\n                }\r\n                ul {\r\n                    margin-bottom: 30px;\r\n                    @extend %liststyle;\r\n                    li {\r\n                        a {\r\n                            font-size: 14px;\r\n                            font-weight: 500;\r\n                            color: var(--color-body);\r\n                            padding: 10px 0;\r\n                            &:hover {\r\n                                color: var(--color-primary);\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n            .featured-product {\r\n                padding: 40px 30px 40px 55px;\r\n                max-width: 40%;\r\n                opacity: 0;\r\n                transform: translateY(10px);\r\n                transition: all 0.3s ease-in-out;\r\n                transition-delay: 0.4s;\r\n                @media only screen and (max-width: 1199px) {\r\n                    max-width: 100%;\r\n                    opacity: 1;\r\n                    transform: translateY(0);\r\n                    padding: 0;\r\n\r\n                }\r\n                .featured-heading {\r\n                    font-size: 16px;\r\n                    color: var(--color-black);\r\n                    margin-bottom: 12px;\r\n                }\r\n                .product-list {\r\n                    display: grid;\r\n                    grid-template-columns: repeat(2, 1fr);\r\n                    gap: 15px;\r\n                    .item-product {\r\n                        &:nth-child(-n +2) {\r\n                            grid-column: span 2;\r\n                        }\r\n                        a {\r\n                            overflow: hidden;\r\n                            display: block;\r\n                            border-radius: 8px;\r\n                            img {\r\n                                border-radius: 8px;\r\n                                transition: 0.4s ease-in-out;\r\n                            }\r\n                            &:hover {\r\n                                img {\r\n                                    transform: scale(1.1);\r\n                                }\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n                .axil-btn {\r\n                    margin-top: 15px;\r\n                    display: block;\r\n                    text-align: center;\r\n                }\r\n            }\r\n        } \r\n    }\r\n\r\n    .mainmenu {\r\n        justify-content: flex-start;\r\n        >li {\r\n            @media #{$smlg-device} {\r\n                margin: 0 20px;\r\n            }\r\n            &:last-child {\r\n                margin-right: 0;\r\n            }\r\n            >a {\r\n                line-height: 60px;\r\n                height: 60px;\r\n                &:before {\r\n                    bottom: 18px;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n\r\n/*-----------------------\r\n    Mobile Menu\r\n-------------------------*/\r\n.header-main-nav {\r\n    .mainmenu-nav {  \r\n        @media only screen and (max-width: 991px) {\r\n            display: block;\r\n            position: fixed;\r\n            top: 0;\r\n            bottom: 0;\r\n            right: -250px;\r\n            width: 250px;\r\n            background-color: var(--color-white);\r\n            z-index: 100; \r\n            transition: all 0.3s ease-in-out;\r\n            padding: 20px 30px 10px;\r\n            visibility: hidden;\r\n            opacity: 0;\r\n            .mainmenu {\r\n                display: block;\r\n                height: calc(100vh - 85px);\r\n                overflow-y: auto;\r\n                margin: 0;\r\n                >li {\r\n                    margin: 10px 0 !important;\r\n                    transform: translateY(20px);\r\n                    opacity: 0;\r\n                    transition: all 0.3s ease-in-out;\r\n                    >a {\r\n                        color: var(--color-body);\r\n                        line-height: var(--line-height-b2) !important;\r\n                        height: auto !important;\r\n                        padding: 5px 0;\r\n                        display: inline-block;\r\n                        &::before {\r\n                           display: none;\r\n                        }\r\n                    }\r\n                    &.menu-item-has-children {\r\n                        a {\r\n                            margin: 0;\r\n                            &::after {\r\n                                right: -18px;\r\n                                top: 4px;\r\n                                color: var(--color-body);\r\n                            }\r\n                        }\r\n                        .axil-submenu {\r\n                            display: none;\r\n                            position: static;\r\n                            transform: scaleY(1);\r\n                            visibility: visible;\r\n                            opacity: 1;\r\n                            min-width: auto;\r\n                            box-shadow: none;\r\n                            padding: 0;\r\n                            transition: initial;\r\n                            li {\r\n                                a {\r\n                                    padding: 5px 10px;\r\n                                    &:after {\r\n                                        display: none;\r\n                                    }\r\n                                    &:hover {\r\n                                        color: var(--color-primary);\r\n                                        background-color: transparent;\r\n                                    }\r\n                                }\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    &.open {\r\n        .mainmenu-nav {\r\n            right: 0;\r\n            visibility: visible;\r\n            opacity: 1;\r\n            .mainmenu {\r\n                li {\r\n                    transform: translateY(0);\r\n                    opacity: 1;\r\n                    &:nth-child(1n) {\r\n                        transition-delay: 0.3s;\r\n                    }\r\n                    &:nth-child(2n) {\r\n                        transition-delay: 0.4s;\r\n                    }\r\n                    &:nth-child(3n) {\r\n                        transition-delay: 0.5s;\r\n                    }\r\n                    &:nth-child(4n) {\r\n                        transition-delay: 0.6s;\r\n                    }\r\n                    &:nth-child(5n) {\r\n                        transition-delay: 0.7s;\r\n                    }\r\n                    &:nth-child(6n) {\r\n                        transition-delay: 0.8s;\r\n                    }\r\n                    &:nth-child(7n) {\r\n                        transition-delay: 0.9s;\r\n                    }\r\n                    &:nth-child(8n) {\r\n                        transition-delay: 1s;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.mobile-close-btn {\r\n    background-color: transparent;\r\n    position: absolute;\r\n    top: 19px;\r\n    right: 15px;\r\n    height: 35px;\r\n    width: 35px;\r\n    background-color: var(--color-lighter);\r\n    border-radius: 40px;\r\n    color: var(--color-dark);\r\n    font-size: 12px;\r\n    @media only screen and (min-width: 992px) {\r\n        display: none;   \r\n    }\r\n    &:hover {\r\n        background-color: var(--color-primary);\r\n        color: var(--color-white);\r\n    }\r\n}\r\n\r\n.mobile-nav-brand {\r\n    margin-bottom: 30px;\r\n    img {\r\n        max-height: 35px;\r\n    }\r\n    @media only screen and (min-width: 992px) {\r\n        display: none;   \r\n    }\r\n}\r\n\r\n.axil-mobile-toggle {\r\n    margin-left: 30px;\r\n    @media only screen and (min-width: 992px) {\r\n        display: none;\r\n    }\r\n    @media #{$large-mobile} {\r\n        margin-left: 18px;\r\n    }\r\n    .menu-btn {\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        padding: 0;\r\n        background-color: transparent;\r\n        position: relative;\r\n        z-index: 1;\r\n        color: var(--color-heading);\r\n        font-size: 23px;\r\n        @media #{$small-mobile} {\r\n            font-size: 22px;\r\n        }\r\n        i {\r\n            display: inline-block;\r\n            line-height: 0;\r\n        }\r\n        &:after {\r\n            content: \"\";\r\n            height: 40px;\r\n            width: 40px;\r\n            background: var(--color-secondary);\r\n            border-radius: 50%;\r\n            position: absolute;\r\n            z-index: -1;\r\n            transform: scale(0);\r\n            transition: var(--transition);\r\n            @media #{$large-mobile} {\r\n                height: 35px;\r\n                width: 35px;\r\n            }\r\n        }\r\n        &:hover {\r\n            color: var(--color-white);\r\n            &:after {\r\n                transform: scaleX(1);\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.header-mobile-brand {\r\n    img {\r\n        width: 150px;\r\n    }\r\n}\r\n\r\n", "/*-------------------------\r\n    Main Menu Nav  \r\n--------------------------*/\r\n.mainmenu-nav {\r\n    @media only screen and (max-width: 991px) {\r\n        display: none;   \r\n    }\r\n}\r\n.mainmenu {\r\n    @extend %liststyle;\r\n    display: flex;\r\n    align-items: center;\r\n    flex-wrap: wrap;\r\n    justify-content: center;\r\n    margin: 0 -24px;\r\n    @media #{$smlg-device} {\r\n        margin: 0 -15px;\r\n    }\r\n    >li {\r\n        margin: 0 24px;\r\n        @media #{$smlg-device} {\r\n            margin: 0 15px;\r\n        }\r\n        >a {\r\n            color: var(--color-heading);\r\n            font-weight: 700;\r\n            font-size: 15px;\r\n            font-family: var(--font-primary);\r\n            line-height: 80px;\r\n            height: 80px;\r\n            display: block;\r\n            position: relative;\r\n            transition: var(--transition);\r\n            &::before {\r\n                content: \"\";\r\n                height: 2px;\r\n                width: 0;\r\n                background-color: var(--color-black);\r\n                position: absolute;\r\n                bottom: 29px;\r\n                left: 0;\r\n                opacity: 0;\r\n                transition: 0.5s;\r\n            }\r\n            &:hover {\r\n                color: var(--color-black);\r\n                &::before {\r\n                    opacity: 1;\r\n                    width: 100%;\r\n                }\r\n            }\r\n            &.active {\r\n                color: var(--color-black);\r\n                &:before {\r\n                    width: 100%;\r\n                    opacity: 1;\r\n                }\r\n            }\r\n        }\r\n    }\r\n    >.menu-item-has-children {\r\n        position: relative;\r\n        >a {\r\n            position: relative;\r\n            margin-right: 15px;\r\n            &::after {\r\n                content: \"\\f107\";\r\n                font-family: var(--font-awesome);\r\n                font-weight: 400;\r\n                color: #c6c6c6;\r\n                font-size: 14px;\r\n                position: absolute;\r\n                top: 1px;\r\n                right: -14px;\r\n            }\r\n        }\r\n        &.menu-item-open {\r\n            >a {\r\n                &:before {\r\n                    width: 100%;\r\n                    opacity: 1;\r\n                }\r\n            }\r\n        }\r\n        .axil-submenu {\r\n            position: absolute;\r\n            top: 100%;\r\n            left: 0;\r\n            background: #ffffff;\r\n            z-index: -1;\r\n            opacity: 0;\r\n            visibility: hidden;\r\n            min-width: 250px;\r\n            padding: 15px 10px;\r\n            border-radius: 4px;\r\n            box-shadow: var(--shadow-primary);\r\n            transition: all 0.3s ease-in-out;\r\n            list-style: none;\r\n            pointer-events: none;\r\n            li {\r\n                margin: 0;\r\n                a {\r\n                    position: relative;\r\n                    font-size: 15px;\r\n                    text-transform: capitalize;\r\n                    color: var(--color-heading);\r\n                    font-weight: 500;\r\n                    padding: 5px 15px;\r\n                    border-radius: 4px;\r\n                    display: block;\r\n                    transition: all 0.3s ease-in-out;\r\n                    z-index: 1;\r\n                    overflow: hidden;\r\n                    &:hover {\r\n                        color: var(--color-secondary);\r\n                        // background: var(--color-secondary);\r\n                    }\r\n                    &.active {\r\n                        color: var(--color-secondary);\r\n                        &:hover {\r\n                            color: var(--color-secondary);\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        &:hover {\r\n            .axil-submenu {\r\n                top: 90%;\r\n                opacity: 1;\r\n                visibility: visible;\r\n                z-index: 9;\r\n                pointer-events: all;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n", "/*----------------------\r\nHeader Shopping Cart  \r\n-----------------------*/\r\n.cart-dropdown {\r\n    position: fixed;\r\n    right: -600px;\r\n    top: 0;\r\n    bottom: 0;\r\n    z-index: 101;\r\n    transition: all 0.5s cubic-bezier(0.55, 0, 0.1, 1);\r\n    @media only screen and (max-width: 767px) {\r\n        width: 100%; \r\n        right: -100%;\r\n    }\r\n    .cart-content-wrap {\r\n        background-color: var(--color-white);\r\n        padding: 60px 50px;\r\n        width: 600px;\r\n        height: 100%;\r\n        display: flex;\r\n        flex-direction: column;\r\n        overflow: auto;\r\n        @media only screen and (max-width: 767px) {\r\n            width: 100%;   \r\n            padding: 30px;\r\n        }\r\n        @media only screen and (max-width: 479px) {  \r\n            padding: 30px 15px;\r\n        }\r\n        /* width */\r\n        &::-webkit-scrollbar {\r\n            width: 8px;\r\n            border-radius: 10px;\r\n        }\r\n        \r\n        /* Track */\r\n        &::-webkit-scrollbar-track {\r\n            background: #f1f1f1; \r\n            border-radius: 10px;\r\n            transition: .5s;\r\n        }\r\n        \r\n        /* Handle */\r\n        &::-webkit-scrollbar-thumb {\r\n            background: var(--color-lightest);\r\n            border-radius: 10px;\r\n            transition: .5s;\r\n        }\r\n        \r\n        /* Handle on hover */\r\n        &::-webkit-scrollbar-thumb:hover {\r\n            background: var(--color-primary); \r\n        }\r\n        \r\n    }\r\n    .cart-header {\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: space-between;\r\n        border-bottom: 2px solid #F6F7FB;\r\n        padding-bottom: 18px;\r\n        .header-title {\r\n            font-size: 26px;\r\n            color: #27272E;\r\n            margin-bottom: 0;\r\n            @media #{$sm-layout} {\r\n                font-size: 24px;\r\n            }\r\n        }\r\n        .cart-close {\r\n            height: 40px;\r\n            width: 40px;\r\n            font-size: 16px;\r\n            color: var(--color_black);\r\n            border-radius: 50%;\r\n            @extend %transition;\r\n            &:hover {\r\n                background-color: var(--color-primary);\r\n                color: var(--color-white);\r\n            }\r\n        }\r\n    }\r\n    .cart-body {\r\n        padding: 30px 0;\r\n        flex: auto;\r\n    }\r\n    .cart-item-list {\r\n        @extend %liststyle;\r\n    }\r\n    .cart-item {\r\n        display: flex;\r\n        align-items: center;\r\n        margin-bottom: 30px;\r\n        border-bottom: 1px solid #F6F7FB;\r\n        padding-bottom: 30px;\r\n        @media #{$small-mobile} {\r\n            align-items: flex-start;\r\n        }\r\n        &:last-child {\r\n            margin-bottom: 0;\r\n            border-bottom: none;\r\n        }\r\n        .item-img {\r\n            margin-right: 30px;\r\n            position: relative;\r\n            @media only screen and (max-width: 479px) {\r\n                margin-right: 15px; \r\n            }\r\n            a {\r\n                display: block;\r\n                background-color: #F6F7FB;\r\n                border-radius: 10px;\r\n                @media #{$sm-layout} {\r\n                    width: 70px;\r\n                }\r\n                img {\r\n                    border-radius: 10px;\r\n                    height: 100px;\r\n                    width: 100px;\r\n                    object-fit: cover;\r\n                }\r\n            }\r\n            .close-btn {\r\n                height: 31px;\r\n                width: 31px;\r\n                background-color: #F6F7FB;\r\n                border: 2px solid var(--color-white);\r\n                border-radius: 50%;\r\n                font-size: 12px;\r\n                color: var(--color-black);\r\n                position: absolute;\r\n                top: -15px;\r\n                left: -10px;\r\n                transition: all 0.3s ease-in-out;\r\n                @media #{$sm-layout} {\r\n                    height: 25px;\r\n                    width: 25px;\r\n                    font-size: 10px;\r\n                }\r\n                &:hover {\r\n                    background-color: var(--color-primary);\r\n                    color: var(--color-white);\r\n                }\r\n            }\r\n        }\r\n        .item-content {\r\n            flex: 1;\r\n            position: relative;\r\n            padding-right: 110px;\r\n            @media #{$small-mobile} {\r\n               padding-right: 0;\r\n            }\r\n        }\r\n        .product-rating {\r\n            margin-bottom: 14px;\r\n            font-size: 10px;\r\n            transition: var(--transition);\r\n            i {\r\n                color: #FFDC60;\r\n            }\r\n            .rating-number {\r\n                margin-left: 5px;\r\n                font-weight: 500;\r\n            }\r\n        }\r\n        .item-title {\r\n            font-size: 16px;\r\n            color: var(--color-black);\r\n            margin-bottom: 10px;\r\n            overflow: hidden;\r\n            text-overflow: ellipsis;\r\n            display: -webkit-box;\r\n            -webkit-line-clamp: 1;\r\n            -webkit-box-orient: vertical;\r\n            @media #{$sm-layout} {\r\n                font-size: 16px;  \r\n                margin-bottom: 15px;  \r\n            }\r\n            @media #{$small-mobile} {\r\n                margin-bottom: 5px;\r\n            }\r\n        }\r\n        .item-price {\r\n            font-size: 18px;\r\n            font-family: var(--font-secondary);\r\n            color: var(--color-black);\r\n            @media #{$sm-layout} {\r\n                font-size: 16px;\r\n                margin-top: 10px;\r\n            }\r\n        }\r\n        .item-quantity {\r\n            display: flex;\r\n            align-items: center;\r\n            position: absolute;\r\n            top: 50%;\r\n            right: 0;\r\n            transform: translateY(-50%);\r\n            justify-content: flex-end;\r\n            @media #{$small-mobile} {\r\n                position: initial;\r\n                transform: translateY(0);\r\n                justify-content: flex-start;\r\n                margin-top: 8px;\r\n            }\r\n            .qtybtn {\r\n                text-align: center;\r\n                height: 26px;\r\n                width: 26px;\r\n                line-height: 20px;\r\n                font-size: 18px;\r\n                color: var(--color-black);\r\n                background-color: #F6F7FB;\r\n                border-radius: 50%;\r\n                transition: all 0.3s ease-in-out;\r\n                &:hover {\r\n                    background-color: var(--color-primary);\r\n                    color: var(--color-white);\r\n                }\r\n            }\r\n            .quantity-input {\r\n                font-size: 16px;\r\n                font-weight: 600;\r\n                color: #27272E;\r\n                height: 26px;\r\n                width: 30px;\r\n                border: none;\r\n                text-align: center;\r\n                padding: 0;\r\n            }\r\n            input::-webkit-outer-spin-button,\r\n            input::-webkit-inner-spin-button {\r\n              -webkit-appearance: none;\r\n              margin: 0;\r\n            }\r\n            input[type=number] {\r\n              -moz-appearance: textfield;\r\n            }\r\n        }\r\n    }\r\n    .cart-footer {\r\n        border-top: 2px solid #F6F7FB;\r\n        .cart-subtotal {\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: space-between;\r\n            margin: 22px 0 30px;\r\n            color: var(--color-black);\r\n            font-size: 20px;\r\n            .subtotal-amount {\r\n                font-weight: 700;\r\n            }\r\n        }\r\n        .group-btn {\r\n            display: grid;\r\n            grid-template-columns: repeat(2, 1fr);\r\n            column-gap: 20px;\r\n            @media #{$large-mobile} {\r\n                display: block;\r\n            }\r\n            .axil-btn {\r\n                text-align: center;\r\n                &:hover {\r\n                    &:before {\r\n                        transform: scale(1.05);\r\n                    }\r\n                }\r\n                @media #{$large-mobile} {\r\n                    display: block;\r\n                    margin-bottom: 10px;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n.cart-dropdown.open {\r\n    right: 0;\r\n}\r\n\r\nbody.open .closeMask {\r\n    height: 100%;\r\n    width: 100%;\r\n    background-color: rgba(0, 0, 0, 0.6);\r\n    position: fixed;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    z-index: 6;\r\n    transition: 0.3s;\r\n\r\n}", "/*----------------------\r\n    Shop Styles  \r\n----------------------*/\r\n.category-select {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    margin: -10px;\r\n    .single-select {\r\n        color: var(--color-dark);\r\n        width: auto;\r\n        margin: 10px;\r\n        padding-right: 43px;\r\n        background: url(../images/icons/arrow-icon2.png) 85% center no-repeat transparent;\r\n        font-family: var(--font-primary);\r\n        font-weight: 500;\r\n        font-size: var(--font-size-b1);\r\n        border: 2px solid var(--color-light);\r\n        @media #{$sm-layout} {\r\n           width: 100%;\r\n           background-position-x: 95%; \r\n        }\r\n    }\r\n}\r\n\r\n\r\n/* Start Axil Single Product  */\r\n\r\n.axil-product {\r\n    position: relative;\r\n    @media #{$large-mobile} {\r\n        text-align: center;\r\n    }\r\n    >.thumbnail {\r\n        position: relative;\r\n        display: block;\r\n        >a {\r\n            display: block;\r\n            background-color: #f7f7f7;\r\n            border-radius: 6px;\r\n            overflow: hidden;\r\n            position: relative;\r\n            img {\r\n                border-radius: 6px;\r\n                width: 100%;\r\n                transition: 0.3s;\r\n            }\r\n            .hover-img {\r\n                position: absolute;\r\n                top: 0;\r\n                left: 0;\r\n                right: 0;\r\n                bottom: 0;\r\n                visibility: hidden;\r\n                opacity: 0;\r\n                transition: .3s;\r\n            }\r\n        }\r\n        .label-block {\r\n            position: absolute;\r\n            top: 24px;\r\n            left: 24px;\r\n            z-index: 2;\r\n            .product-badget {\r\n                background-color: var(--color-primary);\r\n                line-height: 1;\r\n                padding: 6px 10px 5px;\r\n                font-size: 12px;\r\n                font-weight: 700;\r\n                color: #FFFFFF;\r\n                border-radius: 4px;\r\n                box-shadow: 0 8px 16px 0 rgba(53, 119, 240, .3);\r\n            }\r\n            &.label-right {\r\n                left: auto;\r\n                right: -10px;\r\n            }\r\n        }\r\n    }\r\n    .sub-title {\r\n        margin-bottom: 10px;\r\n    }\r\n    .product-content {\r\n        margin-top: 25px;\r\n        position: relative;\r\n        margin-bottom: 30px;\r\n        .product-rating {\r\n            margin-bottom: 10px;\r\n            font-size: 13px;\r\n            transition: var(--transition);\r\n            i {\r\n                color: #FFDC60;\r\n            }\r\n            .rating-number {\r\n                margin-left: 5px;\r\n                font-weight: 500;\r\n            }\r\n        }\r\n        .inner {\r\n            transition: 0.3s;\r\n        }\r\n        .sub-title {\r\n            transition: var(--transition);  \r\n        }\r\n        .title {\r\n            color: var(--color-body);\r\n            font-size: 16px;\r\n            font-weight: 500;\r\n            margin-bottom: 10px;\r\n            transition: var(--transition);\r\n            a {\r\n                transition: 0.3s;\r\n            }\r\n        }\r\n        .product-price-variant {\r\n            margin: -4px;\r\n            transition: var(--transition);\r\n            transition-delay: 0.1s;\r\n            @media #{$large-mobile} {\r\n                justify-content: center;\r\n            }\r\n            span {\r\n                &.price {\r\n                    margin: 4px;\r\n                    color: var(--color-heading);\r\n                    font-weight: 700;\r\n                    font-size: 20px;\r\n                    font-family: var(--font-secondary);\r\n                    &.old-price {\r\n                        color: #d6d6d6;\r\n                        text-decoration: line-through;\r\n                        margin-left: 0;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n    .product-hover-action {\r\n        position: absolute;\r\n        bottom: 0;\r\n        left: 0;\r\n        right: 0;\r\n        opacity: 0;\r\n        visibility: hidden;\r\n        transition: 0.5s;\r\n      \r\n    }\r\n    .cart-action {\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        @extend %liststyle;\r\n        margin: -5px;\r\n        li {\r\n            margin: 5px;\r\n            &.wishlist,\r\n            &.quickview {\r\n                a {\r\n                    width: 40px;\r\n                    height: 40px;\r\n                    line-height: 42px;\r\n                    border-radius: 4px;\r\n                    background-color: var(--color-white);\r\n                    display: block;\r\n                    text-align: center;\r\n                    transition: 0.3s;\r\n                    position: relative;\r\n                    font-size: 14px;\r\n                    color: var(--color-heading);\r\n                    box-shadow: 0 16px 32px 0 rgba(0,0,0,.06);\r\n                    position: relative;\r\n                    z-index: 1;\r\n                    &:before {\r\n                        content: \"\";\r\n                        height: 100%;\r\n                        width: 100%;\r\n                        background-color: var(--color-white);\r\n                        border-radius: 4px;\r\n                        position: absolute;\r\n                        top: 0;\r\n                        bottom: 0;\r\n                        left: 0;\r\n                        right: 0;\r\n                        z-index: -1;\r\n                        transition: transform .5s cubic-bezier(.165,.84,.44,1);\r\n                    }\r\n                    i {\r\n                        transition: all .3s ease-in-out;\r\n                    }\r\n                    &:hover {\r\n                        &:before {\r\n                            transform: scale(1.2);\r\n                        }\r\n                        i {\r\n                            animation: btnIconSlide 400ms;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n            &.select-option {\r\n                a {\r\n                    position: relative;\r\n                    height: 40px;\r\n                    line-height: 39px;\r\n                    padding: 0 18px;\r\n                    display: block;\r\n                    border-radius: 4px;\r\n                    font-weight: 700;\r\n                    font-size: 14px;\r\n                    color: var(--color-white);\r\n                    background-color: var(--color-secondary);\r\n                    transition: 0.3s;\r\n                    box-shadow: 0 16px 32px 0 rgba(0,0,0,.06);\r\n                    position: relative;\r\n                    z-index: 1;\r\n                    &:before {\r\n                        content: \"\";\r\n                        height: 100%;\r\n                        width: 100%;\r\n                        background-color: var(--color-secondary);\r\n                        border-radius: 4px;\r\n                        position: absolute;\r\n                        top: 0;\r\n                        bottom: 0;\r\n                        left: 0;\r\n                        right: 0;\r\n                        z-index: -1;\r\n                        transition: transform .5s cubic-bezier(.165,.84,.44,1);\r\n                    }\r\n                    &:hover {\r\n                        background-color: var(--color-secondary);\r\n                        color: var(--color-white);\r\n                        &:before {\r\n                            transform: scale(1.1);\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n    &:hover {\r\n        .thumbnail {\r\n            a {\r\n                img {\r\n                    transform: scale(1.1);\r\n                }\r\n                .hover-img {\r\n                    visibility: visible;\r\n                    opacity: 1;\r\n                }\r\n            }\r\n        }\r\n        .product-hover-action {\r\n            bottom: 30px;\r\n            opacity: 1;\r\n            visibility: visible;\r\n            transition-delay: 0.2s;\r\n        }\r\n    }\r\n    &.product-style-two {\r\n        text-align: center;\r\n        .product-content {\r\n            .color-variant,\r\n            .product-price-variant {\r\n                justify-content: center;\r\n                margin-top: 0;\r\n            }\r\n            .color-variant {\r\n                margin-bottom: 10px;\r\n            }\r\n            .product-price-variant {\r\n                margin-bottom: 15px;\r\n            }\r\n\r\n        }\r\n        .thumbnail a {\r\n            width: 276px;\r\n            height: 276px;\r\n            overflow: hidden;\r\n            border-radius: 50%;\r\n            margin: 0 auto;\r\n            @media #{$sm-layout} {\r\n                width: 200px;\r\n                height: 200px;\r\n            } \r\n            @media #{$large-mobile} {\r\n                width: 250px;\r\n                height: 250px;\r\n            }\r\n        }\r\n        .product-hover-action {\r\n            position: initial;\r\n            visibility: visible;\r\n            opacity: 1;\r\n        }\r\n    }\r\n    &.product-style-four {\r\n        text-align: center;\r\n        .product-content {\r\n            .product-price-variant {\r\n                justify-content: center;\r\n            }\r\n            .color-variant {\r\n                justify-content: center;\r\n            }\r\n        } \r\n    }\r\n    &.product-style-five {\r\n        border-radius: 6px;\r\n        text-align: center;\r\n        .thumbnail {\r\n            a {\r\n                border-radius: 6px 6px 0 0;\r\n                img {\r\n                    border-radius: 6px 6px 0 0;\r\n                }\r\n            }\r\n        }\r\n        .product-content {\r\n            padding: 25px 30px 30px;\r\n            margin: 0;\r\n            background-color: var(--color-white);\r\n            .cart-action {\r\n                padding-top: 10px;\r\n            }\r\n        }\r\n    }\r\n    &.product-style-six {\r\n        border: 1px solid #f1f1f1;\r\n        border-radius: 6px;\r\n        margin-bottom: 30px;\r\n        transition: .3s;\r\n        @media #{$large-mobile} {\r\n            text-align: left;\r\n        }\r\n        .thumbnail {\r\n            a {\r\n                border-radius: 6px 6px 0 0;\r\n                img {\r\n                    border-radius: 6px 6px 0 0;\r\n                    transition: transform 3s cubic-bezier(0.2, 0.96, 0.34, 1);\r\n                }\r\n            }\r\n        }\r\n        .product-content {\r\n            margin: 0;\r\n            padding: 35px 30px 30px;\r\n            z-index: 1;\r\n            .product-price-variant {\r\n                position: absolute;\r\n                top: -57px;\r\n                right: 25px;\r\n                z-index: -1;\r\n                background-color: rgba(255, 255, 255, .5);\r\n                border: 1px solid rgba(255, 255, 255, .5);\r\n                backdrop-filter: blur(25px);\r\n                box-shadow: 0 4px 30px rgba(0, 0, 0, .1);\r\n                padding: 5px 15px;\r\n                border-radius: 6px;\r\n                span {\r\n                    &.price {\r\n                        font-size: 18px;\r\n                        color: var(--color-white);\r\n                    }\r\n                }\r\n            }\r\n            .title {\r\n                margin-bottom: 15px;\r\n            }\r\n            .product-hover-action {\r\n                position: initial;\r\n                opacity: 1;\r\n                visibility: visible;\r\n                .cart-action {\r\n                    justify-content: flex-start;\r\n                    li {\r\n                        &.select-option {\r\n                            a {\r\n                                background-color: transparent;\r\n                                border: 1px solid #efefef;\r\n                                color: var(--color-heading);\r\n                                box-shadow: none;\r\n                                &:before {\r\n                                    display: none;\r\n                                }\r\n                                &:hover {\r\n                                    background-color: var(--color-primary);\r\n                                    color: var(--color-white);\r\n                                    border-color: var(--color-primary);\r\n                                }\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n\r\n            }\r\n        }\r\n        &:hover {\r\n            box-shadow: var(--shadow-dark);\r\n            border-color: var(--color-white);\r\n            .thumbnail {\r\n                img {\r\n                    transform: scale(1.3);\r\n                }\r\n            }\r\n        }\r\n    }\r\n    &.product-style-seven {\r\n        &:before {\r\n            content: \"\";\r\n            height: 70%;\r\n            width: 100%;\r\n            background-color: #f7f7f7;\r\n            border-radius: 6px;\r\n            position: absolute;\r\n            top: 0;\r\n            left: 0;\r\n            right: 0;\r\n            transition: 0.3s;\r\n        }\r\n        .product-content {\r\n            margin: 0;\r\n            padding: 40px 30px 0;\r\n            position: relative;\r\n            .cart-btn {\r\n                position: absolute;\r\n                top: -20px;\r\n                right: 20px;\r\n                a {\r\n                    display: inline-block;\r\n                    text-align: center;\r\n                    height: 45px;\r\n                    width: 45px;\r\n                    line-height: 46px;\r\n                    background-color: var(--color-lighter);\r\n                    border: 2px solid var(--color-white);\r\n                    color: var(--color-heading);\r\n                    font-size: 18px;\r\n                    border-radius: 50%;\r\n                    transition: .3s;\r\n                    box-shadow: 0 16px 32px 0 rgba(103, 103, 103, .06);\r\n                    &:hover {\r\n                        background-color: var(--color-primary);\r\n                        border-color: var(--color-primary);\r\n                        color: var(--color-white);\r\n                        box-shadow: 0 8px 16px 0 rgba(53, 119, 240, .3);\r\n                    }\r\n                }\r\n            }\r\n            .product-rating {\r\n                margin-bottom: 0;\r\n                margin-top: 10px;\r\n            }\r\n            .title {\r\n                font-size: 20px;\r\n                color: var(--color-heading);\r\n            }\r\n            .product-price-variant {\r\n                .price {\r\n                    font-size: 16px;\r\n                }\r\n            }\r\n\r\n        }\r\n        .thumbnail {\r\n            a {\r\n                background-color: transparent;\r\n                overflow: visible;\r\n            }\r\n        }\r\n        &:hover {\r\n            .thumbnail {\r\n                img {\r\n                    transform: scale(.9);\r\n                }\r\n            }\r\n        }\r\n    }\r\n    &.product-style-eight {\r\n        .thumbnail {\r\n            a {\r\n                border-radius: 8px;\r\n            }\r\n        }\r\n        .label-block {\r\n            position: absolute;\r\n            z-index: 2;\r\n            &.label-left {\r\n                left: 10px;\r\n                top: 10px;\r\n            }\r\n            &.label-right {\r\n                left: auto;\r\n                right: 20px;\r\n                top: 20px;\r\n            }\r\n            .product-badget {\r\n                line-height: 1;\r\n                font-size: 12px;\r\n                font-weight: 500;\r\n                color: #FFFFFF;\r\n                border-radius: 4px;\r\n                background-color: var(--color-heading);\r\n                padding: 8px 10px;\r\n                box-shadow: none;\r\n                text-transform: uppercase;\r\n                &.sale {\r\n                    background-color: var(--color-white);\r\n                    color: var(--color-primary);\r\n                }\r\n                &.sold-out {\r\n                    background-color: var(--color-primary);\r\n                }\r\n            }\r\n        }\r\n        .cart-action {\r\n            display: block;\r\n            margin: 0 20px;\r\n            li.select-option {\r\n                a {\r\n                    text-align: center;\r\n                    display: flex;\r\n                    align-items: center;\r\n                    justify-content: center;\r\n                    border-radius: 8px;\r\n                    height: 42px;\r\n                    i {\r\n                        font-size: 21px;\r\n                        margin-right: 10px;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        .color-variant-wrapper {\r\n            margin-bottom: 12px;\r\n        }\r\n        .color-variant {\r\n            margin: -5px -2px;\r\n            li {\r\n                >span {\r\n                    height: 12px;\r\n                    width: 12px;\r\n                    border-width: 0;\r\n                    .color {\r\n                        height: 12px;\r\n                        width: 12px;\r\n                        transition: .3s;\r\n                    }\r\n                }\r\n                &.active {\r\n                    >span {\r\n                        border-width: 1px;\r\n                        .color {\r\n                            height: 6px;\r\n                            width: 6px;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        .product-content {\r\n            margin-bottom: 45px;\r\n            padding: 0 20px;\r\n            .title {\r\n                color: #26204B;\r\n                a {\r\n                    overflow: hidden;\r\n                    text-overflow: ellipsis;\r\n                    display: -webkit-box;\r\n                    -webkit-line-clamp: 3;\r\n                    -webkit-box-orient: vertical;\r\n                }\r\n            }\r\n            .product-rating {\r\n                font-size: 16px;\r\n                .rating-number {\r\n                    font-size: 12px;\r\n                }\r\n            }\r\n            .product-price-variant {\r\n                margin: 0;\r\n                line-height: 1.2;\r\n            }\r\n        }\r\n        &:hover {\r\n            .product-hover-action {\r\n                bottom: 20px;\r\n            }\r\n        }\r\n    }\r\n    &.product-list-style-3 {\r\n        display: flex;\r\n        align-items: center;\r\n        background-color: var(--color-lighter);\r\n        border-radius: 8px;\r\n        margin-bottom: 30px;\r\n        @media (max-width: 1199px) {\r\n            display: block;\r\n            padding: 40px 20px 20px;\r\n        }\r\n        .thumbnail {\r\n            padding: 26px 30px 26px 35px;\r\n            @media (max-width: 1199px) {\r\n                padding: 0 0 20px;\r\n            }\r\n            a {\r\n                position: relative;\r\n                z-index: 1;\r\n                &:before {\r\n                    content: \"\";\r\n                    height: 224px;\r\n                    width: 224px;\r\n                    border-radius: 50%;\r\n                    background-color: var(--color-white);\r\n                    position: absolute;\r\n                    bottom: 20px;\r\n                    left: 0;\r\n                    right: 0;\r\n                    margin: 0 auto;\r\n                    z-index: -1;\r\n                }\r\n            }\r\n        }\r\n        .product-content {\r\n            flex: 1;\r\n            margin: 0;\r\n            padding: 0 40px 0 0;\r\n            @media (max-width: 1199px) {\r\n                padding: 0;\r\n            }\r\n            .product-cate {\r\n                font-size: 12px;\r\n                font-weight: 500;\r\n                margin-bottom: 10px; \r\n            }\r\n            .title {\r\n                font-size: 20px;\r\n            }\r\n            .product-price-variant {\r\n                display: flex;\r\n                align-items: center;\r\n                .price-text {\r\n                    font-size: 20px;\r\n                    font-weight: 500;\r\n                    color: var(--color-heading);\r\n                    margin-right: 8px;\r\n                }\r\n                .price {\r\n                    font-size: 32px;\r\n                    font-weight: 700;\r\n                    color: var(--color-secondary);\r\n                }\r\n            }\r\n\r\n        }\r\n        &:hover {\r\n            .thumbnail {\r\n                a {\r\n                    img {\r\n                        transform: scale(1);\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.color-variant {\r\n    @extend %liststyle;\r\n    margin: -5px -2px;\r\n    margin-top: 12px;\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    @media #{$large-mobile} {\r\n        justify-content: center;\r\n    }\r\n    li {\r\n        margin: 5px 2px;\r\n        cursor: pointer;\r\n        >span {\r\n            border: 2px solid transparent;\r\n            width: 21px;\r\n            height: 21px;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n            border-radius: 50%;\r\n            transition: var(--transition);\r\n            .color {\r\n                display: block;\r\n                width: 9px;\r\n                height: 9px;\r\n                line-height: 1;\r\n                border-radius: 50%;\r\n                border: none;\r\n            }\r\n        }\r\n        &.color-extra-01 {\r\n            span {\r\n                .color {\r\n                    background: #AAE6F8;\r\n                }\r\n            }\r\n            &.active {\r\n                span {\r\n                    border-color: #AAE6F8;\r\n                }\r\n            }\r\n        }\r\n        &.color-extra-02 {\r\n            span {\r\n                .color {\r\n                    background: #5F8AF7;\r\n                }\r\n            }\r\n            &.active {\r\n                span {\r\n                    border-color: #5F8AF7;\r\n                }\r\n            }\r\n        }\r\n        &.color-extra-03 {\r\n            span {\r\n                .color {\r\n                    background: #59C3C0;\r\n                }\r\n            }\r\n            &.active {\r\n                span {\r\n                    border-color: #59C3C0;\r\n                }\r\n            }\r\n        }\r\n        &.color-extra-04 {\r\n            span {\r\n                .color {\r\n                    background: #D3BBF3;\r\n                }\r\n            }\r\n            &.active {\r\n                span {\r\n                    border-color: #D3BBF3;\r\n                }\r\n            }\r\n        }\r\n        &.color-extra-05 {\r\n            span {\r\n                .color {\r\n                    background: #E8A2A5;\r\n                }\r\n            }\r\n            &.active {\r\n                span {\r\n                    border-color: #E8A2A5;\r\n                }\r\n            }\r\n        }\r\n        &.color-extra-06 {\r\n            span {\r\n                .color {\r\n                    background: #C3A03B;\r\n                }\r\n            }\r\n            &.active {\r\n                span {\r\n                    border-color: #C3A03B;\r\n                }\r\n            }\r\n        }\r\n        &.color-extra-07 {\r\n            span {\r\n                .color {\r\n                    background: #DFBF9B;\r\n                }\r\n            }\r\n            &.active {\r\n                span {\r\n                    border-color: #DFBF9B;\r\n                }\r\n            }\r\n        }\r\n        &.color-extra-08 {\r\n            span {\r\n                .color {\r\n                    background: #BADEFF;\r\n                }\r\n            }\r\n            &.active {\r\n                span {\r\n                    border-color: #BADEFF;\r\n                }\r\n            }\r\n        }\r\n        &.color-extra-09 {\r\n            span {\r\n                .color {\r\n                    background: #DBDEFF;\r\n                }\r\n            }\r\n            &.active {\r\n                span {\r\n                    border-color: #DBDEFF;\r\n                }\r\n            }\r\n        }\r\n        &.color-extra-10 {\r\n            span {\r\n                .color {\r\n                    background: #DBF8FF;\r\n                }\r\n            }\r\n            &.active {\r\n                span {\r\n                    border-color: #DBF8FF;\r\n                }\r\n            }\r\n        }\r\n        &.color-extra-11 {\r\n            span {\r\n                .color {\r\n                    background: #FFEDDC;\r\n                }\r\n            }\r\n            &.active {\r\n                span {\r\n                    border-color: #FFEDDC;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.range-variant {\r\n    display: flex;\r\n    align-items: center;\r\n    flex-wrap: wrap;\r\n    margin: -5px;\r\n    padding-left: 0;\r\n    li {\r\n        border: 2px solid #F6F7FB;\r\n        background: #fff;\r\n        padding: 5px 13px;\r\n        border-radius: 30px;\r\n        min-width: 44px;\r\n        min-height: 44px;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        text-transform: uppercase;\r\n        font-weight: 500;\r\n        line-height: 24px;\r\n        margin: 5px;\r\n        cursor: pointer;\r\n        transition: 0.3s;\r\n        @media #{$sm-layout} {\r\n            font-size: 15px;\r\n        }\r\n        &.active {\r\n            border-color: #656973;\r\n        }\r\n        &:hover {\r\n            border-color: #656973;\r\n        }\r\n    }\r\n}\r\n\r\n.axil-product-list {\r\n    border: 1px solid #f1f1f1;\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 30px;\r\n    border-radius: 6px;\r\n    margin-bottom: 30px;\r\n    transition: var(--transition);\r\n    @media #{$sm-layout} {\r\n       padding: 30px;\r\n    }\r\n    @media #{$large-mobile} {\r\n        display: block;\r\n        text-align: center;\r\n    }\r\n    .thumbnail {\r\n        margin-right: 30px;\r\n        @media #{$large-mobile} {\r\n            margin-right: 0;\r\n            margin-bottom: 20px;\r\n        }\r\n        a {\r\n            background-color: #f7f7f7;\r\n            border-radius: 6px;\r\n            display: block;\r\n            transition: var(--transition);\r\n            overflow: hidden;\r\n        }\r\n        img {\r\n            border-radius: 6px;\r\n            transition: var(--transition);\r\n        }\r\n    }\r\n    .product-content {\r\n        flex: 1;\r\n        position: relative;\r\n        padding-right: 60px;\r\n        @media #{$large-mobile} {\r\n            margin: 0;\r\n            padding: 0;\r\n        }\r\n        .product-title {\r\n            margin-bottom: 10px;\r\n            font-size: 16px;\r\n            color: var(--color-body);\r\n            a {\r\n                 transition: var(--transition);\r\n            }\r\n        }\r\n        .product-rating {\r\n            display: flex;\r\n            align-items: center;\r\n            margin-bottom: 8px;\r\n            font-size: 12px;\r\n            @media #{$large-mobile} {\r\n                justify-content: center;\r\n            }\r\n            .rating-number {\r\n                font-weight: 500;\r\n                margin-left: 10px;\r\n                display: inline-block;\r\n                span {\r\n                    font-weight: 700;\r\n                    color: var(--color-heading);\r\n                }\r\n            }\r\n            .rating-icon {\r\n                color: #ffa800;\r\n            }\r\n        }\r\n        .product-price-variant {\r\n            font-size: 20px;\r\n            font-weight: 700;\r\n            color: var(--color-heading);\r\n            .price {\r\n                &.old-price {\r\n                    color: #d6d6d6;\r\n                    text-decoration: line-through;\r\n                    margin-left: 10px;\r\n                }\r\n            }\r\n        }\r\n        .product-cart {\r\n            position: absolute;\r\n            top: 50%;\r\n            right: 0;\r\n            transform: translateY(-50%);\r\n            @media #{$large-mobile} {\r\n                position: inherit;\r\n                transform: translateY(0);\r\n                margin-top: 20px;\r\n            }\r\n            .cart-btn {\r\n                text-align: center;\r\n                display: block;\r\n                height: 40px;\r\n                width: 40px;\r\n                line-height: 40px;\r\n                border: 1px solid #efefef;\r\n                border-radius: 6px;\r\n                color: var(--color-heading);\r\n                font-size: 14px;\r\n                font-weight: 500;\r\n                transition: var(--transition);\r\n                margin-bottom: 10px;\r\n                @media #{$large-mobile} {\r\n                    margin: 0 5px;\r\n                    display: inline-block;\r\n                }\r\n                &:hover {\r\n                    background: var(--color-primary);\r\n                    border-color: var(--color-primary);\r\n                    color: var(--color-white);\r\n                }\r\n            }\r\n        }\r\n    }\r\n    &:hover {\r\n        border-color: var(--color-white);\r\n        box-shadow: var(--shadow-dark);\r\n        .thumbnail {\r\n            img {\r\n                transform: scale(1.1);\r\n            }\r\n        }\r\n    }\r\n    &.product-list-style-2 {\r\n        padding: 20px;\r\n        @media #{$large-mobile} {\r\n            padding: 30px;\r\n        }\r\n        .thumbnail {\r\n            margin-right: 20px;\r\n            max-width: 120px;\r\n            overflow: hidden;\r\n            border-radius: 6px;\r\n            @media #{$large-mobile} {\r\n                margin: 0 auto 20px;\r\n            }\r\n            img {\r\n                transition: .5s;\r\n            }\r\n        }\r\n        .product-content {\r\n            padding: 0;\r\n        }\r\n        .product-cart {\r\n            position: initial;\r\n            transform: translateY(0);\r\n            margin-top: 10px;\r\n            .cart-btn {\r\n                height: auto;\r\n                width: auto;\r\n                line-height: 1;\r\n                display: inline-block;\r\n                padding: 10px 15px;\r\n            }\r\n        }\r\n        &:hover {\r\n            img {\r\n               transform: scale(1.15);\r\n            } \r\n        }\r\n    }\r\n}\r\n\r\n.verified-icon {\r\n    color: #2081e2; \r\n    font-size: 14px;\r\n    padding-left: 2px; \r\n         \r\n}\r\n\r\n.product-transparent-layout {\r\n    .slick-list {\r\n        margin-top: -20px;\r\n    }\r\n    .slick-single-layout {\r\n        margin-top: 20px;\r\n    }\r\n}\r\n\r\n/* End Axil Single Product  */\r\n\r\n.product_list_widget {\r\n    list-style: none outside;\r\n    padding: 0;\r\n    margin: 0;\r\n    li {\r\n        display: flex;\r\n        align-items: center;\r\n        margin-bottom: 20px;\r\n        border-bottom: 1px solid #f3f3f3;\r\n        padding-bottom: 20px;\r\n        &:last-child {\r\n            margin-bottom: 0;\r\n            border-bottom: none;\r\n            padding-bottom: 0;\r\n        }\r\n        a {\r\n            display: block;\r\n        }\r\n        .thumbnail {\r\n            width: 120px;\r\n            margin-right: 20px;\r\n            min-width: 120px;\r\n            overflow: hidden;\r\n            margin-bottom: 0;\r\n            border-radius: 6px;\r\n            @media #{$small-mobile} {\r\n                width: 90px;\r\n                min-width: 90px;\r\n            }\r\n            a {\r\n                overflow: hidden;\r\n            }\r\n            img {\r\n                border-radius: 6px;\r\n                transition: .3s;\r\n            }\r\n        }\r\n        .title {\r\n            margin-bottom: 10px;\r\n            font-weight: 500;\r\n            font-size: 17px;\r\n            @media #{$small-mobile} {\r\n                font-size: 15px;\r\n            }\r\n        }\r\n        .content {\r\n            flex: 1;\r\n        }\r\n        .woocommerce-Price-amount.amount {\r\n            font-size: 17px;\r\n            line-height: 28px;\r\n            color: var(--color-heading);\r\n            font-weight: 500;\r\n            @media #{$sm-layout} {\r\n                font-size: 18px;\r\n            }\r\n            del {\r\n                padding-right: 8px;\r\n                color: #d6d6d6;\r\n            }\r\n        }\r\n        &:hover {\r\n            .thumbnail {\r\n                img {\r\n                    transform: scale(1.1);\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.btn-load-more {\r\n    min-width: 250px;\r\n}\r\n\r\n.filter-results {\r\n    font-size: 16px;\r\n    color: #000;\r\n    font-weight: 500;\r\n    margin: 0 20px 0 10px;\r\n    @media #{$sm-layout} {\r\n       margin: 10px;\r\n    }\r\n}\r\n\r\n.product-filter-mobile {\r\n    position: relative;\r\n    width: auto;\r\n    margin-top: 10px;\r\n    padding: 0;\r\n    border-radius: 6px;\r\n    font-size: var(--font-size-b2);\r\n    color: var(--color-dark);\r\n    font-weight: 500;\r\n    background-color: transparent;\r\n    &:after {\r\n        content: \"\";\r\n        height: 1px;\r\n        width: 100%;\r\n        background-color: var(--color-primary);\r\n        position: absolute;\r\n        bottom: 0;\r\n        left: 0;\r\n    }\r\n    i {\r\n        margin-right: 6px;\r\n    }\r\n    @media #{$sm-layout} {\r\n        margin-top: 20px;\r\n    }\r\n    &:hover {\r\n        color: var(--color-primary);\r\n    }\r\n}\r\n\r\n.axil-shop-sidebar {\r\n    padding-right: 20px;\r\n    position: relative;\r\n    @media only screen and (max-width: 991px) {\r\n        padding-right: 0;\r\n        position: fixed;\r\n        top: 0;\r\n        bottom: 0;\r\n        left: -300px;  \r\n        width: 280px;\r\n        background-color: var(--color-white);\r\n        z-index: 100;\r\n        padding: 100px 20px 50px;\r\n        overflow-y: auto;\r\n        transition: all 0.4s ease-in-out;\r\n\r\n    }\r\n    .toggle-list {\r\n        position: relative;\r\n        padding-bottom: 40px;\r\n        ul {\r\n            @extend %liststyle;\r\n        }\r\n        &.active {\r\n            .title {\r\n                &::before {\r\n                    content: \"\\f068\";\r\n                }\r\n                &::after {\r\n                    width: 100%;\r\n                }\r\n            }\r\n        }\r\n    }\r\n    \r\n    .title {\r\n        font-size: 18px;\r\n        font-weight: 500;\r\n        color: var(--color-dark);\r\n        border-bottom: 2px solid var(--color-light);\r\n        padding-bottom: 10px;\r\n        margin-bottom: 20px;\r\n        cursor: pointer;\r\n        position: relative;\r\n        &::before {\r\n            content: \"\\f067\";\r\n            font-family: \"Font Awesome 5 Pro\";\r\n            position: absolute;\r\n            top: 0;\r\n            right: 0;\r\n            transition: 0.3s;\r\n            pointer-events: none;\r\n        }\r\n        &::after {\r\n            content: \"\";\r\n            position: absolute;\r\n            bottom: -2px;\r\n            left: 0;\r\n            width: 0;\r\n            height: 2px;\r\n            background: var(--color-primary);\r\n            transition: 0.3s;\r\n        }\r\n    }\r\n\r\n    .product-categories {\r\n        ul {\r\n            margin: -5px 0;\r\n            li {\r\n                margin: 0;\r\n                font-size: var(--font-size-b2);\r\n                font-weight: var(--s-medium);\r\n                padding: 6px 0;\r\n                a {\r\n                    position: relative;\r\n                    padding-left: 28px;\r\n                    color: var(--color-body);\r\n                    &::before {\r\n                        content: \"\";\r\n                        height: 16px;\r\n                        width: 16px;\r\n                        line-height: 15px;\r\n                        text-align: center;\r\n                        border: 1px solid var(--color-body);\r\n                        border-radius: 50%;\r\n                        position: absolute;\r\n                        top: 3px;\r\n                        left: 0;\r\n                        transition: var(--transition);\r\n                    }\r\n                }\r\n                &.current-cat,\r\n                &.chosen {\r\n                    a {\r\n                        &::before {\r\n                            content: \"\\f00c\";\r\n                            font-family: var(--font-awesome);\r\n                            font-size: 8px;\r\n                            font-weight: 700;\r\n                            color: var(--color-white);\r\n                            background: var(--color-primary);\r\n                            border-color: var(--color-primary);\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    .product-color {\r\n        ul {\r\n            display: flex;\r\n            align-items: center;\r\n            flex-wrap: wrap;\r\n        }\r\n        li {\r\n            margin: 0 8px 8px 0;\r\n            height: 30px;\r\n            width: 30px;\r\n            line-height: 32px;\r\n            border-radius: 50%;\r\n            text-align: center;\r\n            &.chosen {\r\n                border: 2px solid #906145;  \r\n            }\r\n            a {\r\n                display: inline-block;\r\n                height: 16px;\r\n                width: 16px;\r\n                border-radius: 50%;\r\n                &.color-extra-01 {\r\n                    background: #906145;\r\n                }\r\n                &.color-extra-02 {\r\n                    background: #FAB8C4;\r\n                }\r\n                &.color-extra-03 {\r\n                    background: #FFDC60;\r\n                }\r\n                &.color-extra-04 {\r\n                    background: #896BA7;\r\n                }\r\n                &.color-extra-05 {\r\n                    background: #DBDEFF;\r\n                }\r\n                &.color-extra-06 {\r\n                    background: #BADEFF;\r\n                }\r\n                &.color-extra-07 {\r\n                    background: #DFBF9B;\r\n                }\r\n                &.color-extra-08 {\r\n                    background: #BADEFF;\r\n                }\r\n                &.color-extra-09 {\r\n                    background: #DBDEFF;\r\n                }\r\n                &.color-extra-10 {\r\n                    background: #DBF8FF;\r\n                }\r\n                &.color-extra-11 {\r\n                    background: #FFEDDC;\r\n                }\r\n            }\r\n        }\r\n    } \r\n\r\n    .product-size {\r\n        li {\r\n            display: inline-block;\r\n            margin: 0 5px 10px 0;\r\n            a {\r\n                border: 2px solid #F6F7FB;\r\n                background: #fff;\r\n                padding: 5px 13px;\r\n                border-radius: 30px;\r\n                min-width: 44px;\r\n                min-height: 44px;\r\n                display: flex;\r\n                align-items: center;\r\n                justify-content: center;\r\n                text-transform: uppercase;\r\n                font-weight: 500;\r\n                font-size: var(--font-size-b2);\r\n                color: var(--color-body);\r\n            }\r\n            &.chosen {\r\n                a {\r\n                    border-color: var(--color-primary);\r\n                    background-color: var(--color-primary);\r\n                    color: var(--color-white);\r\n                }\r\n            }\r\n        }\r\n    }\r\n    .product-price-range {\r\n        li {\r\n            display: inline-block;\r\n            margin: 0 15px 0 0;\r\n            a {\r\n                border: 2px solid #F6F7FB;\r\n                background: #fff;\r\n                padding: 5px 13px;\r\n                border-radius: 30px;\r\n                min-width: 50px;\r\n                min-height: 44px;\r\n                display: flex;\r\n                align-items: center;\r\n                justify-content: center;\r\n                font-weight: 500;\r\n                font-size: var(--font-size-b2);\r\n                color: var(--color-body);\r\n            }\r\n            &.chosen {\r\n                a {\r\n                    border-color: var(--color-body);\r\n                }\r\n            }\r\n        }\r\n        .input-range,\r\n        .amount-range{\r\n            color: var(--color-heading);\r\n            font-size: var(--font-size-b2);\r\n        }\r\n    }\r\n    .axil-btn.btn-outline {\r\n        width: auto;\r\n        color: var(--color-body);\r\n    }\r\n\r\n    .filter-close-btn {\r\n        position: absolute;\r\n        top: 15px;\r\n        left: 20px;\r\n        height: 30px;\r\n        width: 30px;\r\n        background-color: var(--color-lighter);\r\n        border-radius: 10px;\r\n        color: var(--color-dark);\r\n        font-size: 14px;\r\n\r\n    }\r\n    &.open {\r\n        left: 0;\r\n    }\r\n}\r\n\r\n.product-area {\r\n    border-bottom: 2px solid var(--color-lighter);\r\n    &.pb--80 {\r\n        @media #{$sm-layout} {\r\n           padding-bottom: 60px !important; \r\n        }\r\n    }\r\n    &.pb--50 {\r\n        @media #{$sm-layout} {\r\n           padding-bottom: 30px !important; \r\n        }\r\n    }\r\n}\r\n\r\n\r\n.axil-new-arrivals-product-area {\r\n    &.fullwidth-container {\r\n        margin-left: calc((100% - 1320px) / 2);\r\n        overflow: hidden;\r\n        @media only screen and (max-width: 1349px) {\r\n           margin-left: auto;   \r\n        }\r\n        .slick-list {\r\n            overflow: visible;\r\n            @media only screen and (max-width: 1349px) {\r\n                overflow: hidden;   \r\n            }\r\n        }\r\n    }\r\n    &.flash-sale-area {\r\n        .arrow-top-slide {\r\n            .slide-arrow {\r\n                @media #{$sm-layout} {\r\n                    top: -180px;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n// Product Carousel Mobile Style\r\n@media #{$large-mobile} {\r\n    .product-slide-mobile {\r\n        .axil-product {\r\n            text-align: left;\r\n            display: inline-block;\r\n            width: 270px;\r\n            .product-content {\r\n                .product-price-variant {\r\n                    justify-content: flex-start;\r\n                }\r\n                .color-variant {\r\n                    justify-content: flex-start;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n\r\n// Product Isotop Style\r\n.product-isotope-heading {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    @media #{$smlg-device} {\r\n      flex-direction: column;\r\n      align-items: flex-start;\r\n      margin-bottom: 40px;\r\n    }\r\n    .section-title-wrapper {\r\n        @media #{$smlg-device} {\r\n           margin-bottom: 30px;\r\n           padding-right: 0;\r\n        }\r\n        .title {\r\n            margin-bottom: 0;\r\n        }\r\n    }\r\n\r\n}\r\n\r\n.isotope-button {\r\n    display: flex;\r\n    align-items: center;\r\n    flex-wrap: wrap;\r\n    margin: -5px;\r\n    button {\r\n        margin: 5px;\r\n        padding: 10px 15px;\r\n        border-radius: 6px;\r\n        font-size: 15px;\r\n        font-weight: 500;\r\n        color: var(--color-heading);\r\n        background-color: transparent;\r\n        position: relative;\r\n        z-index: 1;\r\n        transition: .3s;\r\n        width: auto;\r\n        &:after {\r\n            content: \"\";\r\n            transform: scale(.7) perspective(1px);\r\n            position: absolute;\r\n            top: 0;\r\n            left: 0;\r\n            display: block;\r\n            height: 100%;\r\n            width: 100%;\r\n            opacity: 0;\r\n            transition: .3s;\r\n            background-color: var(--color-lighter);\r\n            border-radius: 6px;\r\n            z-index: -1;\r\n        }\r\n        &:hover {\r\n            &:after {\r\n                transform: scale(1.035) perspective(1px);\r\n                opacity: 1;\r\n            }\r\n        }\r\n        &.is-checked {\r\n            color: var(--color-white);\r\n            &:after {\r\n                transform: scale(1.035) perspective(1px);\r\n                opacity: 1;\r\n                background-color: var(--color-primary);\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n// Product Collection Style\r\n.product-collection {\r\n    position: relative;\r\n    margin-bottom: 30px;\r\n    .collection-content {\r\n        position: absolute;\r\n        left: 30px;\r\n        top: 50%;\r\n        transform: translateY(-50%);\r\n        right: 0;\r\n        z-index: 1;\r\n        .title {\r\n            font-size: 32px;\r\n            margin-bottom: 16px;\r\n            line-height: 1.2;\r\n        }\r\n        .price-warp {\r\n            margin-bottom: 30px;\r\n            .price-text {\r\n                font-size: 18px;\r\n                font-weight: 500;\r\n                color: var(--color-heading);\r\n                display: block;\r\n                margin-bottom: 8px;\r\n            }\r\n            .price {\r\n                font-size: 32px;\r\n                line-height: 1.2;\r\n                font-weight: 700;\r\n                color: var(--color-secondary);\r\n            }\r\n        }\r\n        .plus-btn {\r\n            position: absolute;\r\n            left: 38%;\r\n            top: 47%;\r\n            @media (max-width: 767px) {\r\n                left: 55%;\r\n            }\r\n            .plus-icon {\r\n                font-size: 20px;\r\n                color: var(--color-heading);\r\n                border: 2px solid var(--color-heading);\r\n                background-color: #ECF3FF;\r\n                border-radius: 50%;\r\n                height: 50px;\r\n                width: 50px;\r\n                line-height: 48px;\r\n                text-align: center;\r\n                display: block;\r\n                transition: 0.3s;\r\n                &:hover {\r\n                    color: var(--color-primary);\r\n                    border-color: var(--color-primary);\r\n                }\r\n            }\r\n        }\r\n    }\r\n    .label-block {\r\n        position: absolute;\r\n        z-index: 2;\r\n        &.label-right {\r\n            left: auto;\r\n            right: 20px;\r\n            top: 20px;\r\n        }\r\n        .product-badget {\r\n            line-height: 1;\r\n            font-size: 12px;\r\n            font-weight: 500;\r\n            color: #FFFFFF;\r\n            border-radius: 4px;\r\n            background-color: var(--color-heading);\r\n            padding: 8px 10px;\r\n            box-shadow: none;\r\n            text-transform: uppercase;\r\n        }\r\n    }\r\n    .collection-thumbnail {\r\n        position: relative;\r\n        img {\r\n            border-radius: 8px;\r\n            width: 100%;\r\n            @media (max-width: 991px) {\r\n                height: 370px;\r\n                object-fit: cover;\r\n                object-position: left;\r\n            }\r\n        }\r\n    }\r\n    &.product-collection-two {\r\n        .collection-content {\r\n            left: 50px;\r\n            .title {\r\n                margin-bottom: 20px;\r\n            }\r\n            .price-warp {\r\n                margin-bottom: 50px;\r\n                .price-text {\r\n                    margin-bottom: 0;\r\n                }\r\n            }\r\n            .plus-btn {\r\n                left: 40%;\r\n                top: 30%;\r\n                .plus-icon {\r\n                    background-color: var(--color-white);\r\n                    &:hover {\r\n                        background-color: var(--color-primary);\r\n                        color: var(--color-white);\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n\r\n.product-collection-three {\r\n    display: flex;\r\n    background-color: var(--color-white);\r\n    border-radius: 8px;\r\n    padding: 30px 35px 0 20px;\r\n    margin-bottom: 30px;\r\n    min-height: 185px;\r\n    .collection-content {\r\n        flex: 1;\r\n        .title {\r\n            font-weight: 700;\r\n            a {\r\n                transition: 0.3s;\r\n            }\r\n        }\r\n        .price-warp {\r\n            .price-text {\r\n                font-size: 14px;\r\n                font-weight: 500;\r\n                display: block;\r\n            }\r\n            .price {\r\n                line-height: 1.2;\r\n                font-weight: 700;\r\n                color: var(--color-secondary);\r\n            }\r\n        }\r\n    }\r\n    .collection-thumbnail {\r\n        position: relative;\r\n        z-index: 1;\r\n        width: 70px;\r\n        &:before {\r\n            content: \"\";\r\n            height: 118px;\r\n            width: 118px;\r\n            background-color: var(--color-lighter);\r\n            border-radius: 50%;\r\n            position: absolute;\r\n            bottom: 10px;\r\n            right: -23px;\r\n            z-index: -1;\r\n        }\r\n    }\r\n}\r\n\r\n", "/*----------------------------\r\n    Product Details Styles  \r\n----------------------------*/\r\n.single-product-thumbnail {\r\n    &.thumbnail-badge {\r\n        .thumbnail {\r\n            padding-right: 45px;\r\n            position: relative;\r\n            @media #{$large-mobile} {\r\n                padding-right: 20px;\r\n            }                \r\n            .label-block {\r\n                position: absolute;\r\n                top: 30px;\r\n                &.label-right {\r\n                    right: 0;\r\n                }\r\n                .product-badget {\r\n                    background-color: var(--color-primary);\r\n                    line-height: 1;\r\n                    padding: 6px 10px 5px;\r\n                    font-size: 12px;\r\n                    font-weight: 700;\r\n                    color: #FFFFFF;\r\n                    border-radius: 4px;\r\n                    \r\n                }\r\n            } \r\n        }\r\n    }\r\n    .thumbnail {\r\n        img {\r\n            width: 100%;\r\n            border-radius: 6px;\r\n        }\r\n    }\r\n    &:hover {\r\n        .thumbnail {\r\n            a {\r\n                img {\r\n                    transform: scale(1);\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n.single-product-content {\r\n    .inner {\r\n        // Product Rating \r\n        .product-rating {\r\n            display: flex;\r\n            align-items: center;\r\n            margin-bottom: 20px;\r\n            line-height: 1;\r\n            padding-bottom: 20px;\r\n            border-bottom: 2px solid #F6F7FB;\r\n            .star-rating {\r\n                margin-right: 8px; \r\n                font-size: 14px;\r\n                color: #FFDC60;\r\n            }\r\n            .review-link {\r\n                a {\r\n                    font-size: 16px;\r\n                    line-height: 24px;\r\n                    color: var(--color-body);\r\n                    transition: var(--transition);\r\n                    &:hover {\r\n                        color: var(--color-heading);\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        .product-title {\r\n            margin-bottom: 18px;\r\n            color: var(--color-dark);\r\n        }\r\n        .price-amount {\r\n            font-weight: 500;\r\n            font-size: 24px;\r\n            font-family: var(--font-secondary);\r\n            display: block;\r\n            margin-bottom: 20px;\r\n            color: var(--color-black);\r\n            @media #{$sm-layout} {\r\n                font-size: 20px;\r\n            }\r\n            &.price-offer-amount {\r\n                display: flex;\r\n                align-items: center;\r\n                margin: 0 -10px 20px;\r\n                @media #{$small-mobile} {\r\n                    margin: 0 -4px 20px;\r\n                }\r\n                span {\r\n                    display: inline-block;\r\n                    margin: 0 10px;\r\n                    @media #{$small-mobile} {\r\n                        margin: 0 4px;\r\n                    }\r\n                }\r\n                .old-price {\r\n                    color: var(--color-body);\r\n                    text-decoration: line-through;\r\n                }\r\n                .offer-badge {\r\n                    background-color: var(--color-chart03);\r\n                    height: 48px;\r\n                    line-height: 40px;\r\n                    padding: 5px 20px;\r\n                    font-size: 16px;\r\n                    color: var(--color-white);\r\n                    border-radius: 24px;\r\n                    font-family: var(--font-secondary);\r\n                }\r\n            }\r\n        }\r\n        .product-meta {\r\n            @extend %liststyle;\r\n            margin-bottom: 20px;\r\n            li {\r\n                color: var(--color-primary);\r\n                font-weight: 500;\r\n                font-size: 16px;\r\n                line-height: 24px;\r\n                display: flex;\r\n                align-items: center;\r\n                margin: 0;\r\n                i {\r\n                    padding-right: 15px;\r\n                    font-size: 18px;\r\n                }\r\n            }\r\n        }\r\n        .description {\r\n            margin-bottom: 30px;\r\n        }\r\n        .product-variation {\r\n            margin-bottom: 30px;\r\n            display: flex;\r\n            align-items: center;\r\n            .title {\r\n                font-weight: 500;\r\n                font-size: 20px;\r\n                margin-bottom: 0;\r\n                min-width: 114px;\r\n                @media #{$sm-layout} {\r\n                    font-size: 18px;\r\n                    min-width: 90px;\r\n                    \r\n                }\r\n            }\r\n            .color-variant {\r\n                margin: -5px !important;\r\n                li {\r\n                    margin: 5px;\r\n                    >span {\r\n                        .color {\r\n                            width: 11px;\r\n                            height: 11px;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n            .variable-items-wrapper {\r\n                display: flex;\r\n                align-items: center;\r\n                @extend %liststyle;\r\n                &.color-variable-wrapper {\r\n                    li {\r\n                        &.color-variable-item {\r\n                            .variable-item-span {\r\n                                padding: 5px;\r\n                                border: 1px solid transparent;\r\n                                display: block;\r\n                                border-radius: 100%;\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n            &.product-size-variation {\r\n                align-items: flex-start;\r\n                .title {\r\n                    margin-top: 8px;\r\n                }\r\n\r\n            }\r\n        }\r\n        .product-features {\r\n            margin-bottom: 20px;\r\n            tr {\r\n                &:first-child {\r\n                    td {\r\n                        border-top: 1px solid var(--color-light);\r\n                    }\r\n                }\r\n                td {\r\n                    border-bottom: 1px solid var(--color-light);\r\n                    padding: 15px 20px;\r\n                    @media #{$small-mobile} {\r\n                        padding: 15px;\r\n                    }\r\n                    &:first-child {\r\n                        padding-left: 0;\r\n                    }\r\n                    &:last-child {\r\n                        padding-right: 0;\r\n                        text-align: right;\r\n                    }\r\n                }\r\n            }\r\n            .pro-qty {\r\n                .qtybtn {\r\n                    font-size: 20px;\r\n                }\r\n            }\r\n            .title {\r\n                margin-bottom: 0;\r\n                color: var(--color-black);\r\n                @media #{$small-mobile} {\r\n                    font-size: 17px;\r\n                }\r\n            }\r\n            .price-amount {\r\n                margin: 0;\r\n                padding: 0;\r\n                border: none;\r\n                color: var(--color-body);\r\n            }\r\n            .mini-btn {\r\n                display: inline-block;\r\n                width: auto;\r\n                font-size: var(--font-size-b3);\r\n                color: var(--color-white);\r\n                font-weight: var(--p-medium);\r\n                background-color: var(--color-body);\r\n                border-radius: 20px;\r\n                padding: 5px 14px;\r\n                min-width: 100px;\r\n                text-align: center;\r\n            }\r\n        }\r\n        .nft-short-meta {\r\n            border-bottom: 1px solid var(--color-border-light);\r\n            padding-bottom: 30px;\r\n            margin-bottom: 30px;\r\n            margin-top: 30px;\r\n        }\r\n        .nft-category, \r\n        .nft-verified-option {\r\n            display: flex;\r\n            align-items: center;\r\n            label {\r\n                font-size: 15px;\r\n                display: block;\r\n                margin-right: 10px;\r\n            }\r\n            .category-list {\r\n                a {\r\n                    transition: .3s;\r\n                }\r\n            }\r\n        }\r\n        .nft-category {\r\n            label {\r\n                font-size: 20px;\r\n                font-weight: 500;\r\n                color: var(--color-heading);\r\n            }\r\n        }\r\n        .nft-verified-option {\r\n            justify-content: flex-end;\r\n            @media #{$sm-layout} {\r\n                justify-content: flex-start; \r\n                margin-top: 20px;\r\n            }\r\n            .verify-btn {\r\n                width: auto;\r\n                padding: 12px 30px;\r\n            }\r\n        } \r\n    }\r\n    &.nft-single-product-content {\r\n        .inner {\r\n            .price-amount {\r\n                border-bottom: none;\r\n                &.price-offer-amount {\r\n                    padding-bottom: 10px;\r\n                }\r\n            }\r\n            .product-title {\r\n                margin-bottom: 20px;\r\n            }\r\n        }\r\n        .product-action-wrapper {\r\n            @media #{$small-mobile} {\r\n                flex-direction: row;\r\n            }\r\n            .product-action {\r\n                &.action-style-two {\r\n                    padding-right: 0;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.product-action-wrapper {\r\n    @media #{$small-mobile} {\r\n        flex-direction: column;\r\n        .pro-qty {\r\n            margin-bottom: 20px;\r\n        }\r\n    }\r\n    .product-action {\r\n        flex: 1;\r\n        .add-to-cart {\r\n            flex: 1;\r\n            .axil-btn {\r\n                width: 100%;\r\n                text-align: center;\r\n                display: block;\r\n                &:hover {\r\n                    &:before {\r\n                        transform: scale(1.05);\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        &.action-style-two {\r\n            margin: 0 -10px;\r\n            padding-right: 220px;\r\n            @media #{$lg-layout} {\r\n                padding-right: 0;\r\n            }\r\n            @media #{$large-mobile} {\r\n                padding-right: 0;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.product-action {\r\n    list-style: none;\r\n    padding: 0;\r\n    li {\r\n        margin: 0 10px;\r\n        .axil-btn {\r\n            @media #{$lg-layout} {\r\n                padding: 16px 20px;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.pro-qty {\r\n    width: 130px;\r\n    border-radius: 50px;\r\n    input {\r\n        width: 28px;\r\n        float: left;\r\n        border: none;\r\n        height: 32px;\r\n        line-height: 30px;\r\n        padding: 0;\r\n        text-align: center;\r\n        background-color: transparent;\r\n        font-size: 20px;\r\n        font-weight: 500;\r\n        margin: 0 12px;\r\n        color: #27272e;\r\n    }\r\n    .qtybtn {\r\n        width: 32px;\r\n        display: block;\r\n        float: left;\r\n        line-height: 26px;\r\n        cursor: pointer;\r\n        text-align: center;\r\n        font-size: 16px;\r\n        font-weight: 300;\r\n        color: #000;\r\n        height: 32px;\r\n        background: #F6F7FB;\r\n        border-radius: 50%;\r\n        transition: 0.3s;\r\n        border: 2px solid transparent;\r\n        &:hover {\r\n            border-color: var(--color-primary);\r\n        }\r\n    }\r\n    input::-webkit-outer-spin-button,\r\n    input::-webkit-inner-spin-button {\r\n      -webkit-appearance: none;\r\n      margin: 0;\r\n    }\r\n    input[type=number] {\r\n      -moz-appearance: textfield;\r\n    }\r\n}\r\n\r\n.product-quick-view {\r\n    a {\r\n        background: #fff;\r\n        width: 48px;\r\n        height: 48px;\r\n        display: flex !important;\r\n        align-items: center;\r\n        justify-content: center;\r\n        border-radius: 50%;\r\n        cursor: pointer;\r\n        transition: 0.3s;\r\n        &:hover {\r\n            background: var(--color-primary);\r\n            color: #fff;\r\n        }\r\n    }\r\n}\r\n\r\n.position-view {\r\n    position: absolute;\r\n    bottom: 47px;\r\n    right: 92px;\r\n    z-index: 4;\r\n    @media #{$large-mobile} {\r\n        bottom: 20px;\r\n        right: 40px;\r\n    }\r\n}\r\n\r\n.small-thumb-wrapper {\r\n    @media only screen and (max-width: 991px) {\r\n        margin-top: 10px; \r\n        .slick-list {\r\n            margin: 0 -10px;\r\n            .slick-slide {\r\n                margin: 10px;\r\n            }\r\n        }  \r\n    }\r\n    .small-thumb-img {\r\n        position: relative;\r\n        overflow: hidden;\r\n        border-radius: 10px;\r\n        margin-bottom: 20px;\r\n        cursor: pointer;\r\n        transition: all 0.4s;\r\n        img {\r\n            border-radius: 10px;\r\n            border: 2px solid transparent;\r\n            width: 80px;\r\n            height: auto;\r\n            transition: all 0.2s;\r\n        }\r\n        &:hover,\r\n        &.slick-current {\r\n            img {\r\n                border-color: var(--color-primary);\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.small-thumb-style-two {\r\n    .small-thumb-img {\r\n        img {\r\n            width: 60px;\r\n            height: auto;\r\n            border-radius: 50%;\r\n            display: inline-block;\r\n            @media #{$lg-layout} {\r\n                width: 50px;\r\n                height: 50px; \r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.single-product-thumb {\r\n    &.bg-vista-white {\r\n        .single-product-content {\r\n            .inner {\r\n                .product-rating {\r\n                    border-bottom-color: #e9e9e9;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.single-product-thumbnail-wrap {\r\n    position: relative;\r\n    .label-block {\r\n        position: absolute;\r\n        top: 30px;\r\n        right: 30px;\r\n        .product-badget {\r\n            background-color: var(--color-primary);\r\n            line-height: 1;\r\n            padding: 6px 10px 5px;\r\n            font-size: 12px;\r\n            font-weight: 700;\r\n            color: #FFFFFF;\r\n            border-radius: 4px;\r\n            box-shadow: 0 8px 16px 0 rgba(53, 119, 240, .30);\r\n            \r\n        }\r\n\r\n    }\r\n    .product-quick-view {\r\n        right: 30px;\r\n        bottom: 30px;\r\n    }\r\n}\r\n\r\n// product tabs\r\n.woocommerce-tabs {\r\n    &.wc-tabs-wrapper {\r\n        padding: 80px 0 35px;\r\n        @media #{$sm-layout} {\r\n            padding: 60px 0 15px;\r\n        }\r\n    }\r\n    ul.tabs {\r\n        margin: 0 -20px 60px;\r\n        @media #{$large-mobile} {\r\n            border-bottom: 1px solid #c7c7c7;\r\n            padding-bottom: 20px;\r\n        }\r\n        @media #{$large-mobile} {\r\n            flex-direction: column;\r\n            align-items: center;\r\n            margin: 0 0 60px;\r\n        }\r\n\r\n        li {\r\n            margin: 0 20px;\r\n            @media #{$large-mobile} {\r\n                margin: 10px 0;\r\n            }\r\n            a {\r\n                font-size: 24px;\r\n                line-height: 25px;\r\n                font-weight: 500;\r\n                display: block;\r\n                color: var(--color-body);\r\n                position: relative;\r\n                &:after {\r\n                    content: \"\";\r\n                    height: 2px;\r\n                    width: 0;\r\n                    background-color: var(--color-primary);\r\n                    position: absolute;\r\n                    bottom: -5px;\r\n                    right: 0;\r\n                    opacity: 0;\r\n                    transition: 0.5s;\r\n                }\r\n                &.active,\r\n                &:hover {\r\n                    color: var(--color-primary);\r\n                    &:after {\r\n                        width: 100%;\r\n                        left: 0;\r\n                        opacity: 1;\r\n                    }\r\n                }\r\n                @media #{$smlg-device} {\r\n                    font-size: 22px;\r\n                }\r\n                @media #{$sm-layout} {\r\n                    font-size: 20px;\r\n                }\r\n            }\r\n        }\r\n    }\r\n    &.nft-info-tabs {\r\n        padding: 30px 15px;\r\n        margin-top: 30px;\r\n        border-radius: 6px;\r\n        @media #{$large-mobile} {\r\n            padding: 30px 0;\r\n        }\r\n         ul.tabs {\r\n            margin: 0 -5px 10px;\r\n            @media #{$large-mobile} {\r\n                margin: 0 0 30px;\r\n            }\r\n             li {\r\n                margin: 0 5px;\r\n                @media #{$large-mobile} {\r\n                    margin: 5px 0;\r\n                }\r\n                 a {\r\n                    font-size: 18px;\r\n                    background-color: rgba(255,255,255, 0.7);\r\n                    border-radius: 6px;\r\n                    padding: 10px 25px;\r\n                    position: relative;\r\n                    @media only screen and (min-width: 1200px) and (max-width: 1399px) {\r\n                        font-size: 15px;\r\n                    }\r\n                    @media #{$lg-layout} {\r\n                        font-size: 15px;\r\n                        padding: 5px 11px;\r\n                    }\r\n                    @media #{$sm-layout} {\r\n                        font-size: 15px;\r\n                        padding: 5px 11px;\r\n                    }\r\n                    &:before {\r\n                        content: \". . .\";\r\n                        color: var(--color-border-light);\r\n                        position: absolute;\r\n                        bottom: -2px;\r\n                        left: 50%;\r\n                        transform: translateX(-50%);\r\n                        visibility: hidden;\r\n                        opacity: 0;\r\n                        transition: 0.3s;\r\n                        z-index: 1;\r\n                        line-height: 1;\r\n                    }\r\n                    &:after {\r\n                        content: \"\";\r\n                        height: 12px;\r\n                        width: 100%;\r\n                        background-color: var(--color-white);\r\n                        position: absolute;\r\n                        bottom: -12px;\r\n                        left: 0;\r\n                        right: 0;\r\n                        visibility: hidden;\r\n                        opacity: 0;\r\n                        transition: 0.3s;\r\n                        @media #{$large-mobile} {\r\n                            display: none;\r\n                        }\r\n                    }\r\n                    &.active {\r\n                        border-radius: 6px 6px 0 0;\r\n                        background-color: rgba(255,255,255, 1);\r\n                        @media #{$large-mobile} {\r\n                            border-radius: 6px;\r\n                        }\r\n                        &:before {\r\n                            visibility: visible;\r\n                            opacity: 1;\r\n                        }\r\n                        &:after {\r\n                            visibility: visible;\r\n                            opacity: 1;\r\n                        }\r\n                    }\r\n                 }\r\n             }\r\n        }\r\n        .tab-content {\r\n            background-color: var(--color-white);\r\n            padding: 30px;\r\n            border-radius: 6px;\r\n            @media #{$small-mobile} {\r\n                padding: 20px;\r\n            }\r\n        }\r\n        .product-additional-info {\r\n            padding: 0;\r\n            margin-bottom: 0;\r\n            table tbody tr th, table tbody tr td {\r\n                padding: 10px 20px 10px;\r\n                min-width: 120px;\r\n            }\r\n        }\r\n    }\r\n    &.wc-tab-style-two {\r\n        padding: 80px 0 50px;\r\n        @media #{$sm-layout} {\r\n            padding: 60px 0 30px;\r\n        }\r\n        .tabs-wrap {\r\n            background-color: var(--color-white);\r\n            border-radius: 8px;\r\n            padding: 30px;\r\n            margin-bottom: 30px;\r\n            ul.tabs {\r\n                border-bottom: 2px solid #EBEBEB;\r\n                margin: 0 0 30px 0;\r\n                li {\r\n                    margin: 0;\r\n                    margin-right: 20px;\r\n                    a {\r\n                        font-size: 20px;\r\n                        font-weight: 700;\r\n                        padding: 10px 15px;\r\n                        &:after {\r\n                            bottom: -2px;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n            .product-desc-wrapper {\r\n                .title {\r\n                    margin-bottom: 12px;\r\n                }\r\n                ul {\r\n                    list-style-type: disc;\r\n                    li {\r\n                        font-size: 18px;\r\n\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        .pro-des-commend-respond .form-group label {\r\n            background-color: var(--color-white);\r\n        }\r\n    }\r\n}\r\n\r\n.single-product-features {\r\n    .single-features {\r\n        background-color: var(--color-white);\r\n        margin-bottom: 30px; \r\n        display: flex;\r\n        align-items: center;\r\n        padding: 25px 30px;\r\n        border-radius: 8px;\r\n        .icon {\r\n            width: 77px;\r\n            height: 77px;\r\n            line-height: 77px;\r\n            margin-right: 16px;\r\n            font-size: 40px;\r\n            position: relative;\r\n            z-index: 1;\r\n            text-align: center;\r\n            color: var(--color-primary);\r\n            &:before {\r\n                content: \"\";\r\n                height: 100%;\r\n                width: 100%;\r\n                background-color: #F6F7FB;\r\n                border-radius: 50%;\r\n                position: absolute;\r\n                top: 0;\r\n                left: 0;\r\n                z-index: -1;\r\n\r\n            }\r\n            &.quality {\r\n                color: var(--color-secondary);\r\n            }\r\n            &.original {\r\n                color: var(--light-primary);\r\n            }\r\n        }\r\n        .content {\r\n            flex: 1;\r\n            .title {\r\n                margin-bottom: 5px;\r\n                font-weight: 700;\r\n            }\r\n            p {\r\n                font-size: 14px;\r\n\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.product-desc-wrapper {\r\n    .desc-heading {\r\n        @media #{$sm-layout} {\r\n            font-size: 24px;\r\n        }\r\n    }\r\n}\r\n\r\n.single-desc {\r\n    .title {\r\n        margin-bottom: 20px;\r\n    }\r\n}\r\n\r\n.pro-des-features {\r\n    padding: 0;\r\n    list-style: none;\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    margin-left: -30px;\r\n    margin-right: -30px;\r\n    li {\r\n        padding: 15px 30px;\r\n        margin: 0;\r\n        font-size: 20px;\r\n        font-weight: 500;\r\n        color: var(--color-dark);\r\n        @media #{$sm-layout} {\r\n            font-size: 20px;\r\n        }\r\n    }\r\n    .icon {\r\n        width: 60px;\r\n        height: 60px;\r\n        background: #fff;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        border-radius: 50%;\r\n        margin: 20px 0;\r\n        img {\r\n            max-width: 30px;\r\n        }\r\n    }\r\n}\r\n\r\n.pro-desc-style-two {\r\n    margin: 0 -15px;\r\n    li {\r\n        padding: 15px;\r\n    }\r\n}\r\n\r\n.product-additional-info {\r\n    background: #fff;\r\n    padding: 50px;\r\n    border-radius: 6px;\r\n    margin-bottom: 40px;\r\n    @media #{$sm-layout} {\r\n        padding: 20px 15px 0;\r\n    }\r\n    table {\r\n        margin-bottom: 0;\r\n        tbody {\r\n            tr {\r\n                &:nth-child(odd) {\r\n                    background: var(--color-lighter);\r\n                }\r\n                th,\r\n                td {\r\n                    font-size: 16px;\r\n                    line-height: 24px;\r\n                    font-weight: 400;\r\n                    padding: 17px 30px 18px;\r\n                    min-width: 200px;\r\n                    @media #{$sm-layout} {\r\n                        padding: 15px;\r\n                        font-size: 15px;\r\n                    }\r\n                }\r\n                th {\r\n                    text-transform: capitalize;\r\n                    color: #292930;\r\n                    font-weight: 500;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.pro-desc-commnet-area {\r\n    padding-right: 110px;\r\n    @media #{$smlg-device} {\r\n        padding-right: 30px;\r\n    }\r\n    @media only screen and (max-width: 991px) {\r\n      padding-right: 0;\r\n        \r\n    }\r\n    .comment-list {\r\n        .comment {\r\n            .commenter {\r\n                margin-bottom: 0;\r\n                display: flex;\r\n                align-items: center;\r\n                justify-content: space-between;\r\n                @media #{$large-mobile} {\r\n                    display: block;\r\n                }\r\n                .hover-flip-item-wrapper,\r\n                .commenter-rating {\r\n                    margin-bottom: 5px;\r\n                    a {\r\n                        font-size: 12px;\r\n                        i {\r\n                            color: #cecece;\r\n                            &:not(.empty-rating) {\r\n                                color: #ffca0f;\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n                .commenter-rating {\r\n                    margin-left: 15px;\r\n                    @media #{$small-mobile} {\r\n                        display: block;\r\n                        margin-bottom: 5px;\r\n                        margin-left: 0;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.pro-des-commend-respond {\r\n    .form-group {\r\n        textarea,\r\n        input {\r\n            background-color: transparent;\r\n        }\r\n        label {\r\n            background-color: #f9f3f0;\r\n        }\r\n        textarea {\r\n            padding-left: 30px;\r\n            padding-top: 20px;\r\n            font-size: 14px;\r\n            line-height: 1.5;\r\n        }\r\n    }\r\n}\r\n\r\n.small-thumb-wrapper {\r\n    .slick-track {\r\n        margin-left: 0;\r\n    }\r\n}  \r\n\r\n.small-thumb-style-three {\r\n    margin: 30px 60px 0;\r\n    @media #{$lg-layout} {\r\n        margin: 30px 0 0;\r\n    }\r\n    @media #{$sm-layout} {\r\n        margin: 30px 0 0;\r\n    }\r\n\r\n    .small-thumb-img {\r\n        margin: 10px;\r\n    }\r\n}\r\n\r\n\r\n\r\n/* Quick View Modal */\r\n.quick-view-product {\r\n    .modal-dialog {\r\n        max-width: 1100px;\r\n    }\r\n    .modal-content {\r\n        border: none;\r\n    }\r\n    .modal-header {\r\n        padding: 30px 15px;\r\n        justify-content: flex-end;\r\n        .btn-close {\r\n            width: auto;\r\n            background-image: none;\r\n            font-size: 14px;\r\n            padding: 0 10px;\r\n            transition: var(--transition);\r\n            position: relative;\r\n            right: 10px;\r\n            z-index: 1;\r\n            &:after {\r\n                content: \"\";\r\n                height: 35px;\r\n                width: 35px;\r\n                background: var(--color-primary);\r\n                border-radius: 50%;\r\n                position: absolute;\r\n                top: -9px;\r\n                left: -3px;\r\n                transform: scale(0);\r\n                z-index: -1;\r\n                transition: var(--transition);\r\n            }\r\n            &:hover {\r\n                color: var(--color-white);\r\n                &:after {\r\n                    transform: scale(1);\r\n                }\r\n            }\r\n        }\r\n    }\r\n    .modal-body {\r\n        padding: 30px;\r\n    }\r\n}\r\n\r\n.mfp-wrap {\r\n    z-index: 1060;\r\n}\r\n.mfp-bg {\r\n    z-index: 1055;\r\n}\r\n\r\n\r\n\r\n// New Style\r\n.single-product-modern {\r\n    .single-product-content {\r\n        .inner {\r\n            .price-amount {\r\n                font-weight: 700;\r\n                color: var(--color-primary);\r\n                margin-bottom: 10px;\r\n            }\r\n            .product-rating {\r\n                border-bottom: none;\r\n                padding-bottom: 0;\r\n                margin-bottom: 30px;\r\n                .star-rating {\r\n                    color: #FACC15;\r\n                }\r\n                .review-number {\r\n                    font-size: 14px;\r\n                    font-weight: 700;\r\n                    color: var(--color-heading);\r\n                }\r\n                .total-answerd {\r\n                    font-size: 14px;\r\n                    border-left: 1px solid #D6D6D6;\r\n                    padding-left: 8px;\r\n                    margin-left: 8px;\r\n                }\r\n            }\r\n            .description {\r\n                list-style: disc;\r\n                li {\r\n                    font-weight: 500;\r\n                }\r\n            }\r\n            .product-variation {\r\n                display: block;\r\n                margin-bottom: 20px;\r\n                .title {\r\n                    margin-bottom: 10px;\r\n                    font-size: 18px;\r\n                }\r\n            }\r\n            .range-variant {\r\n                li {\r\n                    border-radius: 4px;\r\n                    background-color: var(--color-lighter);\r\n                    border: none;\r\n                    width: 40px;\r\n                    height: 40px;\r\n                    font-size: 14px;\r\n                    font-weight: 700;\r\n                    &:hover, &.active {\r\n                        color: var(--color-white);\r\n                        background-color: var(--color-primary);\r\n                    }\r\n                }\r\n            }\r\n            .color-variant {\r\n                @media (max-width: 575px) {\r\n                    justify-content: flex-start;\r\n                }\r\n                li {\r\n                    >span {\r\n                        border: 1px solid;\r\n                        height: 24px;\r\n                        width: 24px;\r\n                        .color {\r\n                            height: 24px;\r\n                            width: 24px;\r\n                        }\r\n                    }\r\n                    &.active {\r\n                        >span {\r\n                            .color {\r\n                                width: 12px;\r\n                                height: 12px;\r\n                            }\r\n                        }\r\n                    }\r\n                    &.color-extra-01 {\r\n                        >span {\r\n                            border-color: #AAE6F8;\r\n                        }\r\n                    }\r\n                    &.color-extra-02 {\r\n                        >span {\r\n                            border-color: #5F8AF7;\r\n                        }\r\n                    }\r\n                    &.color-extra-03 {\r\n                        >span {\r\n                            border-color: #59C3C0;\r\n                        }\r\n                    }\r\n                    &.color-extra-04 {\r\n                        >span {\r\n                            border-color: #D3BBF3;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n            .quantity-variant-wrapper {\r\n                margin-bottom: 30px;\r\n                .pro-qty {\r\n                    min-height: 40px;\r\n                    width: auto;\r\n                    .qtybtn {\r\n                        width: 40px;\r\n                        height: 40px;\r\n                        line-height: 34px;\r\n                        border-radius: 8px;\r\n                        font-size: 20px;\r\n                    }\r\n                    input {\r\n                        border: 1px solid #D8D8D8;\r\n                        border-radius: 8px;\r\n                        height: 40px;\r\n                        width: 40px;\r\n                        font-size: 14px;\r\n                        margin: 0 10px;\r\n                    }\r\n                }\r\n            }\r\n\r\n            .product-action-wrapper {\r\n                .product-action {\r\n                    margin: -10px;\r\n                    width: 84%;\r\n                    @media (max-width: 1199px) {\r\n                        width: 100%;\r\n                    }\r\n                    @media (max-width: 575px) {\r\n                        display: block;\r\n                    }\r\n                    li {\r\n                        margin: 10px;\r\n                       a {\r\n                        padding: 12px 38px;\r\n                        i {\r\n                            font-size: 20px;\r\n                        }\r\n                       } \r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n    .single-product-thumbnail {\r\n        margin-bottom: 20px;\r\n        .thumbnail {\r\n            img {\r\n                border-radius: 8px;\r\n            }\r\n        }\r\n    }\r\n    .small-thumb-wrapper {\r\n        margin: 0 30px;\r\n        .small-thumb-img {\r\n            margin-bottom: 0;\r\n            border-radius: 8px;\r\n            img {\r\n                width: 100%;\r\n                border-radius: 8px;\r\n                border-width: 1px;\r\n            }\r\n        }\r\n        &.axil-slick-arrow {\r\n            .slide-arrow {\r\n                height: 40px;\r\n                width: 40px;\r\n                border-radius: 50%;\r\n                border: 2px solid var(--color-white);\r\n                font-size: 18px;\r\n                left: -25px;\r\n                &:before {\r\n                    border-radius: 50%;\r\n                }\r\n                &.next-arrow {\r\n                    right: -24px;\r\n                    left: auto;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}", "/*-----------------------\r\n    Checkout Styles  \r\n-------------------------*/\r\n.product-table-heading {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\t.title {\r\n\t\tmargin-bottom: 20px;\r\n\t\tfont-weight: 500;\r\n\t\tdisplay: inline-block;\r\n\t}\r\n\t.cart-clear {\r\n\t\tdisplay: inline-block;\r\n\t\tfont-size: 14px;\r\n\t\tcolor: var(--color-primary);\r\n\t\ttransition: var(--transition);\r\n\t\t&:hover {\r\n\t\t\tcolor: var(--color-black);\r\n\t\t}\r\n\t\t\r\n\t}\r\n}\r\n\r\n.axil-product-table {\r\n\tfont-family: var(--font-secondary);\r\n\tmargin: 0;\r\n\t@media #{$sm-layout} {\r\n\t\tmargin-top: 20px;\r\n\t}\r\n\tth, td {\r\n\t\t&:last-child {\r\n\t\t\ttext-align: right;\r\n\t\t\t@media #{$sm-layout} {\r\n\t\t\t\tborder-bottom: none;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\tthead {\r\n\t\tbackground-color: var(--color-lighter);\r\n\t\t@media #{$sm-layout} {\r\n\t\t\tdisplay: none;\r\n\t\t}\r\n\t\tth {\r\n\t\t\tfont-size: 20px;\r\n\t\t\ttext-transform: capitalize;\r\n\t\t\tborder: none;\r\n\t\t\tcolor: var(--color-heading);\r\n\t\t\tpadding: 18px 15px;\r\n\t\t\t@media only screen and (max-width: 991px) {\r\n\t\t\t\tfont-size: 18px;\r\n\t\t\t\tpadding: 18px 10px;\r\n\t\t\t}\r\n\t\t\t&:first-child {\r\n\t\t\t\tborder-radius: 6px 0 0 6px;\r\n\t\t\t}\r\n\t\t\t&:last-child {\r\n\t\t\t\tborder-radius: 0 6px 6px 0;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\ttbody {\r\n\t\tborder-top: none !important;\r\n\t\ttr {\r\n\t\t\t@media #{$sm-layout} {\r\n\t\t\t\tpadding-left: 120px;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\tdisplay: block;\r\n\t\t\t\tborder-bottom: 1px solid var(--color-lighter);\r\n\t\t\t\tmargin-bottom: 30px;\r\n\t\t\t\t&:last-child {\r\n\t\t\t\t\tmargin-bottom: 0;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t@media #{$large-mobile} {\r\n\t\t\t\tpadding-left: 90px;\r\n\t\t\t}\r\n\t\t}\r\n\t\ttd {\r\n\t\t\tborder-top: none;\r\n\t\t\tborder-bottom: 2px solid var(--color-lighter);\r\n\t\t\tvertical-align: middle;\r\n\t\t\tpadding: 15px;\r\n\t\t\tfont-size: 20px;\r\n\t\t\tfont-weight: 500;\r\n\t\t\tcolor: var(--color-body);\r\n\t\t\tmin-width: 150px;\r\n\t\t\t@media only screen and (max-width: 991px) {\r\n\t\t\t\tfont-size: 18px;\r\n\t\t\t\tpadding: 10px 10px;\r\n\t\t\t}\r\n\t\t\t@media #{$sm-layout} {\r\n\t\t\t\tfont-size: 16px;\r\n\t\t\t\tdisplay: block;\r\n\t\t\t\ttext-align: right;\r\n\t\t\t\tpadding: 10px 10px 10px 0;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\t&:before {\r\n\t\t\t\t\tcontent: attr(data-title) \" :\";\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\tleft: 0;\r\n\t\t\t\t\tfont-size: 13px;\r\n\t\t\t\t\tcolor: var(--color-black);\t\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t&.product-remove {\r\n\t\t\t\tmin-width: auto;\r\n\t\t\t\t@media #{$sm-layout} {\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\ttop: 0;\r\n\t\t\t\t\tright: 0;\r\n\t\t\t\t\tborder-bottom: none;\r\n\t\t\t\t\tz-index: 1;\r\n\t\t\t\t}\r\n\t\t\t\t.remove-wishlist {\r\n\t\t\t\t\tdisplay: block;\r\n\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\theight: 32px;\r\n\t\t\t\t\twidth: 32px;\r\n\t\t\t\t\tline-height: 30px;\r\n\t\t\t\t\tbackground-color: var(--color-lighter);\r\n\t\t\t\t\tborder: 2px solid var(--color-lighter);\r\n\t\t\t\t\tborder-radius: 50%;\r\n\t\t\t\t\tfont-size: 12px;\r\n\t\t\t\t\tcolor: var(--color-black);\r\n\t\t\t\t\ttransition: var(--transition);\r\n\t\t\t\t\t@media #{$sm-layout} {\r\n\t\t\t\t\t\theight: 25px;\r\n\t\t\t\t\t\twidth: 25px;\r\n\t\t\t\t\t\tline-height: 22px;\r\n\t\t\t\t\t\tfont-size: 10px;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t&:hover {\r\n\t\t\t\t\t\tborder-color: var(--color-primary);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t&:before {\r\n\t\t\t\t\tdisplay: none;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t&.product-thumbnail {\r\n\t\t\t\tmin-width: 130px;\r\n\t\t\t\twidth: 130px;\r\n\t\t\t\t@media #{$sm-layout} {\r\n\t\t\t\t\tmin-width: 80px;\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\tleft: 0;\r\n\t\t\t\t\ttop: 0;\r\n\t\t\t\t\tborder-bottom: none;\r\n\t\t\t\t\twidth: 100px;\r\n\t\t\t\t}\r\n\t\t\t\t@media #{$large-mobile} {\r\n\t\t\t\t\twidth: 80px;\r\n\t\t\t\t}\r\n\t\t\t\ta {\r\n\t\t\t\t\tborder-radius: 10px;\r\n\t\t\t\t\tdisplay: block;\r\n\t\t\t\t\timg {\r\n\t\t\t\t\t\tborder-radius: 10px;\r\n\t\t\t\t\t\theight: 80px;\r\n    \t\t\t\t\twidth: 80px;\r\n\t\t\t\t\t\tobject-fit: cover;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t&:before {\r\n\t\t\t\t\tdisplay: none;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t&.product-title {\r\n\t\t\t\twidth: 30%;\r\n\t\t\t\tcolor: var(--color-black);\r\n\t\t\t\t@media #{$sm-layout} {\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\ttext-align: left;\r\n\t\t\t\t\tpadding-right: 40px;\r\n\t\t\t\t}\r\n\t\t\t\ta {\r\n\t\t\t\t\ttransition: var(--transition);\r\n\t\t\t\t}\r\n\t\t\t\t&:before {\r\n\t\t\t\t\tdisplay: none;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t&.product-add-cart {\r\n\t\t\t\t.btn-outline {\r\n\t\t\t\t\tborder-color: #efefef;\r\n\t\t\t\t\tpadding: 10px 20px;\r\n\t\t\t\t\tfont-size: 14px;\r\n\t\t\t\t\t&:hover {\r\n\t\t\t\t\t\tborder-color: var(--color-primary);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t&:before {\r\n\t\t\t\t\tdisplay: none;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t&.axil-cart-table {\r\n\t\tth, td {\r\n\t\t\t&:first-child {\r\n\t\t\t\tpadding-left: 0;\r\n\t\t\t}\r\n\t\t\t&:last-child {\r\n\t\t\t\tpadding-right: 50px;\r\n\t\t\t\t@media #{$sm-layout} {\r\n\t\t\t\t\tpadding-right: 10px;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t.pro-qty {\r\n\t\t\twidth: auto;\r\n\t\t\t@media #{$sm-layout} {\r\n\t\t\t\tdisplay: inline-flex;\r\n\t\t\t}\r\n\t\t\tinput {\r\n\t\t\t\tmargin: 0 5px;\r\n\t\t\t\tmin-width: 45px;\r\n\t\t\t\t@media #{$sm-layout} {\r\n\t\t\t\t\tmin-width: 30px;\r\n\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t.qtybtn {\r\n\t\t\t\tfont-size: 20px;\r\n\t\t\t\tline-height: 27px;\r\n\t\t\t\tfont-weight: 500;\r\n\t\t\t\t@media #{$md-layout} {\r\n\t\t\t\t\tfont-size: 18px;\r\n\t\t\t\t}\r\n\t\t\t\t@media #{$sm-layout} {\r\n\t\t\t\t\tfont-size: 16px;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t.quantity-input {\r\n\t\t\t\t@media #{$md-layout} {\r\n\t\t\t\t\tfont-size: 18px;\r\n\t\t\t\t}\r\n\t\t\t\t@media #{$sm-layout} {\r\n\t\t\t\t\tfont-size: 16px;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.axil-product-cart-wrap {\r\n\t.cart-update-btn-area {\r\n\t\tdisplay: grid;\r\n\t\tgrid-template-columns: repeat(2, 1fr);\r\n\t\tgap: 30px;\r\n\t\t@media #{$sm-layout} {\r\n\t\t\tdisplay: block;\r\n\t\t}\r\n\t}\r\n\t.product-cupon {\r\n\t\tflex-wrap: nowrap;\r\n\t\tinput {\r\n\t\t\twidth: 100%;\r\n\t\t\tpadding: 0;\r\n\t\t\tborder-bottom: 2px solid #efefef;\r\n\t\t\tborder-radius: 0;\r\n\t\t\t@media #{$sm-layout} {\r\n\t\t\t\theight: 46px;\r\n\t\t\t}\r\n\t\t}\r\n\t\t.product-cupon-btn {\r\n\t\t\tmargin-left: 20px !important;\r\n\t\t}\r\n\t\t.axil-btn {\r\n\t\t\twidth: auto;\r\n\t\t\tborder-width: 2px;\r\n\t\t\tborder-color: #efefef;\r\n\t\t\tbackground-color: transparent;\r\n\t\t\t&:hover {\r\n\t\t\t\tborder-color: var(--color-primary);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t.update-btn {\r\n\t\ttext-align: right;\r\n\t\t@media #{$sm-layout} {\r\n\t\t\ttext-align: left;\r\n\t\t\tmargin-top: 30px;\r\n\t\t}\r\n\t\t.axil-btn {\r\n\t\t\tborder-width: 2px;\r\n\t\t\tborder-color: #efefef;\r\n\t\t\t&:hover {\r\n\t\t\t\tborder-color: var(--color-primary);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.axil-order-summery {\r\n\tbackground-color: #f9f3f0;\r\n\tborder-radius: 6px;\r\n\tpadding: 40px;\r\n\t@media #{$small-mobile} {\r\n\t\tpadding: 30px 20px;\r\n\t}\r\n\t.title {\r\n\t\tfont-weight: var(--s-medium);\r\n\t\t@media only screen and (max-width: 991px) {\r\n\t\t\tfont-size: 20px;\t\r\n\t\t}\r\n\t}\r\n\t.summery-table {\r\n\t\ttbody {\r\n\t\t\tborder-top: none !important;\r\n\t\t\ttd {\r\n\t\t\t\tborder-bottom: 1px solid;\r\n\t\t\t\tborder-color: rgba(101,105,115,0.2);\r\n\t\t\t\tfont-size: var(--font-size-b1);\r\n\t\t\t\tfont-weight: var(--s-medium);\r\n\t\t\t\tcolor: #292930;\r\n\t\t\t\tpadding: 18px 15px 18px 0;\r\n\t\t\t\tmin-width: 180px;\r\n\t\t\t\t@media #{$small-mobile} {\r\n\t\t\t\t\tmin-width: 90px;\r\n\t\t\t\t}\r\n\r\n\t\t\t}\r\n\t\t}\r\n\t\t.order-shipping {\r\n\t\t\t.input-group {\r\n\t\t\t\tmargin-bottom: 10px;\r\n\t\t\t\t&:last-child {\r\n\t\t\t\t\tmargin-bottom: 0;\r\n\t\t\t\t}\r\n\t\t\t\tlabel {\r\n\t\t\t\t\tcolor: #292930;\r\n\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\t&:before {\r\n\t\t\t\t\t\tborder-width: 2px;\r\n\t\t\t\t\t\tborder-color: #D5D4D4;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t&:after {\r\n\t\t\t\t\t\tbackground-color: var(--color-primary);\r\n\t\t\t\t\t\tborder: none;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t.order-total-amount {\r\n\t\t\tfont-size: 20px;\r\n\t\t\tfont-weight: var(--s-bold);\r\n\t\t\tcolor: var(--color-primary);\r\n\t\t}\r\n\t}\r\n\t&.order-checkout-summery {\r\n\t\t.summery-table-wrap {\r\n\t\t\tbackground-color: var(--color-white);\r\n\t\t\tborder-radius: 6px;\r\n\t\t\tpadding: 30px;\r\n\t\t\tmargin-bottom: 45px;\r\n\t\t\t@media #{$small-mobile} {\r\n\t\t\t\tpadding: 0;\r\n\t\t\t\tbackground-color: transparent;\r\n\t\t\t}\r\n\t\t}\r\n\t\t.summery-table {\r\n\t\t\tth {\r\n\t\t\t\tfont-size: 20px;\r\n\t\t\t\tcolor: var(--color-heading);\r\n\t\t\t\ttext-transform: capitalize;\r\n\t\t\t\tpadding: 15px 0;\r\n\t\t\t}\r\n\t\t\ttd {\r\n\t\t\t\tpadding: 18px 0;\r\n\t\t\t\t@media #{$lg-layout} {\r\n\t\t\t\t\tmin-width: 155px;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t@media #{$large-mobile} {\r\n\t\t\t\t\tmin-width: 100px;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t\r\n\t\t\t\t&.order-total-amount {\r\n\t\t\t\t\tcolor: var(--color-black);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tth, td {\r\n\t\t\t\t&:last-child {\r\n\t\t\t\t\ttext-align: right;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\ttr {\r\n\t\t\t\t&:last-child {\r\n\t\t\t\t\ttd {\r\n\t\t\t\t\t\tborder-bottom: none;\r\n\t\t\t\t\t\tpadding-bottom: 0;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t.order-shipping {\r\n\t\t\t\t.shipping-amount {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tjustify-content: space-between;\r\n\t\t\t\t\tmargin-bottom: 12px;\r\n\t\t\t\t\t.title {\r\n\t\t\t\t\t\tdisplay: inline-block;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\ttd {\r\n\t\t\t\t\ttext-align: left;\r\n\t\t\t\t}\r\n\t\t\t\t.input-group {\r\n\t\t\t\t\tmargin-bottom: 5px;\r\n\t\t\t\t\tlabel {\r\n\t\t\t\t\t\tcolor: var(--color-body);\r\n\t\t\t\t\t\tpadding-left: 26px;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t.order-total {\r\n\t\t\t\ttd {\r\n\t\t\t\t\tfont-size: 20px;\r\n\t\t\t\t\tfont-weight: 600;\r\n\t\t\t\t\tcolor: var(--color-black);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t.order-payment-method {\r\n\t\t\t.single-payment {\r\n\t\t\t\tborder-bottom: 1px solid var(--color-light);\r\n\t\t\t\tmargin-bottom: 20px;\r\n\t\t\t\tpadding-bottom: 20px;\r\n\t\t\t\t.input-group {\r\n\t\t\t\t\tmargin-bottom: 20px;\r\n\t\t\t\t\tlabel {\r\n\t\t\t\t\t\tfont-size: 20px;\r\n\t\t\t\t\t\tcolor: #292930;\r\n\t\t\t\t\t\t@media #{$sm-layout} {\r\n\t\t\t\t\t\t\tfont-size: 18px;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t&:before {\r\n\t\t\t\t\t\t\tbackground-color: transparent;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t&:after {\r\n\t\t\t\t\t\t\tcontent: \"\\f00c\";\r\n\t\t\t\t\t\t\tfont-family: var(--font-awesome);\r\n\t\t\t\t\t\t\tfont-size: 8px;\r\n\t\t\t\t\t\t\tcolor: var(--color-white);\r\n\t\t\t\t\t\t\tfont-weight: 900;\r\n\t\t\t\t\t\t\tline-height: 8px;\r\n\t\t\t\t\t\t\ttransform: rotate(0deg);\r\n\t\t\t\t\t\t\tbackground-color: transparent;\r\n\t\t\t\t\t\t\tborder: none;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\tinput[type=\"radio\"]:checked ~ label::before {\r\n\t\t\t\t\t\tbackground-color: var(--color-primary);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tp {\r\n\t\t\t\t\tpadding-left: 28px;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t.checkout-btn {\r\n\t\twidth: 100%;\r\n\t\ttext-align: center;\r\n\t\t&:hover {\r\n\t\t\t&:before {\r\n\t\t\t\ttransform: scale(1.05);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.axil-checkout-billing {\r\n\t.title {\r\n\t\tfont-weight: var(--s-medium);\r\n\t}\r\n\t.form-group {\r\n\t\tlabel {\r\n\t\t\tspan {\r\n\t\t\t\tcolor: var(--color-chart03);\r\n\t\t\t}\r\n\t\t}\r\n\t\tinput {\r\n\t\t\theight: 60px;\r\n\t\t\tborder-color: var(--color-light);\r\n\t\t\tpadding: 0 30px;\r\n\t\t}\r\n\t\ttextarea {\r\n\t\t\tborder-color: var(--color-light);\r\n\t\t\tpadding: 15px 30px;\r\n\t\t\tline-height: var(--line-height-b2);\r\n\t\t}\r\n\t\tselect {\r\n\t\t\tborder-color: var(--color-light);\r\n\t\t}\r\n\t\t&.input-group {\r\n\t\t\tmargin-bottom: 40px;\r\n\t\t\tlabel {\r\n\t\t\t\ttop: 0;\r\n\t\t\t\tleft: 0;\r\n\t\t\t\tpointer-events: auto;\r\n\t\t\t\tcolor: #292930;\r\n\t\t\t\t\r\n\t\t\t\t&:after {\r\n\t\t\t\t\tborder-width: 2px;\r\n\t\t\t\t\ttop: 6px;\r\n\t\t\t\t\theight: 6px;\r\n\t\t\t\t\twidth: 11px;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t&.different-shippng {\r\n\t\t\tmargin-bottom: 40px;\r\n\t\t\t.toggle-bar {\r\n\t\t\t\tlabel {\r\n\t\t\t\tposition: initial;\r\n\t\t\t\tfont-size: 24px;\r\n\t\t\t\tpadding-left: 0;\r\n\t\t\t\tpadding-right: 28px;\r\n\t\t\t\twidth: 100%;;\r\n\t\t\t\tcolor: #292930;\r\n\t\t\t\t@media #{$sm-layout} {\r\n\t\t\t\t\tfont-size: 20px;\r\n\t\t\t\t}\r\n\t\t\t\t@media #{$small-mobile} {\r\n\t\t\t\t\tfont-size: 17px;\r\n\t\t\t\t}\r\n\t\t\t\t&:before {\r\n\t\t\t\t\tleft: auto;\r\n\t\t\t\t\tright: 0;\r\n\t\t\t\t\ttop: 6px;\r\n\t\t\t\t}\r\n\t\t\t\t&:after {\r\n\t\t\t\t\tleft: auto;\r\n\t\t\t\t\tright: 3px;\r\n\t\t\t\t\ttop: 10px;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tinput {\r\n\t\t\t\tcursor: pointer\r\n\t\t\t}\r\n\t\t\t\t\r\n\t\t\t}\r\n\t\t\t.toggle-open {\r\n\t\t\t\tdisplay: none;\r\n\t\t\t\tpadding: 10px 0 0;\r\n\t\t\t\tmargin-top: 20px;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\r\n}\r\n\r\n.axil-checkout-notice {\r\n\tmargin-bottom: 40px;\r\n\t.axil-toggle-box {\r\n\t\tmargin-bottom: 20px;\r\n\t}\r\n\t.toggle-bar {\r\n\t\tbackground-color: var(--color-lighter);\r\n\t\tborder-radius: 6px;\r\n\t\tpadding: 17px 30px;\r\n\t\ti {\r\n\t\t\tmargin-right: 8px;\r\n\t\t}\r\n\t\ta {\r\n\t\t\tfont-weight: 500;\r\n\t\t\ttransition: var(--transition);\r\n\t\t\ti {\r\n\t\t\t\tcolor: var(--color-body);\r\n\t\t\t\tfont-weight: 400;\r\n\t\t\t\tmargin-left: 5px;\r\n\t\t\t}\r\n\t\t\t&:focus {\r\n\t\t\t\tcolor: var(--color-heading);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t.toggle-open {\r\n\t\tdisplay: none;\r\n\t\tmargin-top: 20px;\r\n\t}\r\n\t.axil-checkout-coupon {\r\n\t\tborder: 1px solid var(--color-light);\r\n\t\tborder-radius: 16px;\r\n\t\tpadding: 30px;\r\n\t\tp {\r\n\t\t\tfont-size: var(--font-size-b2);\r\n\t\t\tmargin-bottom: 20px;\r\n\t\t}\r\n\t\tinput {\r\n\t\t\tborder: 1px solid var(--color-light);\r\n\t\t\twidth: auto;\r\n\t\t\theight: 50px;\t\r\n\t\t\tmargin-right: 10px;\r\n\t\t\tmargin-bottom: 10px;\r\n\t\t\tborder-radius: 6px !important;\r\n\t\t}\r\n\r\n\t\t.axil-btn {\r\n\t\t\tborder-width: 1px;\r\n\t\t\tpadding: 12px 40px;\r\n\t\t\tborder-color: var(--color-light);\r\n\t\t\t&:hover {\r\n\t\t\t\tborder-color: var(--color-primary);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t.axil-checkout-login {\r\n\t\tborder: 1px solid var(--color-light);\r\n\t\tborder-radius: 16px;\r\n\t\tpadding: 30px;\r\n\t\tp {\r\n\t\t\tmargin-bottom: 30px;\r\n\t\t}\r\n\t\tinput {\r\n\t\t\tborder-color: var(--color-light);\r\n\t\t}\r\n\t\t.axil-btn {\r\n\t\t\twidth: auto;\r\n\t\t\tpadding: 11px 40px;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n", "/*-----------------------\r\n My Account Dashboard \r\n-------------------------*/\r\n.axil-signin-area {\r\n\tposition: fixed;\r\n\ttop: 0;\r\n\tbottom: 0;\r\n\tleft: 0;\r\n\tright: 0;\r\n\theight: 100%;\r\n\twidth: 100%;\r\n\toverflow: hidden;\r\n}\r\n\r\n.signin-header {\r\n\tposition: fixed;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\tright: 0;\r\n\tpadding: 40px 100px;\r\n\tz-index: 1;\r\n\t@media only screen and (max-width: 991px) {\r\n\t\tpadding: 40px 30px;\t\r\n\t}\r\n\t@media #{$large-mobile} {\r\n\t\tpadding: 30px;\r\n\t\ttext-align: center;\r\n\t}\r\n\t.site-logo {\r\n\t\tdisplay: inline-block;\r\n\t\t@media #{$large-mobile} {\r\n\t\t\tmargin-bottom: 40px;\r\n\t\t}\r\n\t}\r\n\t.singin-header-btn {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: flex-end;\r\n\t\t@media #{$large-mobile} {\r\n\t\t\tjustify-content: center;\r\n\t\t\tflex-direction: column;\r\n\t\t}\r\n\t\tp {\r\n\t\t\tmargin-bottom: 0;\r\n\t\t\tcolor: #292930;\r\n\t\t\tfont-size: var(--font-size-b2);\r\n\t\t\tfont-weight: var(--p-medium);\r\n\t\t}\r\n\t\t.sign-up-btn {\r\n\t\t\tmargin-left: 40px;\r\n\t\t    @media #{$md-layout} {\r\n\t\t    \tmargin-left: 20px;\r\n\t\t    }\r\n\t\t    @media #{$large-mobile} {\r\n\t\t    \tmargin-left: 0;\r\n\t\t    \tmargin-top: 10px;\r\n\t\t    }\r\n\t\t}\r\n\t}\r\n\t.back-btn {\r\n\t\twidth: 40px;\r\n\t\theight: 40px;\r\n\t\tline-height: 40px;\r\n\t\tborder: 1px solid #CBD3D9;\r\n\t\tborder-radius: 4px;\r\n\t\tfont-size: 16px;\r\n\t\tdisplay: block;\r\n\t\ttext-align: center;\r\n\t\ttransition: var(--transition);\r\n\r\n\t\t&:hover {\r\n\t\t\tbackground-color: var(--color-primary);\r\n\t\t\tborder-color: var(--color-primary);\r\n\t\t\tcolor: var(--color-white);\r\n\t\t\ti {\r\n\t\t\t\tanimation: prevNavSlide 400ms;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.axil-signin-banner {\r\n\tmin-height: 900px;\r\n\theight: 100vh;\r\n\tpadding: 200px 50px 0 100px;\r\n\t@media #{$lg-layout} {\r\n\t\tmargin-right: 50px;\r\n\t}\r\n\t@media only screen and (max-width: 991px) {\r\n\t\tdisplay: none;\t\r\n\t}\r\n}\r\n\r\n.axil-signin-form-wrap {\r\n\twidth: 100%;\r\n\theight: calc(100vh - 180px);\r\n\toverflow-y: auto;\r\n\tdisplay: flex;\r\n\tmargin: 180px -30px -30px;\r\n\t@media only screen and (max-width: 991px) {\r\n\t\tjustify-content: center;\r\n\t\ttext-align: center;\r\n\t\tmargin: 150px 0 0;\r\n\t}\r\n\t@media #{$large-mobile} {\r\n\t\tmargin: 200px 0 0;\r\n\t\theight: calc(100vh - 200px);\r\n\t}\r\n}\r\n\r\n.axil-signin-form {\r\n\tmax-width: 450px;\r\n\twidth: 100%;\r\n\tpadding: 30px;\r\n\t.singin-form {\r\n\t\tpadding-bottom: 30px;\r\n\t}\r\n\t.title {\r\n\t\tmargin-bottom: 16px;\r\n\t}\r\n\tp {\r\n\t\tcolor: var(--color-gray);\r\n\t}\r\n\t.form-group {\r\n\t\tmargin-bottom: 35px;\r\n\t\t&:last-child {\r\n\t\t\tmargin-bottom: 0;\r\n\t\t}\r\n\t}\r\n\t.form-control {\r\n\t\theight: 60px;\r\n\t\tborder-color: var(--color-light);\r\n\t\tpadding: 0 30px;\r\n\t\tcolor: var(--color-body);\r\n\t}\r\n\t.submit-btn {\r\n\t\twidth: auto;\r\n\t}\r\n\t.forgot-btn {\r\n\t\tfont-size: 14px;\r\n\t\tcolor: var(--color-primary);\r\n\t\ttransition: var(--transition);\r\n\t\t&:hover {\r\n\t\t\tcolor: var(--color-body);\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.axil-dashboard-warp {\r\n\t.axil-dashboard-author {\r\n\t\tmargin-bottom: 50px;\r\n\t\t.media {\r\n\t\t\talign-items: center;\r\n\t\t}\r\n\t\t.thumbnail {\r\n\t\t\tmargin-bottom: 10px;\r\n\t\t\timg {\r\n\t\t\t\tborder-radius: 50%;\r\n\t\t\t}\r\n\t\t}\r\n\t\t.joining-date {\r\n\t\t\tfont-size: 14px;\r\n\t\t\tcolor: var(--color-body);\r\n\t\t\tfont-weight: var(--s-medium); \r\n\r\n\t\t}\r\n\t}\r\n\t.tab-content {\r\n\t\tpadding-left: 45px;\r\n\t\t@media only screen and (max-width: 1199px) {\r\n\t\t\tpadding-left: 0;\t\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.axil-dashboard-aside {\r\n\tborder: 1px solid var(--color-light);\r\n\tpadding: 40px 35px 30px;\r\n\tborder-radius: 6px;\r\n\t@media #{$md-layout} {\r\n\t\tpadding: 30px 15px 20px;\r\n\t}\r\n\t@media #{$sm-layout} {\r\n\t\tmargin-bottom: 40px;\r\n\t}\r\n\t.nav-tabs {\r\n\t\tborder-bottom: none;\r\n\t}\r\n\t.nav-link {\r\n\t\tfont-weight: 500;\r\n\t\tcolor: var(--color-body);\r\n\t\tfont-size: var(--font-size-b2);\r\n\t\tposition: relative;\r\n\t\tborder-radius: 6px;\r\n\t\tpadding: 9px 10px 9px 55px;\r\n\t\tmargin-bottom: 8px;\r\n\t\ttransition: var(--transition);\r\n\t\tborder: none;\r\n\t\twidth: 100%;\r\n\t\t@media #{$md-layout} {\r\n\t\t\tpadding: 9px 10px 9px 40px;\r\n\t\t}\r\n\t\ti {\r\n\t\t\tposition: absolute;\r\n\t\t\ttop: 12px;\r\n\t\t\tleft: 24px;\r\n\t\t\tfont-size: 18px;\r\n\t\t\t@media #{$md-layout} {\r\n\t\t\t\tleft: 10px;\r\n\t\t\t}\r\n\t\t}\r\n\t\t&.active,\r\n\t\t&:hover {\r\n\t\t\tbackground-color: var( --color-lighter);\r\n\t\t\tcolor: var(--color-primary);\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.axil-dashboard-overview {\r\n\t.welcome-text {\r\n\t\tcolor: var(--color-black);\r\n\t\tfont-size: 18px;\r\n\t\tmargin-bottom: 25px;\r\n\t\tspan {\r\n\t\t\tfont-weight: var(--p-bold);\r\n\t\t}\r\n\t\ta {\r\n\t\t\tcolor: var(--color-chart03);\r\n\t\t\ttransition: var(--transition);\r\n\t\t\t&:hover {\r\n\t\t\t\tcolor: var(--color-primary);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\tp {\r\n\t\tfont-size: var(--font-size-b1);\r\n\t}\r\n}\r\n\r\n.axil-dashboard-order {\r\n\t.table {\r\n\t\tfont-family: var(--font-secondary);\r\n\t\tthead {\r\n\t\t\tbackground-color: var(--color-lighter);\r\n\t\t\tth {\r\n\t\t\t\tborder: none;\r\n\t\t\t\tpadding: 18px 20px;\r\n\t\t\t\tvertical-align: middle;\r\n\t\t\t\tfont-size: 20px;\r\n\t\t\t\tfont-weight: var(--p-medium);\r\n\t\t\t\ttext-transform: capitalize;\r\n\t\t\t\t&:first-child {\r\n\t\t\t\t\tborder-radius: 6px 0 0 6px;\r\n\t\t\t\t}\r\n\t\t\t\t&:last-child {\r\n\t\t\t\t\tborder-radius: 0 6px 6px 0;\r\n\t\t\t\t\tpadding-right: 30px;\r\n\t\t\t\t\ttext-align: right;\r\n\t\t\t\t}\r\n\t\t\t\t@media #{$smlg-device} {\r\n\t\t\t\t\tfont-size: 18px;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\ttbody {\r\n\t\t\tborder-top: none;\r\n\t\t\ttr {\r\n\t\t\t\ttd, th {\r\n\t\t\t\t\tpadding: 20px 20px;\r\n\t\t\t\t\tvertical-align: middle;\r\n\t\t\t\t\tfont-weight: var(--p-medium);\r\n\t\t\t\t\tfont-size: var(--font-size-b2);\r\n\t\t\t\t\tcolor: var(--color-heading);\r\n\t\t\t\t\tborder-top: none;\r\n\t\t\t\t\tborder-bottom: 2px solid var(--color-lighter);\r\n\t\t\t\t\t&:last-child {\r\n\t\t\t\t\t\ttext-align: right;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tth {\r\n\t\t\t\t\tcolor: var(--color-chart03);\r\n\t\t\t\t}\r\n\t\t\t\ttd {\r\n\t\t\t\t\tmin-width: 150px;\r\n\t\t\t\t}\r\n\t\t\t\t&:first-child {\r\n\t\t\t\t\ttd, th {\r\n\t\t\t\t\t\tborder-top: none;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t.view-btn {\r\n\t\t\t\tpadding: 9px 20px;\r\n\t\t\t\tborder: 1px solid var(--color-body);\r\n\t\t\t\tbackground-color: transparent;\r\n\t\t\t\tcolor: var(--color-dark);\r\n\t\t\t\t&:before {\r\n\t\t\t\t\tdisplay: none;\r\n\t\t\t\t}\r\n\t\t\t\t&:after {\r\n\t\t\t\t\tdisplay: none;\r\n\t\t\t\t}\r\n\t\t\t\t&:hover {\r\n\t\t\t\t\tbackground-color: var(--color-primary);\r\n\t\t\t\t\tborder-color: var(--color-primary);\r\n\t\t\t\t\tcolor: var(--color-white);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.axil-dashboard-address {\r\n\t.notice-text {\r\n\t\tfont-size: var(--font-size-b2);\r\n\t\tcolor: #292930;\r\n\t}\r\n\t.addrss-header {\r\n\t\tborder-bottom: 1px solid var(--color-light);\r\n\t\tpadding-bottom: 20px;\r\n\t\tmargin-bottom: 20px;\r\n\t}\r\n\t.title {\r\n\t\tfont-weight: var(--p-medium);\r\n\t\tcolor: #292930;\r\n\t\t@media #{$lg-layout} {\r\n\t\t\tfont-size: 26px;\r\n\t\t}\r\n\t}\r\n\t.address-edit {\r\n\t\tfont-size: var(--font-size-b2);\r\n\t\tcolor: #292930;\r\n\t\ttransition: var(--transition);\r\n\t\t&:hover {\r\n\t\t\tcolor: var(--color-primary);\r\n\t\t}\r\n\t}\r\n\tul {\r\n\t\t@extend %liststyle;\r\n\t\tli {\r\n\t\t\tfont-size: var(--font-size-b2); \r\n\t\t\tline-height: var(--line-height-b2);\r\n\t\t\tcolor: #292930;\r\n\t\t\tfont-family: var(--font-primary);\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.axil-dashboard-account {\r\n\t.form-group {\r\n\t\t.form-control {\r\n\t\t\theight: 60px;\r\n\t\t\tpadding: 10px 30px;\r\n\t\t\tborder-color: var(--color-light);\r\n\t\t\tcolor: var(--color-body);\r\n\t\t}\r\n\t\tselect {\r\n\t\t\toption:hover {\r\n\t\t\t\tbackground: red !important;\r\n\t\t\t\tcolor: red;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}", "/*----------------------------\r\nBlog List  \r\n----------------------------*/\r\n.axil-post-wrapper {\r\n    .content-blog {\r\n        border-top: 1px solid #f3f3f3;\r\n        padding-top: 60px;\r\n        &:first-child {\r\n            margin-top: 0 !important;\r\n            border-top: none;\r\n            padding-top: 0;\r\n        }\r\n    }\r\n}\r\n\r\n\r\n/* Main Blog  */\r\n\r\n.content-blog {\r\n    .thumbnail {\r\n        margin-bottom: 30px;\r\n        a {\r\n            display: block;\r\n            border-radius: var(--radius);\r\n            img {\r\n                width: 100%;\r\n                border-radius: var(--radius);\r\n            }\r\n        }\r\n    }\r\n    .content {\r\n        .title {\r\n            font-weight: 500;\r\n            line-height: 1.3;\r\n            a {\r\n                @extend %transition;\r\n            }\r\n        }\r\n        p {\r\n            margin-bottom: 30px;\r\n        }\r\n        .read-more-btn {\r\n            .axil-btn {\r\n                display: inline-flex;\r\n            }\r\n        }\r\n    }\r\n    &.sticky {\r\n        .inner {\r\n            background: #f9f3f0;\r\n            border-left: 6px solid var(--color-tertiary);\r\n            border-radius: var(--radius);\r\n            padding: 50px;\r\n            @media #{$small-mobile} {\r\n                padding: 30px 15px;\r\n            }\r\n        }\r\n    }\r\n    &.format-quote {\r\n        .inner {\r\n            background: #f9f3f0;\r\n            border-left: 6px solid var(--color-tertiary);\r\n            border-radius: var(--radius);\r\n            padding: 50px 40px 30px;\r\n            @media #{$large-mobile} {\r\n                padding: 30px 20px 10px;\r\n            }\r\n            .content {\r\n                blockquote {\r\n                    .title {\r\n                        font-weight: 700;\r\n                        line-height: 1.31;\r\n                        font-size: 35px;\r\n                        font-style: italic;\r\n                        @media #{$sm-layout} {\r\n                            font-size: 28px;\r\n                        }\r\n                        @media #{$large-mobile} {\r\n                            font-size: 24px;\r\n                        }\r\n                        a {\r\n                            @extend %transition;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n    &.format-video {\r\n        .thumbnail {\r\n            position: relative;\r\n            text-align: center;\r\n            &:after {\r\n                content: \"\";\r\n                height: 100%;\r\n                width: 100%;\r\n                background-color: var(--color-black);\r\n                opacity: .2;\r\n                border-radius: 4px;\r\n                position: absolute;\r\n                top: 0;\r\n                left: 0;\r\n                right: 0;\r\n                bottom: 0;\r\n                z-index: 1;\r\n            }\r\n            .popup-video {\r\n                position: absolute;\r\n                top: 50%;\r\n                left: 0;\r\n                right: 0;\r\n                transform: translateY(-50%);\r\n                z-index: 2;\r\n                .play-btn {\r\n                    height: 150px;\r\n                    width: 150px;\r\n                    display: flex;\r\n                    align-items: center;\r\n                    justify-content: center;\r\n                    background-color: rgba(0,0,0,.8);\r\n                    border-radius: 50%;\r\n                    margin: 0 auto;\r\n                    font-size: 32px;\r\n                    color: var(--color-white);\r\n                    transition: var(--transition);\r\n                    &:hover {\r\n                        background-color: rgba(0,0,0,1);;\r\n                    }\r\n                    @media only screen and (max-width: 767px) {\r\n                        height: 80px;\r\n                        width: 80px;\r\n                        font-size: 24px;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n    // Post Sidebar \r\n    &.post-list-view {\r\n        display: flex;\r\n        align-items: center;\r\n        border-bottom: 1px solid #f3f3f3;\r\n        padding-bottom: 20px;\r\n        @media #{$small-mobile} {\r\n            align-items: flex-start;\r\n        }\r\n        &:last-child {\r\n            margin-bottom: 0;\r\n            border-bottom: none;\r\n            padding-bottom: 0;\r\n        }\r\n        .thumbnail {\r\n            width: 120px;\r\n            margin-right: 20px;\r\n            min-width: 120px;\r\n            overflow: hidden;\r\n            margin-bottom: 0;\r\n            @media #{$lg-layout} {\r\n                width: 70px;\r\n                margin-right: 10px;\r\n                min-width: 70px;\r\n            }\r\n            @media #{$small-mobile} {\r\n                width: 80px;\r\n                min-width: 80px;\r\n            }\r\n            a {\r\n                border-radius: 6px;\r\n                overflow: hidden;\r\n                img {\r\n                    width: 100%;\r\n                    border-radius: 6px;\r\n                    transition: 0.5s;\r\n                    object-fit: contain;\r\n                }\r\n            }\r\n        }\r\n        .content {\r\n            flex: 1;\r\n            .title {\r\n                font-size: 17px;\r\n                margin-bottom: 10px;\r\n                @media #{$sm-layout} {\r\n                    font-size: 16px;\r\n                }\r\n                a {\r\n                    display: -webkit-box;\r\n                    -webkit-line-clamp: 2;\r\n                    -webkit-box-orient: vertical;\r\n                    overflow: hidden;\r\n                    text-overflow: ellipsis;\r\n                }\r\n            }\r\n            .axil-post-meta {\r\n                margin-bottom: 0;\r\n            }\r\n        }\r\n        &:hover {\r\n            .thumbnail {\r\n                img {\r\n                    transform: scale(1.1);\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n/*---------------------\r\n    Blog Meta \r\n-----------------------*/\r\n\r\n.axil-post-meta {\r\n    display: flex;\r\n    margin-bottom: 20px;\r\n    .post-author-avatar {\r\n        min-width: 50px;\r\n        max-height: 50px;\r\n        margin-right: 20px;\r\n        width: 50px;\r\n        img {\r\n            border-radius: 100%;\r\n            width: 100%;\r\n            height: 100%;\r\n            object-fit: contain;\r\n        }\r\n    }\r\n    .post-meta-content {\r\n        .author-title {\r\n            margin-bottom: 5px;\r\n            font-size: 16px;\r\n            a {\r\n                @extend %transition;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.post-meta-list {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    padding: 0;\r\n    margin: 0;\r\n    list-style: none;\r\n    margin: 0 -15px;\r\n    li {\r\n        color: var(--color-body);\r\n        font-size: 14px;\r\n        padding: 0 15px;\r\n        position: relative;\r\n        margin-top: 0;\r\n        margin-bottom: 0;\r\n        &::after {\r\n            position: absolute;\r\n            content: \"\";\r\n            background: #CBD3D9;\r\n            width: 1px;\r\n            height: 14px;\r\n            right: 0;\r\n            top: 50%;\r\n            transform: translateY(-50%);\r\n        }\r\n        &:last-child {\r\n            &::after {\r\n                display: none;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n/*---------------------\r\n    Blog Grid \r\n-----------------------*/\r\n.blog-grid {\r\n    border: 1px solid #f1f1f1;\r\n    border-radius: 6px;\r\n    padding: 20px;\r\n    .thumbnail {\r\n        margin-bottom: 25px;\r\n        overflow: hidden;\r\n        border-radius: 6px;\r\n        position: relative;\r\n        img {\r\n            transition: .5s;\r\n        }\r\n        .blog-category {\r\n            position: absolute;\r\n            bottom: 20px;\r\n            right: 20px;\r\n            a {\r\n                background-color: rgba(255, 255, 255, 0.5);\r\n                border: 1px solid rgba(255, 255, 255, 0.5);\r\n                backdrop-filter: blur(25px);\r\n                box-shadow: 0 4px 30px rgba(0, 0, 0,.1);\r\n                padding: 2px 10px;\r\n                border-radius: 4px;\r\n                color: var(--color-white);\r\n                font-size: 14px;\r\n            }\r\n        }\r\n    }\r\n    .content {\r\n        .title {\r\n            margin-bottom: 20px;\r\n        }\r\n        .axil-btn {\r\n            padding: 0;\r\n            align-items: center;\r\n            color: var(--color-heading);\r\n            i {\r\n                padding-left: 6px;\r\n                top: 1px;\r\n                color: var(--color-heading);\r\n                transition: var(--transition);\r\n            }\r\n            &:after {\r\n                content: \"\";\r\n                height: 1px;\r\n                width: 0;\r\n                background-color: var(--color-primary);\r\n                position: absolute;\r\n                bottom: 0;\r\n                right: 0;\r\n                transition: var(--transition);\r\n            }\r\n            &:hover {\r\n                color: var(--color-primary);\r\n                &:after {\r\n                    width: 100%;\r\n                    left: 0;\r\n                }\r\n                i {\r\n                    color: var(--color-primary);\r\n                }\r\n            }\r\n        }\r\n    }\r\n    &:hover {\r\n        .thumbnail {\r\n            img {\r\n                transform: scale(1.1);\r\n            }\r\n        }\r\n    }\r\n}", "/*----------------------------\r\nBlog Single  \r\n----------------------------*/\r\n.axil-single-post {\r\n    .post-content {\r\n        padding: 0 75px;\r\n        @media #{$smlg-device} {\r\n            padding: 0;\r\n        }\r\n    }\r\n    &.post-formate {\r\n        .content-block {\r\n            .post-thumbnail img {\r\n                border-radius: 6px;\r\n            }\r\n        }\r\n    }\r\n    &.post-video {\r\n        .format-video {\r\n            .thumbnail {\r\n                padding-top: 0;\r\n                margin-bottom: 0;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.post-single-wrapper {\r\n    padding-top: 80px;\r\n    @media only screen and (max-width: 991px) {\r\n        padding-top: 30px;\r\n    }\r\n    .content-blog {\r\n        border-top: none;\r\n        padding-top: 0;\r\n    }\r\n}\r\n\r\n.axil-post-wrapper {\r\n    .audio-player {\r\n        margin-bottom: 20px;\r\n        audio {\r\n            width: 100%;\r\n        }\r\n    }\r\n    .post-heading {\r\n        border-bottom: 1px solid var(--color-border-light);\r\n        margin-bottom: 45px;\r\n        padding-bottom: 30px;\r\n    }\r\n}\r\n\r\n\r\n.axil-single-widget:first-child,\r\n.widget-sidebar:first-child {\r\n    margin-top: 0 !important;\r\n}\r\n\r\n.newsletter-inner {\r\n    &::before {\r\n        content: \"\";\r\n        position: absolute;\r\n        left: 30px;\r\n        background: url(../images/send-mail.png) no-repeat;\r\n        z-index: 2;\r\n        top: 17px;\r\n        width: 25px;\r\n        height: 25px;\r\n    }\r\n}\r\n\r\n.wp-block-columns {\r\n    display: flex;\r\n    margin-bottom: 28px;\r\n    margin: 0 -15px;\r\n    @media #{$sm-layout} {\r\n        flex-wrap: wrap;\r\n    }\r\n}\r\n\r\n.wp-block-column {\r\n    flex-grow: 1;\r\n    min-width: 0;\r\n    word-break: break-word;\r\n    overflow-wrap: break-word;\r\n    padding-right: 15px;\r\n    padding-left: 15px;\r\n    .wp-block-image {\r\n        img {\r\n            border-radius: 6px;\r\n        }\r\n    }\r\n}\r\n\r\n.post-details figure,\r\n.entry-content figure {\r\n    margin-bottom: 40px;\r\n    @media #{$large-mobile} {\r\n        margin-bottom: 20px;\r\n    }\r\n}\r\n\r\n.post-details__social-share {\r\n    .share-on-text {\r\n        display: inline-block;\r\n        margin-bottom: 10px;\r\n        margin-right: -5px;\r\n        @media #{$smlg-device} {\r\n            margin-right: -18px;\r\n        }\r\n    }\r\n    .social-share {\r\n        flex-direction: column;\r\n        align-items: center;\r\n        @media only screen and (max-width: 992px) {\r\n            flex-direction: row;\r\n            margin-bottom: 15px;\r\n        }\r\n    }\r\n}\r\n\r\n.sticky-top {\r\n    z-index: 0 !important;\r\n    top: 100px;\r\n}\r\n\r\n\r\n", "/*---------------------\r\nAxil Comment  \r\n----------------------*/\r\n\r\n.axil-total-comment-post {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 30px 0;\r\n    @media #{$large-mobile} {\r\n        display: block;\r\n    }\r\n    .add-comment-button {\r\n        @media #{$large-mobile} {\r\n            margin-top: 20px;\r\n        }\r\n    }\r\n}\r\n\r\n\r\n/*---------------------------\r\n  Comment Form Styles  \r\n----------------------------*/\r\n\r\n.comment-respond {\r\n    margin: 50px 0 0;\r\n    .title {\r\n        margin-bottom: 20px;\r\n    }\r\n    .comment-notes {\r\n        color: var(--color-gray);\r\n        margin-bottom: 40px;\r\n    }\r\n    .comment-form-cookies-consent {\r\n        margin-bottom: 20px;\r\n    }\r\n}\r\n\r\n\r\n/* --------------------------\r\n  Comments Styles  \r\n-----------------------------*/\r\n\r\n.comment-list {\r\n    @extend %liststyle;\r\n    ul {\r\n        &.children {\r\n            @extend %liststyle;\r\n            padding-left: 75px;\r\n            @media #{$sm-layout} {\r\n                padding-left: 30px;\r\n            }\r\n        }\r\n    }\r\n    .comment {\r\n        margin-top: 0;\r\n        margin-bottom: 0;\r\n        .single-comment {\r\n            padding: 15px 0;\r\n            display: flex;\r\n            .comment-img {\r\n                margin-bottom: 15px;\r\n                min-width: 60px;\r\n                margin-right: 20px;\r\n                img {\r\n                    border-radius: 100%;\r\n                    width: 100%;\r\n                }\r\n            }\r\n        }\r\n        .commenter {\r\n            line-height: 33px;\r\n            margin-bottom: 6px;\r\n            a {\r\n                .hover-flip-item {\r\n                    span {\r\n                        &::before {\r\n                            color: var(--color-heading);\r\n                        }\r\n                        &::after {\r\n                            color: var(--color-primary);\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        .comment-meta {\r\n            display: flex;\r\n            align-items: center;\r\n            margin-bottom: 8px;\r\n            flex-wrap: wrap;\r\n        }\r\n        .time-spent {\r\n            color: var(--color-extra01);\r\n            font-size: 16px;\r\n            line-height: 24px;\r\n        }\r\n        .reply-edit {\r\n            a {\r\n                &.comment-reply-link {\r\n                    font-size: 16px;\r\n                    line-height: 24px;\r\n                    display: flex;\r\n                    color: var(--color-primary);\r\n                    margin-left: 8px;\r\n                    padding-left: 8px;\r\n                    position: relative;\r\n                    font-weight: 500;\r\n                    overflow: visible;\r\n                    @extend %transition;\r\n                    .hover-flip-item {\r\n                        span {\r\n                            &::before {\r\n                                color: var(--color-heading);\r\n                            }\r\n                            &::after {\r\n                                color: var(--color-primary);\r\n                            }\r\n                        }\r\n                    }\r\n                    &:hover {\r\n                        color: var(--color-primary);\r\n                    }\r\n                    &::before {\r\n                        position: absolute;\r\n                        content: \"\";\r\n                        top: 50%;\r\n                        transform: translateY(-50%);\r\n                        left: -2px;\r\n                        width: 4px;\r\n                        height: 4px;\r\n                        background: var(--color-extra01);\r\n                        border-radius: 100%;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.form-group label {\r\n    position: absolute;\r\n    top: -11px;\r\n    left: 20px;\r\n    pointer-events: none;\r\n    z-index: 4;\r\n    background: #fff;\r\n    padding: 0 10px;\r\n}", "/*-------------------------\r\n Blog Sidebar  \r\n---------------------------*/\r\n.axil-sidebar-area {\r\n    @media only screen and (max-width: 991px) {\r\n        margin-top: 60px;   \r\n    }\r\n    .axil-single-widget {\r\n        &:first-child {\r\n            margin-top: 0 !important;\r\n        }\r\n    }\r\n}\r\n\r\n.axil-single-widget {\r\n    border: 1px solid #f3f3f3;\r\n    border-radius: 6px;\r\n    padding: 30px;\r\n    .widget-title {\r\n        font-weight: 500;\r\n        margin-bottom: 30px;\r\n        color: var(--color-dark);\r\n    }\r\n    @media #{$small-mobile} {\r\n        padding: 20px;\r\n    }\r\n}\r\n\r\n\r\n/*---------------------\r\n    Tag Cloud \r\n-----------------------*/\r\n\r\n.tagcloud {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    margin: -5px;\r\n    a {\r\n        border: 2px solid var(--color-border-light);\r\n        font-size: var(--font-size-b2) !important;\r\n        color: var(--color-body);\r\n        height: 40px;\r\n        padding: 0 20px;\r\n        margin: 5px;\r\n        display: inline-block;\r\n        line-height: 35px;\r\n        border-radius: 500px;\r\n        @extend %transition;\r\n        font-family: var(--font-secondary);\r\n\r\n        &:hover {\r\n            background: var(--color-primary);\r\n            color: #ffffff;\r\n            border-color: var(--color-primary);\r\n        }\r\n    }\r\n}\r\n\r\n\r\n\r\n/*-----------------------\r\n    Blog Search  \r\n------------------------*/\r\n.blog-search,\r\n.wp-block-search {\r\n    position: relative;\r\n    input {\r\n        height: 50px;\r\n        border: 1px solid #F0F2F5;\r\n        background-color: #F0F2F5;\r\n        padding: 0 20px;\r\n        color: var(--color-heading);\r\n        padding-left: 50px;\r\n        font-size: 16px;\r\n        border-radius: var(--radius);\r\n        font-family: var(--font-secondary);\r\n    }\r\n    .search-button {\r\n        position: absolute;\r\n        left: 20px;\r\n        top: 50%;\r\n        transform: translateY(-50%);\r\n        border: 0 none;\r\n        padding: 0;\r\n        background-color: transparent;\r\n        width: auto;\r\n        i {\r\n            color: var(--color-body);\r\n            font-weight: 400;\r\n        }\r\n    }\r\n}\r\n\r\n\r\n\r\n/*---------------------\r\n    Widget Ress  \r\n-----------------------*/\r\n\r\n.widget_rss {\r\n    ul {\r\n        li {\r\n            a {\r\n                color: var(--color-heading);\r\n                text-decoration: none;\r\n                @extend %transition;\r\n                display: block;\r\n                &:hover {\r\n                    color: var(--color-primary);\r\n                }\r\n            }\r\n            span {\r\n                &.rss-date{\r\n                    font-size: 14px;\r\n                }\r\n            }\r\n            .rssSummary {\r\n                margin-top: 9px;\r\n            }\r\n            cite {\r\n                margin-top: 4px;\r\n                display: inline-block;\r\n                font-weight: 500;\r\n                font-size: 14px;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n/*---------------------\r\n    Widget Archives  \r\n-----------------------*/\r\n.widget_archive {\r\n    ul {\r\n        margin-bottom: 0;\r\n        list-style: disc;\r\n        li {\r\n            &::marker {\r\n                font-size: 18px;\r\n                color: #CED0D4;\r\n                transition: var(--transition);\r\n            }\r\n            a {\r\n                color: #65676B;\r\n                transition: var(--transition);\r\n            }\r\n            &:hover {\r\n                &::marker {\r\n                    color: var(--color-primary);\r\n                }\r\n                a {\r\n                    color: var(--color-black);\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n/*---------------------\r\n    Widget Archives Dropdown \r\n-----------------------*/\r\n.widget_archive_dropdown {\r\n    select {\r\n        border-radius: 4px;\r\n        height: 50px;\r\n        padding: 0 20px;\r\n    }\r\n}\r\n\r\n\r\n\r\n\r\n\r\n\r\n", "/*------------------------\r\n    Footer Styles  \r\n-------------------------*/\r\n\r\n.footer-top {\r\n    padding: 80px 0 40px;\r\n    position: relative;\r\n    @media #{$sm-layout} {\r\n        padding: 60px 0 20px;\r\n    }\r\n    &.separator-top {\r\n        &::after {\r\n            position: absolute;\r\n            top: 0;\r\n            width: 1290px;\r\n            height: 2px;\r\n            background-color: #F6F7FB;\r\n            content: \"\";\r\n            left: 0;\r\n            right: 0;\r\n            margin: 0 auto;\r\n            border-radius: 100px;\r\n        }\r\n    }\r\n}\r\n\r\n\r\n/*----------------------\r\n    Footer Widget  \r\n----------------------*/\r\n\r\n.axil-footer-widget {\r\n    margin-bottom: 40px;\r\n    .widget-title {\r\n        font-size: 18px;\r\n        font-weight: 500;\r\n        letter-spacing: -0.025em;\r\n        margin-bottom: 20px;\r\n    }\r\n    .inner {\r\n        ul {\r\n            padding-left: 0;\r\n            >li {\r\n                &:first-child {\r\n                    margin-top: 0;\r\n                }\r\n            }\r\n        }\r\n        ul {\r\n            list-style: none;\r\n            li {\r\n                margin-top: 12px;\r\n                margin-bottom: 12px;\r\n                a {\r\n                    color: var(--color-body);\r\n                    font-weight: 500;\r\n                    font-size: 16px;\r\n                    transition: 0.3s;\r\n                    position: relative;\r\n                    &:after {\r\n                        content: \"\";\r\n                        height: 2px;\r\n                        width: 0;\r\n                        background-color: var(--color-black);\r\n                        position: absolute;\r\n                        bottom: -2px;\r\n                        right: 0;\r\n                        opacity: 0;\r\n                        transition: 0.5s;\r\n                    }\r\n                    &:hover {\r\n                        color: var(--color-heading);\r\n                        &:after {\r\n                            width: 100%;\r\n                            opacity: 1;\r\n                            left: 0;\r\n\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n    .logo {\r\n        img {\r\n           height: 35px;\r\n           width: auto;\r\n        }\r\n    }\r\n    .support-list-item {\r\n        margin-bottom: 0;\r\n        li {\r\n            padding-left: 26px;\r\n            position: relative;\r\n            a {\r\n                position: initial!important;\r\n                font-weight: 400 !important;\r\n                &:after {\r\n                    display: none;\r\n                }\r\n            }\r\n            i {\r\n                padding-right: 5px;\r\n                position: absolute;\r\n                top: 5px;\r\n                left: 0;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n\r\n/*-----------------------\r\n    Copyright Area  \r\n------------------------*/\r\n\r\n.copyright-default {\r\n    padding: 15px 0;\r\n    .quick-link {\r\n        @extend %liststyle;\r\n        display: flex;\r\n        margin: -15px;\r\n        li {\r\n            padding: 15px;\r\n            position: relative;\r\n            color: var(--color-body);\r\n            font-weight: 500;\r\n            font-size: 14px;\r\n            &::after {\r\n                position: absolute;\r\n                content: \"\";\r\n                background: var(--color-lightest);\r\n                width: 5px;\r\n                height: 5px;\r\n                border-radius: 100%;\r\n                right: -3px;\r\n                top: 50%;\r\n                transform: translateY(-50%);\r\n                @media #{$large-mobile} {\r\n                    display: none;\r\n                }\r\n            }\r\n            a {\r\n                color: var(--color-body);\r\n                font-weight: 500;\r\n                font-size: 14px;\r\n                letter-spacing: -0.025em;\r\n                transition: 0.5s;\r\n                position: relative;\r\n                &:after {\r\n                    content: \"\";\r\n                    height: 2px;\r\n                    width: 0;\r\n                    background-color: var(--color-black);\r\n                    position: absolute;\r\n                    bottom: -2px;\r\n                    right: 0;\r\n                    opacity: 0;\r\n                    transition: 0.5s;\r\n                }\r\n                &:hover {\r\n                    color: var(--color-heading);\r\n                    &:after {\r\n                        width: 100%;\r\n                        opacity: 1;\r\n                        left: 0;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        &.payment-icons-bottom {\r\n            margin: -15px -10px;\r\n            li {\r\n                padding: 15px 10px;\r\n                &::after {\r\n                    display: none;\r\n                }\r\n            }\r\n        }\r\n    }\r\n    &.separator-top {\r\n        position: relative;\r\n        &::after {\r\n            position: absolute;\r\n            content: \"\";\r\n            background-color: #F6F7FB;\r\n            border-radius: 100px;\r\n            height: 2px;\r\n            width: 1290px;\r\n            left: 0;\r\n            right: 0;\r\n            margin: 0 auto;\r\n            top: 0;\r\n        }\r\n    }\r\n    .copyright-right {\r\n        span {\r\n            &.card-text {\r\n                color: var(--color-body);\r\n                font-size: 14px;\r\n                font-weight: 500;\r\n                display: inline-block;\r\n                margin: 10px 20px;\r\n                letter-spacing: -0.025em;\r\n                @media #{$large-mobile} {\r\n                    margin-left: 0;   \r\n                }\r\n            }\r\n        }\r\n    }\r\n    .copyright-left {\r\n       @media #{$smlg-device} {\r\n            text-align: center;  \r\n       }\r\n       @media #{$large-mobile} {\r\n            flex-direction: column;  \r\n            align-items: center;\r\n       }\r\n\r\n        ul+ul {\r\n            margin-left: 15px;\r\n            @media #{$large-mobile} {\r\n                margin-left: -15px;   \r\n            }\r\n            li {\r\n                &::after {\r\n                    display: none;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n\r\n// Footer Dark Styles\r\n.footer-dark {\r\n    background-color: var(--color-heading);\r\n    .axil-footer-widget {\r\n        p {\r\n            color: #acacac;\r\n        }\r\n    }\r\n    .social-share {\r\n        a {\r\n            color: #acacac;\r\n            &:hover {\r\n                color: var(--color-white);\r\n            }\r\n        }\r\n    }\r\n    .axil-footer-widget {\r\n        .widget-title {\r\n            color: #c8c8c8;\r\n        }\r\n        .inner {\r\n            ul {\r\n                li {\r\n                    a {\r\n                        color: #acacac;\r\n                        &:after {\r\n                            background-color: #acacac;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n    .copyright-default {\r\n        &.separator-top {\r\n            &:after {\r\n                background-color: #454545;\r\n            }\r\n        }\r\n        .quick-link {\r\n            li {\r\n                color: #acacac;\r\n                &:after {\r\n                    background: #acacac;\r\n                }\r\n                a {\r\n                    color: #acacac;\r\n                    &:after {\r\n                        background-color: #acacac;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        .copyright-right {\r\n            span.card-text {\r\n                color: #acacac;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n\r\n// Footer Style 2\r\n.footer-style-2 {\r\n    .footer-top {\r\n        padding: 50px 0 0;\r\n        &.separator-top {\r\n            &:after {\r\n                background-color: #F6F7FB;\r\n                height: 2px;\r\n            }\r\n        }\r\n    }\r\n    .axil-footer-widget {\r\n        .widget-title {\r\n            font-weight: 600;\r\n        }\r\n        .inner {\r\n            .download-btn-group {\r\n                display: flex;\r\n                align-items: center;\r\n                margin-top: 15px;\r\n                .qr-code {\r\n                    margin-right: 20px;\r\n                    img {\r\n                        @media #{$lg-layout} {\r\n                            height: 80px;\r\n                        }\r\n                    }\r\n                }\r\n                .app-link {\r\n                    flex: 1;\r\n                    a {\r\n                        margin-bottom: 15px;\r\n                        display: block;\r\n                        &:last-child {\r\n                            margin-bottom: 0;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n    .copyright-default {\r\n        .quick-link {\r\n            li {\r\n                &:last-child {\r\n                    &:after {\r\n                        display: none;\r\n                    }\r\n                }\r\n            }\r\n            &.payment-icons-bottom {\r\n                li {\r\n                    padding: 10px;\r\n                    img {\r\n                       height: 20px;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        .social-share {\r\n            @media #{$smlg-device} {\r\n                justify-content: center;\r\n                margin-top: 0;\r\n                margin-bottom: 0;\r\n            }\r\n            a {\r\n                font-size: 16px;\r\n                color: var(--color-body);\r\n                line-height: normal;\r\n                &:after {\r\n                    height: 35px;\r\n                    width: 35px;\r\n                }\r\n                &:hover {\r\n                    color: var(--color-white);\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n// Footer Style 3\r\n.footer-style-3 {\r\n    .footer-top {\r\n        padding: 35px 0 0 0;\r\n    }\r\n    .footer-widget-warp {\r\n        border-bottom: 1px solid rgba(119, 119, 119, .4);\r\n        padding-top: 30px;\r\n        &:last-child {\r\n            padding-bottom: 30px;\r\n        }\r\n    }\r\n    .footer-middle {\r\n        padding: 28px 0;\r\n    }\r\n    .payment-method {\r\n        display: flex;\r\n        align-items: center;\r\n        .title {\r\n            color: var(--color-white);\r\n            margin-bottom: 0;\r\n            padding-right: 24px;\r\n            text-align: right;\r\n            font-size: 14px;\r\n            line-height: 1.5;\r\n            min-width: 90px;\r\n        }\r\n        ul {\r\n            border-left: 1px solid rgba(119, 119, 119, .4);\r\n            margin-bottom: 0;\r\n            padding-left: 18px;\r\n            list-style: none;\r\n            margin: -6px;\r\n            li {\r\n                text-align: center;\r\n                display: inline-block;\r\n                height: 40px;\r\n                width: 40px;\r\n                line-height: 40px;\r\n                border-radius: 50%;\r\n                background-color: var(--color-white);\r\n                margin: 6px;\r\n                img {\r\n                    max-width: 28px;\r\n                }\r\n            }\r\n        }\r\n    }\r\n    .footer-social-link {\r\n        @media (max-width: 991px) {\r\n            margin-top: 20px;\r\n        }\r\n        ul {\r\n            list-style: none;\r\n            border-left-color: rgba(51, 120, 240, .4);\r\n            li {\r\n                background-color: transparent;\r\n                a {\r\n                    height: 40px;\r\n                    width: 40px;\r\n                    line-height: 40px;\r\n                    background-color: var(--color-primary);\r\n                    border-radius: 50%;\r\n                    display: block;\r\n                    text-align: center;\r\n                    font-size: 18px;\r\n                    color: var(--color-white);\r\n                    &:hover {\r\n                        background-color: var(--color-secondary);\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n    .copyright-area {\r\n        border-top: 1px solid rgba(119, 119, 119, .1);\r\n        .quick-link {\r\n            li {\r\n                a {\r\n                    font-weight: 400;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.axil-footer-widget {\r\n    &.widget-flex {\r\n        display: flex;\r\n        margin-bottom: 30px;\r\n        .widget-title {\r\n            border-right: 1px solid rgba(119, 119, 119, .4);\r\n            padding-right: 22px;\r\n            margin-right: 22px;\r\n            font-size: 14px;\r\n            margin-bottom: 0;\r\n            min-width: 90px;\r\n            font-weight: 700;\r\n            text-align: right;\r\n        }\r\n        .inner {\r\n            flex: 1;\r\n            ul {\r\n                margin-bottom: 0;\r\n                li {\r\n                    margin: 10px 0;\r\n                    &:first-child {\r\n                        margin-top: 0;\r\n                    }\r\n                    &:last-child {\r\n                        margin-bottom: 0;\r\n                    }\r\n                    a {\r\n                        font-size: 14px;\r\n                        color: #D6D6D6;\r\n                        font-family: var(--font-secondary);\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n    &.footer-widget-newsletter {\r\n        padding-right: 50px;\r\n        .input-group {\r\n            input {\r\n                background-color: #49495F;\r\n                height: 46px;\r\n                border-radius: 8px 0 0 8px;\r\n                padding: 0 20px;\r\n                border: none;\r\n                color: #D6D6D6;\r\n                &:focus {\r\n                    background-color: #49495F;\r\n                    box-shadow: none;\r\n                    color: #D6D6D6;\r\n                }\r\n                &::placeholder {\r\n                    color: #D6D6D6;\r\n                    /* Firefox */\r\n                    opacity: 1;\r\n                }\r\n                &:-ms-input-placeholder {\r\n                    /* Internet Explorer 10-11 */\r\n                    color: #D6D6D6;\r\n                }\r\n                &::-ms-input-placeholder {\r\n                    /* Microsoft Edge */\r\n                    color: #D6D6D6;\r\n                }\r\n            }\r\n            button {\r\n                width: auto;\r\n                background-color: var(--color-primary);\r\n                font-size: 14px;\r\n                font-weight: 700;\r\n                border-radius: 0 8px 8px 0;\r\n                color: var(--color-white);\r\n                padding: 0 24px;\r\n                &:hover {\r\n                    background-color: var(--color-secondary);\r\n                }\r\n            }\r\n        }\r\n        .widget-title {\r\n            color: var(--color-white);\r\n            font-size: 24px;\r\n            margin-bottom: 8px;\r\n        }\r\n        p {\r\n            color: #D6D6D6;\r\n            font-size: 14px;\r\n            margin-bottom: 16px;\r\n            span {\r\n                color: #FE497C;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n\r\n/*-----------------------\r\n   Offer Popup Modal Area  \r\n------------------------*/\r\n.offer-popup-modal {\r\n    position: fixed;\r\n    top: 50%;\r\n    left: 50%;\r\n    transform: translateY(-50%) translateX(-50%) scale(.8);\r\n    z-index: 101;\r\n    visibility: hidden;\r\n    opacity: 0;\r\n    transition: 0.3s;\r\n    .offer-popup-wrap {\r\n        background-color: var(--color-white);\r\n        border-radius: 6px;\r\n        padding: 50px;\r\n        width: 730px;\r\n        height: 450px;\r\n        display: flex;\r\n        flex-direction: column;\r\n        overflow: auto;\r\n        background-image: url('../../assets/images/others/popup-bg.png');\r\n        background-repeat: no-repeat;\r\n        background-position: center right;\r\n        @media only screen and (max-width: 767px) {\r\n            width: 100%;   \r\n            padding: 40px 30px;\r\n            max-height: 400px;\r\n            background-image: none;\r\n        }\r\n        @media #{$small-mobile} {\r\n            padding: 30px 20px;\r\n            max-height: 370px;\r\n        }\r\n        .popup-close {\r\n            height: 40px;\r\n            width: 40px;\r\n            font-size: 18px;\r\n            color: var(--color-white);\r\n            background-color: var(--color-primary);\r\n            border-radius: 50%;\r\n            position: absolute;\r\n            top: -30px;\r\n            right: -30px;\r\n            &:hover {\r\n                background-color: var(--color-secondary);\r\n            }\r\n            @media only screen and (max-width: 767px){\r\n                height: 30px;\r\n                width: 30px;\r\n                font-size: 12px;     \r\n                top: -30px;\r\n                right: -15px;\r\n            }\r\n            @media #{$small-mobile} {\r\n                top: -20px;\r\n                right: -10px;\r\n            }\r\n        }\r\n        .card-body {\r\n            position: relative;\r\n            padding: 0;\r\n            display: flex;\r\n            align-items: center;\r\n            .section-title-wrapper {\r\n                margin-bottom: 0;\r\n                @media only screen and (max-width: 767px) {\r\n                    padding-right: 0;\r\n                }\r\n                .title {\r\n                    font-size: 40px;\r\n                    line-height: 1.2;\r\n                    @media only screen and (max-width: 767px) {\r\n                        font-size: 28px;\r\n                    }\r\n                }\r\n            }\r\n            .countdown {\r\n                margin-bottom: 38px;\r\n                .countdown-section {\r\n                    background-color: var(--color-lighter);\r\n                }\r\n                \r\n            }\r\n            .axil-btn {\r\n                i {\r\n                    margin-right: 0;\r\n                    margin-left: 8px;\r\n                    position: relative;\r\n                    top: 1px;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n.offer-popup-modal.open {\r\n    visibility: visible;\r\n    opacity: 1;\r\n    transform: translate(-50%, -50%) scale(1);\r\n    transition: all .3s cubic-bezier(0.29, 1.39, 0.86, 1.15);\r\n}", "/*----------------------\r\nSpacing\r\n-----------------------*/\r\n\r\n.slick-dotted.slick-slider {\r\n    margin-bottom: 0;\r\n}\r\n\r\n.axil-section-gap {\r\n    padding: 80px 0;\r\n    @media #{$sm-layout} {\r\n        padding: 60px 0;\r\n    }\r\n}\r\n\r\n.axil-section-gapcommon {\r\n    padding: 80px 0 50px;\r\n    @media #{$sm-layout} {\r\n        padding: 60px 0 30px;\r\n    }\r\n}\r\n\r\n.section-gap-80-35 {\r\n    padding: 80px 0 35px;\r\n    @media #{$sm-layout} {\r\n        padding: 60px 0 15px;\r\n    }\r\n}\r\n\r\n.axil-section-gapBottom {\r\n    padding-bottom: 80px;\r\n    @media #{$md-layout} {\r\n        padding-bottom: 80px; \r\n    }\r\n    @media #{$sm-layout} {\r\n        padding-bottom: 60px ;\r\n    }\r\n}\r\n\r\n.pb--165,\r\n.pb--85 {\r\n    @media #{$md-layout} {\r\n        padding-bottom: 80px; \r\n    }\r\n    @media #{$sm-layout} {\r\n        padding-bottom: 60px ;\r\n    }\r\n}\r\n.axil-section-gapTop {\r\n    padding-top: 80px;\r\n    @media #{$md-layout} {\r\n        padding-top: 80px; \r\n    }\r\n    @media #{$sm-layout} {\r\n        padding-top: 60px ;\r\n    }\r\n}\r\n\r\n.axilil-service-area {\r\n    &.axil-section-gap {\r\n        &.layout-2  {\r\n            padding-bottom: 160px;\r\n            padding-top: 120px;\r\n        \r\n            @media #{$md-layout} {\r\n                padding-top: 80px; \r\n                padding-bottom: 80px;\r\n            }\r\n            @media #{$sm-layout} {\r\n                padding-top: 60px; \r\n                padding-bottom: 60px;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.pl--0 {\r\n    padding-left: 0 !important;\r\n}\r\n.pr--0 {\r\n    padding-right: 0 !important;\r\n}\r\n.pt--0 {\r\n    padding-top: 0 !important;\r\n}\r\n.pb--0 {\r\n    padding-bottom: 0 !important;\r\n}\r\n.mr--0 {\r\n    margin-right: 0 !important;\r\n}\r\n.ml--0 {\r\n    margin-left: 0 !important;\r\n}\r\n.mt--0 {\r\n    margin-top: 0 !important;\r\n}\r\n.mb--0 {\r\n    margin-bottom: 0 !important;\r\n}\r\n\r\n.ptb---100{\r\n    padding: 100px 0;\r\n    @media #{$md-layout} {\r\n        padding: 80px 0;\r\n    }\r\n    @media #{$sm-layout} {\r\n        padding: 60px 0;\r\n    }\r\n}\r\n\r\n@for $i from 1 through 40 {\r\n    .ptb--#{5 * $i} { padding: 5px *$i 0; }\r\n    .plr--#{5 * $i} { padding: 0 5px *$i; }\r\n    .pt--#{5 * $i} { padding-top: 5px *$i; }\r\n    .pb--#{5 * $i} { padding-bottom: 5px *$i; }\r\n    .pl--#{5 * $i} { padding-left: 5px *$i;}\r\n    .pr--#{5 * $i} { padding-right: 5px *$i;}\r\n    .mt--#{5 * $i} {margin-top: 5px *$i;}\r\n    .mb--#{5 * $i} {margin-bottom: 5px *$i;}\r\n    .mr--#{5 * $i} {margin-right: 5px *$i;}\r\n    .ml--#{5 * $i} {margin-left: 5px *$i;}\r\n}\r\n\r\n@media only screen and (min-width: 1350px) {\r\n    .ml--xxl-0 {\r\n        margin-left: 0;\r\n    }   \r\n}\r\n\r\n@media #{$laptop-device} {\r\n    @for $i from 1 through 20 {\r\n        .ptb_lp--#{5 * $i} {\r\n            padding: 5px *$i 0;\r\n        }\r\n\r\n        .plr_lp--#{5 * $i} {\r\n            padding: 0 5px *$i;\r\n        }\r\n\r\n        .pt_lp--#{5 * $i} {\r\n            padding-top: 5px *$i;\r\n        }\r\n\r\n        .pb_lp--#{5 * $i} {\r\n            padding-bottom: 5px *$i;\r\n        }\r\n\r\n        .pl_lp--#{5 * $i} {\r\n            padding-left: 5px *$i;\r\n        }\r\n\r\n        .pr_lp--#{5 * $i} {\r\n            padding-right: 5px *$i;\r\n        }\r\n\r\n        .mt_lp--#{5 * $i} {\r\n            margin-top: 5px *$i;\r\n        }\r\n\r\n        .mb_lp--#{5 * $i} {\r\n            margin-bottom: 5px *$i;\r\n        }\r\n    }\r\n}\r\n\r\n\r\n@media #{$lg-layout} {\r\n    @for $i from 1 through 20 {\r\n        .ptb_lg--#{5 * $i} {\r\n            padding: 5px *$i 0;\r\n        }\r\n        .plr_lg--#{5 * $i} {\r\n            padding: 0 5px *$i;\r\n        }\r\n        .pt_lg--#{5 * $i} {\r\n            padding-top: 5px *$i;\r\n        }\r\n        .pb_lg--#{5 * $i} {\r\n            padding-bottom: 5px *$i;\r\n        }\r\n\r\n        .pl_lg--#{5 * $i} {\r\n            padding-left: 5px *$i;\r\n        }\r\n\r\n        .pr_lg--#{5 * $i} {\r\n            padding-right: 5px *$i;\r\n        }\r\n\r\n        .mt_lg--#{5 * $i} {\r\n            margin-top: 5px *$i;\r\n        }\r\n\r\n        .mb_lg--#{5 * $i} {\r\n            margin-bottom: 5px *$i;\r\n        }\r\n        .ml_lg--#{5 * $i} {\r\n            margin-left: 5px *$i;\r\n        }\r\n\r\n    }\r\n}\r\n\r\n@media #{$md-layout} {\r\n\r\n    .ptb_md--0{\r\n        padding: 0 !important;\r\n    }\r\n    .pl_md--0 {\r\n        padding-left: 0 !important;\r\n    }\r\n    .pr_md--0 {\r\n        padding-right: 0 !important;\r\n    }\r\n    .pt_md--0 {\r\n        padding-top: 0 !important;\r\n    }\r\n    .pb_md--0 {\r\n        padding-bottom: 0 !important;\r\n    }\r\n    .mr_md--0 {\r\n        margin-right: 0 !important;\r\n    }\r\n    .ml_md--0 {\r\n        margin-left: 0 !important;\r\n    }\r\n    .mt_md--0 {\r\n        margin-top: 0 !important;\r\n    }\r\n    .mb_md--0 {\r\n        margin-bottom: 0 !important;\r\n    }\r\n    .ptb_md--250{\r\n        padding: 250px 0 !important;\r\n    }\r\n    \r\n    @for $i from 1 through 20 {\r\n        .ptb_md--#{5 * $i} {\r\n            padding: 5px *$i 0;\r\n        }\r\n\r\n        .plr_md--#{5 * $i} {\r\n            padding: 0 5px *$i;\r\n        }\r\n\r\n        .pt_md--#{5 * $i} {\r\n            padding-top: 5px *$i;\r\n        }\r\n\r\n        .pb_md--#{5 * $i} {\r\n            padding-bottom: 5px *$i;\r\n        }\r\n\r\n        .pl_md--#{5 * $i} {\r\n            padding-left: 5px *$i;\r\n        }\r\n\r\n        .pr_md--#{5 * $i} {\r\n            padding-right: 5px *$i;\r\n        }\r\n\r\n        .mt_md--#{5 * $i} {\r\n            margin-top: 5px *$i;\r\n        }\r\n\r\n        .mb_md--#{5 * $i} {\r\n            margin-bottom: 5px *$i;\r\n        }\r\n        \r\n    }\r\n}\r\n\r\n@media #{$sm-layout} {\r\n    .ptb_sm--250{\r\n        padding: 250px 0 !important;\r\n    }\r\n    .ptb_sm--0{\r\n        padding: 0 !important;\r\n    }\r\n    .pl_sm--0 {\r\n        padding-left: 0 !important;\r\n    }\r\n    .pr_sm--0 {\r\n        padding-right: 0 !important;\r\n    }\r\n    .pt_sm--0 {\r\n        padding-top: 0 !important;\r\n    }\r\n    .pb_sm--0 {\r\n        padding-bottom: 0 !important;\r\n    }\r\n    .mr_sm--0 {\r\n        margin-right: 0 !important;\r\n    }\r\n    .ml_sm--0 {\r\n        margin-left: 0 !important;\r\n    }\r\n    .mt_sm--0 {\r\n        margin-top: 0 !important;\r\n    }\r\n    .mb_sm--0 {\r\n        margin-bottom: 0 !important;\r\n    }\r\n    .pt_sm--150 {\r\n        padding-top: 150px !important;\r\n    }\r\n    .pb_sm--110 {\r\n        padding-bottom: 110px !important;\r\n    }\r\n    @for $i from 1 through 20 {\r\n        .ptb_sm--#{5 * $i} {\r\n            padding: 5px *$i 0;\r\n        }\r\n        .plr_sm--#{5 * $i} {\r\n            padding: 0 5px *$i;\r\n        }\r\n        .pt_sm--#{5 * $i} {\r\n            padding-top: 5px *$i;\r\n        }\r\n\r\n        .pb_sm--#{5 * $i} {\r\n            padding-bottom: 5px *$i;\r\n        }\r\n\r\n        .pl_sm--#{5 * $i} {\r\n            padding-left: 5px *$i;\r\n        }\r\n\r\n        .pr_sm--#{5 * $i} {\r\n            padding-right: 5px *$i;\r\n        }\r\n\r\n        .mt_sm--#{5 * $i} {\r\n            margin-top: 5px *$i;\r\n        }\r\n        \r\n        .ml_sm--#{5 * $i} {\r\n            margin-left: 5px *$i;\r\n        }\r\n\r\n        .mr_sm--#{5 * $i} {\r\n            margin-right: 5px *$i;\r\n        }\r\n\r\n        .mb_sm--#{5 * $i} {\r\n            margin-bottom: 5px *$i;\r\n        }\r\n    }\r\n\r\n    .pl_sm--0 {\r\n        padding-left: 0;\r\n    }\r\n    .pr_sm--0 {\r\n        padding-right: 0;\r\n    }\r\n    .pt_sm--0 {\r\n        padding-top: 0;\r\n    }\r\n    .pb_sm--0 {\r\n        padding-bottom: 0;\r\n    }\r\n    .mr_sm--0 {\r\n        margin-right: 0;\r\n    }\r\n    .ml_sm--0 {\r\n        margin-left: 0;\r\n    }\r\n    .mt_sm--0 {\r\n        margin-top: 0;\r\n    }\r\n    .mb_sm--0 {\r\n        margin-bottom: 0;\r\n    }\r\n    \r\n}\r\n\r\n\r\n@media #{$large-mobile}{\r\n    @for $i from 1 through 20 {\r\n        .ptb_mobile--#{5 * $i} {\r\n            padding: 5px *$i 0;\r\n        }\r\n        .plr_mobile--#{5 * $i} {\r\n            padding: 0 5px *$i;\r\n        }\r\n        .pt_mobile--#{5 * $i} {\r\n            padding-top: 5px *$i;\r\n        }\r\n        .pb_mobile--#{5 * $i} {\r\n            padding-bottom: 5px *$i;\r\n        }\r\n        .pl_mobile--#{5 * $i} {\r\n            padding-left: 5px *$i;\r\n        }\r\n        .pr_mobile--#{5 * $i} {\r\n            padding-right: 5px *$i;\r\n        }\r\n        .mt_mobile--#{5 * $i} {\r\n            margin-top: 5px *$i;\r\n        }\r\n        .mb_mobile--#{5 * $i} {\r\n            margin-bottom: 5px *$i;\r\n        }\r\n    }\r\n}\r\n\r\n"]}